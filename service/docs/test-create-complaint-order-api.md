# 测试创建客诉单API使用说明

## 接口信息

**接口路径**: `/test/dify-chat-analysis/test-create-complaint-order`  
**请求方法**: POST  
**Content-Type**: application/json

## 功能说明

该接口用于测试`handleAsyncExecuteOperation`中`tryCreateComplaintOrder`方法的逻辑，模拟AI客服自动创建客诉单的完整流程。

## 请求参数

```json
{
    "orderId": 12345,
    "beComplainIdentity": "商家",
    "complainDescription": "服务质量不满意",
    "complainReason": "服务态度差",
    "isChangePhone": false,
    "complainPhone": "13800138000",
    "contactName": "测试用户"
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| orderId | Long | 是 | - | 订单ID |
| beComplainIdentity | String | 否 | "商家" | 被投诉人身份，可选值：商家、劳动者 |
| complainDescription | String | 否 | - | 投诉描述 |
| complainReason | String | 否 | - | 投诉原因 |
| isChangePhone | Boolean | 否 | false | 是否更换电话号码 |
| complainPhone | String | 否 | - | 投诉人电话（当isChangePhone为true时使用） |
| contactName | String | 否 | "测试用户" | 联系人姓名 |

## 响应结果

### 成功响应

```json
{
    "success": true,
    "data": {
        "orderId": 12345,
        "testParams": "{\"orderId\":12345,\"beComplainIdentity\":\"商家\",\"complainDescription\":\"服务质量不满意\",\"complainReason\":\"服务态度差\",\"isChangePhone\":false}",
        "corpId": "ww5cfa32107e9a1f20",
        "botUserId": "testBotUser",
        "externalUserId": "testExternalUser",
        "contactName": "测试用户",
        "message": "测试执行完成，请查看日志了解详细执行结果"
    }
}
```

### 失败响应

```json
{
    "success": false,
    "msg": "测试失败：具体错误信息"
}
```

## 测试场景

### 1. 基本成功场景
```bash
curl -X POST "http://hyqywxbiz.58.com/test/dify-chat-analysis/test-create-complaint-order" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": 3762089389597721544,
    "beComplainIdentity": "商家",
    "complainDescription": "服务质量不满意",
    "complainReason": "服务态度差",
    "contactName": "张三"
  }'
```

### 2. 测试劳动者投诉
```bash
curl -X POST "http://hyqywxbiz.58.com/test/dify-chat-analysis/test-create-complaint-order" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": 67890,
    "beComplainIdentity": "劳动者",
    "complainDescription": "阿姨服务不专业",
    "complainReason": "技能不熟练",
    "contactName": "李四"
  }'
```

### 3. 测试自定义电话号码
```bash
curl -X POST "http://hyqywxbiz.58.com/test/dify-chat-analysis/test-create-complaint-order" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": 3762153734762136347,
    "beComplainIdentity": "商家",
    "complainDescription": "服务时间不准时",
    "complainReason": "迟到30分钟",
    "isChangePhone": true,
    "complainPhone": "13900139000",
    "contactName": "王五"
  }'
```

## 注意事项

1. **环境限制**: 该接口仅在非生产环境可用，生产环境会返回错误
2. **日志查看**: 测试执行的详细结果需要查看应用日志
3. **依赖服务**: 测试会调用真实的订单查询服务和客诉服务
4. **反射调用**: 接口通过反射调用私有方法，如果方法签名变更可能导致调用失败

## 日志关键字

在日志中搜索以下关键字可以查看测试执行详情：

- `开始测试创建客诉单`
- `开始创建客诉单`
- `创建客诉单成功`
- `创建客诉单失败`
- `客诉单创建后消息发送成功`

## 相关代码

- 测试Controller: `DifyChatAnalysisTestController.testCreateComplaintOrder`
- 被测试方法: `LbgAiReplyComponent.handleAsyncExecuteOperation`
- 核心逻辑: `LbgAiReplyComponent.tryCreateComplaintOrder`
