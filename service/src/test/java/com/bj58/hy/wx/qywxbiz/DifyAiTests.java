package com.bj58.hy.wx.qywxbiz;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;

/**
 * Description:
 *
 * <AUTHOR>
 */
@SpringBootTest
public class DifyAiTests {

    @Autowired
    private DifyRemoteService difyRemoteService;

    @Test
    public void workflow() throws Exception {

        WorkflowRequest request = new WorkflowRequest();

        final String corpId = "ww5cfa32107e9a1f20";
        final String corpUserId = "WangYanRui";
        final String externalUserId = "wmOrVVCwAA_Sb7Ye5kvQIWRnwPc-S9xg";

        String user = String.format("%s:%s:%s", corpId, corpUserId, externalUserId);
        request.setUser(user);

        request.setInputs(new HashMap<>());

        request.getInputs().put("corpId", corpId);
        request.getInputs().put("userId", corpUserId);
        request.getInputs().put("externalUserId", externalUserId);

        Result<WorkflowResponse> result1 = difyRemoteService.workflow(
                "app-n8jwSPTt9uufo1639RZ95YCR",
                request);

        System.out.println();
    }

    @Test
    public void chatflow() throws Exception {

        ChatflowRequest request = new ChatflowRequest();

        final String corpId = "ww5cfa32107e9a1f20";
        final String corpUserId = "WangYanRui";
        final String externalUserId = "wmOrVVCwAA_Sb7Ye5kvQIWRnwPc-S9xg";

        String user = String.format("%s:%s:%s", corpId, corpUserId, externalUserId);
        request.setUser(user);

        request.setConversationId("");
        request.setQuestion("我想下单个日常保洁");

        request.setInputs(new HashMap<>());
        request.getInputs().put("corpId", corpId);
        request.getInputs().put("userId", corpUserId);
        request.getInputs().put("externalUserId", externalUserId);

        Result<ChatflowResponse> aiResult = difyRemoteService.chatflow(
                "app-qtf6aSZ0IFsXkKcoMQlbOdP7",
                request);

        if (ObjectUtils.isNull(aiResult) || aiResult.isFailed() || ObjectUtils.isNull(aiResult.getData())) {
            System.out.println();
        }
        System.out.println();
    }
}
