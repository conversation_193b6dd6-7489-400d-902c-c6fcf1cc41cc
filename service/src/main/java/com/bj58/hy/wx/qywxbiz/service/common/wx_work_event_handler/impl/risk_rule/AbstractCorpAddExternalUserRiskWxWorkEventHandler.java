package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.impl.risk_rule;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IExternalContactService;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.AddExternalContactEventBo;
import com.bj58.hy.wx.qywxbiz.service.bo.RateLimiterWrapper;
import com.bj58.hy.wx.qywxbiz.service.common.risk_rule.RiskAlarmComponent;
import com.bj58.hy.wx.qywxbiz.service.common.risk_rule.RiskRuleComponent;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractAddExternalContactEventHandler;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import org.redisson.api.*;
import org.redisson.client.codec.LongCodec;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * Description: 企业成员 添加 外部联系人的风控规则校验
 *
 * <AUTHOR>
 */
public abstract class AbstractCorpAddExternalUserRiskWxWorkEventHandler extends AbstractAddExternalContactEventHandler {


    @SCFClient(lookup = IExternalContactService.SCF_URL)
    protected IExternalContactService externalContactService;

    @Autowired
    protected RiskRuleComponent riskRuleComponent;

    @Autowired
    protected RiskAlarmComponent riskAlarmComponent;


    protected Long getCorpPassiveAddThreshold(final @NonNull AddExternalContactEventBo event) {
        RBucket<Long> thresholdBucket = redisson.getBucket(
                String.format("CorpPassiveAddThreshold:%s", event.getCorpId()),
                LongCodec.INSTANCE
        );

        if (thresholdBucket.isExists()) {
            return thresholdBucket.get();
        }

        thresholdBucket = redisson.getBucket(
                String.format("CorpPassiveAddThreshold:%s", "default"),
                LongCodec.INSTANCE
        );
        return thresholdBucket.get();
    }

    protected Long getCorpProactivelyAddThreshold(final @NonNull AddExternalContactEventBo event) {
        RBucket<Long> thresholdBucket = redisson.getBucket(
                String.format("CorpProactivelyAddThreshold:%s", event.getCorpId()),
                LongCodec.INSTANCE
        );

        if (thresholdBucket.isExists()) {
            return thresholdBucket.get();
        }

        thresholdBucket = redisson.getBucket(
                String.format("CorpProactivelyAddThreshold:%s", "default"),
                LongCodec.INSTANCE
        );
        return thresholdBucket.get();
    }

    protected Long getCorpUserPassiveAddThreshold(final @NonNull AddExternalContactEventBo event) {
        RBucket<Long> thresholdBucket = redisson.getBucket(
                String.format("CorpUserPassiveAddThreshold:%s:%s", event.getCorpId(), event.getUserId()),
                LongCodec.INSTANCE
        );

        if (thresholdBucket.isExists()) {
            return thresholdBucket.get();
        }

        thresholdBucket = redisson.getBucket(
                String.format("CorpUserPassiveAddThreshold:%s", "default"),
                LongCodec.INSTANCE
        );
        return thresholdBucket.get();
    }

    protected Long getCorpUserProactivelyAddThreshold(final @NonNull AddExternalContactEventBo event) {
        RBucket<Long> thresholdBucket = redisson.getBucket(
                String.format("CorpUserProactivelyAddThreshold:%s:%s", event.getCorpId(), event.getUserId()),
                LongCodec.INSTANCE
        );

        if (thresholdBucket.isExists()) {
            return thresholdBucket.get();
        }

        thresholdBucket = redisson.getBucket(
                String.format("CorpUserProactivelyAddThreshold:%s", "default"),
                LongCodec.INSTANCE
        );
        return thresholdBucket.get();
    }

    protected RateLimiterWrapper getCorpAddRateLimiter(final @NonNull AddExternalContactEventBo event) {
        RAtomicLong rateValue = redisson.getAtomicLong(
                // CorpAddExternalUserFreqRate:ww5cfa32107e9a1f20
                String.format("CorpAddExternalUserFreqRate:%s", event.getCorpId())
        );
        if (!rateValue.isExists()) {
            rateValue = redisson.getAtomicLong(
                    String.format("CorpAddExternalUserFreqRate:%s", "default")
            );
        }

        RAtomicLong rateIntervalValue = redisson.getAtomicLong(
                // CorpAddExternalUserFreqRateInterval:ww5cfa32107e9a1f20
                String.format("CorpAddExternalUserFreqRateInterval:%s", event.getCorpId())
        );
        if (!rateIntervalValue.isExists()) {
            rateIntervalValue = redisson.getAtomicLong(
                    String.format("CorpAddExternalUserFreqRateInterval:%s", "default")
            );
        }

        RBucket<String> rateIntervalUnitValue = redisson.getBucket(
                // CorpAddExternalUserFreqRateIntervalUnit:ww5cfa32107e9a1f20
                String.format("CorpAddExternalUserFreqRateIntervalUnit:%s", event.getCorpId()), StringCodec.INSTANCE
        );
        if (!rateIntervalUnitValue.isExists()) {
            rateIntervalUnitValue = redisson.getBucket(
                    String.format("CorpAddExternalUserFreqRateIntervalUnit:%s", "default"), StringCodec.INSTANCE
            );
        }

        long rate = 10;
        if (rateValue.isExists()) {
            rate = rateValue.get();
        }

        long rateInterval = 1;
        if (rateIntervalValue.isExists()) {
            rateInterval = rateIntervalValue.get();
        }

        RateIntervalUnit rateIntervalUnit = RateIntervalUnit.SECONDS;
        if (rateIntervalUnitValue.isExists()) {
            RateIntervalUnit v = RateIntervalUnit.valueOf(rateIntervalUnitValue.get());
            if (ObjectUtils.notNull(v)) {
                rateIntervalUnit = v;
            }
        }

        // 判断当前企业 是否超过了 添加的频率限制
        RRateLimiter rateLimiter = redisson.getRateLimiter(
                String.format("CorpAddExternalUserFreqRateLimiter:%s", event.getCorpId())
        );

        if (!rateLimiter.isExists()) {
            log.info("[{}]当前企业添加客户的频率风控规则暂未初始化, rate = {}, rate interval = {}, rate interval unit = {}",
                    event.getCorpId(),
                    rate, rateInterval, rateIntervalUnit.name());
            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        RateLimiterConfig config = rateLimiter.getConfig();
        long oldRate = config.getRate();
        long oldRateInterval = config.getRateInterval();

        long newRateInterval = rateIntervalUnit.toMillis(rateInterval);

        if (!Objects.equals(oldRate, rate) ||
                !Objects.equals(oldRateInterval, newRateInterval)) {

            log.info("[{}]当前企业添加客户频率的设置发生变动, limiter = {}, old rate = {}, old rate interval = {}, new rate = {}, new rate interval = {}",
                    event.getCorpId(), rateLimiter.getName(),
                    oldRate, oldRateInterval, rate, newRateInterval);

            redisson.getKeys().delete( // 删除key，这里的删除操作极端情况下可能会导致报错，影响不大暂时忽略
                    rateLimiter.getName(),
                    String.format("{%s}:permits", rateLimiter.getName()),
                    String.format("{%s}:value", rateLimiter.getName())
            );

            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        return new RateLimiterWrapper(rateLimiter, rate, rateInterval, rateIntervalUnit);
    }

    protected RateLimiterWrapper getCorpUserAddRateLimiter(final @NonNull AddExternalContactEventBo event) {
        RAtomicLong rateValue = redisson.getAtomicLong(
                // CorpUserAddExternalUserFreqRate:ww5cfa32107e9a1f20:WangYanRui
                String.format("CorpUserAddExternalUserFreqRate:%s:%s", event.getCorpId(), event.getUserId())
        );
        if (!rateValue.isExists()) {
            rateValue = redisson.getAtomicLong(
                    String.format("CorpUserAddExternalUserFreqRate:%s", event.getCorpId())
            );

            if (!rateValue.isExists()) {
                rateValue = redisson.getAtomicLong(
                        String.format("CorpUserAddExternalUserFreqRate:%s", "default")
                );
            }
        }

        RAtomicLong rateIntervalValue = redisson.getAtomicLong(
                // CorpUserAddExternalUserFreqRateInterval:ww5cfa32107e9a1f20:WangYanRui
                String.format("CorpUserAddExternalUserFreqRateInterval:%s:%s", event.getCorpId(), event.getUserId())
        );
        if (!rateIntervalValue.isExists()) {
            rateIntervalValue = redisson.getAtomicLong(
                    String.format("CorpUserAddExternalUserFreqRateInterval:%s", event.getCorpId())
            );

            if (!rateIntervalValue.isExists()) {
                rateIntervalValue = redisson.getAtomicLong(
                        String.format("CorpUserAddExternalUserFreqRateInterval:%s", "default")
                );
            }
        }

        RBucket<String> rateIntervalUnitValue = redisson.getBucket(
                // CorpUserAddExternalUserFreqRateIntervalUnit:ww5cfa32107e9a1f20:WangYanRui
                String.format("CorpUserAddExternalUserFreqRateIntervalUnit:%s:%s", event.getCorpId(), event.getUserId()), StringCodec.INSTANCE
        );
        if (!rateIntervalUnitValue.isExists()) {
            rateIntervalUnitValue = redisson.getBucket(
                    String.format("CorpUserAddExternalUserFreqRateIntervalUnit:%s", event.getCorpId()), StringCodec.INSTANCE
            );

            if (!rateIntervalUnitValue.isExists()) {
                rateIntervalUnitValue = redisson.getBucket(
                        String.format("CorpUserAddExternalUserFreqRateIntervalUnit:%s", "default"), StringCodec.INSTANCE
                );
            }
        }

        long rate = 3;
        if (rateValue.isExists()) {
            rate = rateValue.get();
        }

        long rateInterval = 1;
        if (rateIntervalValue.isExists()) {
            rateInterval = rateIntervalValue.get();
        }

        RateIntervalUnit rateIntervalUnit = RateIntervalUnit.SECONDS;
        if (rateIntervalUnitValue.isExists()) {
            RateIntervalUnit v = RateIntervalUnit.valueOf(rateIntervalUnitValue.get());
            if (ObjectUtils.notNull(v)) {
                rateIntervalUnit = v;
            }
        }

        // 判断当前企业 是否超过了 添加的频率限制
        RRateLimiter rateLimiter = redisson.getRateLimiter(
                String.format("CorpUserAddExternalUserFreqRateLimiter:%s:%s", event.getCorpId(), event.getUserId())
        );

        if (!rateLimiter.isExists()) {
            log.info("[{},{}]当前企业成员添加客户的频率风控规则暂未初始化, rate = {}, rate interval = {}, rate interval unit = {}",
                    event.getCorpId(), event.getUserId(),
                    rate, rateInterval, rateIntervalUnit.name());
            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        RateLimiterConfig config = rateLimiter.getConfig();
        long oldRate = config.getRate();
        long oldRateInterval = config.getRateInterval();

        long newRateInterval = rateIntervalUnit.toMillis(rateInterval);

        if (!Objects.equals(oldRate, rate) ||
                !Objects.equals(oldRateInterval, newRateInterval)) {

            log.info("[{},{}]当前企业成员添加客户的频率风控规则发生变动, limiter = {}, old rate = {}, old rate interval = {}, new rate = {}, new rate interval = {}",
                    event.getCorpId(), event.getUserId(), rateLimiter.getName(),
                    oldRate, oldRateInterval, rate, newRateInterval);

            redisson.getKeys().delete( // 删除key，这里的删除操作极端情况下可能会导致报错，影响不大暂时忽略
                    rateLimiter.getName(),
                    String.format("{%s}:permits", rateLimiter.getName()),
                    String.format("{%s}:value", rateLimiter.getName())
            );

            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        return new RateLimiterWrapper(rateLimiter, rate, rateInterval, rateIntervalUnit);
    }
}
