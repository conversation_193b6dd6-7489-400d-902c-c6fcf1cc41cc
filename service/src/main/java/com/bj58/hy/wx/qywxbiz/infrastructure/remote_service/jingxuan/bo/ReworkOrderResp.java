package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo;

import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/31 14:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class ReworkOrderResp {


    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 服务开始时间
     */
    private String serviceStartTime;

    /**
     * 服务结束时间
     */
    private String serviceEndTime;

    /**
     * 服务完成时间
     */
    private String completeTime;

    /**
     * sku id
     */
    private Long serviceId;

    /**
     * sku名称
     */
    private String serviceName;

    private int forthCateId;

    private String forthCateName;

    private int cateId;

    private String cateName;
}
