package com.bj58.hy.wx.qywxbiz.service.common.group_chat;

import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.GetRecommendedOwnerIdReq;
import lombok.NonNull;
import org.jetbrains.annotations.Nullable;

/**
 * Description: 
 *
 * <AUTHOR>
 */
public abstract class AbstractGroupChatOwnerStrategy {

    protected org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(this.getClass());

    public abstract boolean matched(@NonNull final GetRecommendedOwnerIdReq req);

    @Nullable
    public abstract String getUserId(@NonNull final GetRecommendedOwnerIdReq req);

}
