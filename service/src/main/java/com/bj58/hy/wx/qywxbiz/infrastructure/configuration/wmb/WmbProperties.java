package com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Optional;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wmb")
public class WmbProperties {

    private String path;

    private Map<String, Client> clients;

    public Client.Publish getPublish(String key) {
        return Optional.ofNullable(this.getClients().get(key))
                .map(Client::getPublish)
                .orElse(null);
    }

    public Client.Subscribe getSubscribe(String key) {
        return Optional.ofNullable(this.getClients().get(key))
                .map(Client::getSubscribe)
                .orElse(null);
    }


    @Data
    public static class Client {

        private Publish publish;

        private Subscribe subscribe;

        @Data
        public static class Publish {
            private int subjectId;
        }

        @Data
        public static class Subscribe {
            private int subjectId;
            private int clientId;
            private boolean enabled;
        }

    }


}
