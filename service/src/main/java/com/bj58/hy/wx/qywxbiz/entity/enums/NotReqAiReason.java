package com.bj58.hy.wx.qywxbiz.entity.enums;

import com.bj58.hy.lib.core.util.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * Description: 没有请求AI的原因
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum NotReqAiReason {


    NOT_HAS_AI_AUTH(1, "客服账号没有AI权限"),

    NOT_FOUND_RELATIONSHIP(2, "未发现好友关系"),

    MISMATCH_STATE_VALUE(3, "渠道参数不符合条件"),

    CANCEL_HOSTING(4, "取消托管中..."),

    NOT_ONLINE_TIME(5, "非托管时间"),

    NOT_SUPPORTED_MESSAGE_TYPE(6, "不支持的消息类型"),


    MANUAL_REPLY_CANCEL_HOSTING(101, "人工客服回复"),
    AI_CAN_NOT_JOIN_CANCEL_HOSTING(102, "AI无法承接"),
    USER_FORCE_REQUEST_CANCEL_HOSTING(103, "用户要求转人工"),


    /**
     * 未知
     */
    UNKNOWN(-1, "unknown"),
    ;

    private final int code;
    private final String desc;

    public static NotReqAiReason of(Integer code) {
        if (ObjectUtils.isNull(code)) {
            return null;
        }


        for (NotReqAiReason source : NotReqAiReason.values()) {
            if (source.getCode() == code) {
                return source;
            }
        }

        return null;
    }

    public static NotReqAiReason strictOf(Integer code) {
        return Optional.ofNullable(of(code))
                .orElseThrow(() -> new UnsupportedOperationException(
                        String.format("unsupported enum, value = %s", code)));
    }
}
