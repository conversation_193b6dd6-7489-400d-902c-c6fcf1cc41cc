package com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.repository;

import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DifyChatResultRepository {

    private final RedissonClient redisson;

    public void save(@NonNull final String conversationId,
                     @NonNull final String reply,
                     @NonNull final List<String> targetReqMessageIds) {
        // 记录一下AI返回的结果，对应的是哪几条请求
        RMap<String, String> aiResultMap = redisson.getMap(
                String.format("DIFY_AI_RESULT:%s", conversationId), StringCodec.INSTANCE);
        aiResultMap.expire(1, TimeUnit.HOURS); // 1h有效期即可，回调很快就能收到

        aiResultMap.put(reply, JacksonUtils.format(targetReqMessageIds));
    }

    public List<String> get(@NonNull final String conversationId,
                            @NonNull final String reply) {
        // 记录一下AI返回的结果，对应的是哪几条请求
        RMap<String, String> aiResultMap = redisson.getMap(
                String.format("DIFY_AI_RESULT:%s", conversationId), StringCodec.INSTANCE);

        String val = aiResultMap.get(reply);
        if (ObjectUtils.isEmpty(val)) {
            return null;
        }

        return JacksonUtils.parse(val, new TypeReference<List<String>>() {
        });
    }

}
