package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.enums;

import com.bj58.hy.lib.core.util.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/1 14:30
 */
@Getter
@AllArgsConstructor
public enum ReWorkPushEnum {

    ORDER_CREATE_FAIL_MESSAGE_TO_USER(1,"%s客户%s订单的返工订单（订单号：%s）申请失败，请尽快跟进售后退款流程","您预约%s上门的服务订单（订单号：%s）派单失败，您看是否方便调整到其他时间服务呢？","返工订单生成失败"),
    SERVICE_FAIL_MESSAGE_TO_EXTERNALUSER(2,"%s客户%s订单的返工订单（订单号：%s）申请失败，请尽快跟进售后退款流程","您预约%s上门的服务订单（订单号：%s）派单失败，您看是否方便调整到其他时间服务呢？","履约过程中反工订单取消"),
    MODIFY_TIME_MESSAGE_TO_EXTERNALUSER(3,"","您的订单（订单号：%s）服务时间已更改为%s，有什么问题随时联系我～","商家修改服务时间推送PUSH"),
    BAD_MESSAGE_TO_EXTERNALUSER(4,"","您好，【%s】不满意重新服务的订单，您这边还有什么宝贵建议吗，或者有什么问题随时联系我","用户差评推送PUSH"),

    ;

    private final int scene;
    private final String messageToUser;
    private final String messageToExternalUserId;
    private final String desc;


    public static ReWorkPushEnum of(int scene) {
        if (ObjectUtils.isNull(scene)) {
            return null;
        }


        for (ReWorkPushEnum enums : ReWorkPushEnum.values()) {
            if (enums.getScene() == scene) {
                return enums;
            }
        }

        return null;
    }

    public static ReWorkPushEnum strictOf(int scene) {
        return Optional.ofNullable(of(scene))
                .orElseThrow(() -> new UnsupportedOperationException(
                        String.format("unsupported enum, value = %s", scene)));
    }
}
