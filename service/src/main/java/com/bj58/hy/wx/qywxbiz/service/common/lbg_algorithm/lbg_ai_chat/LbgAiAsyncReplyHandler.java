package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat;

import cn.hutool.core.thread.BlockPolicy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb.WmbProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.ApplicationUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework.AbstractWmbHandler;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiHumanNotJoinComp;
import com.bj58.spat.esbclient.*;
import com.bj58.spat.esbclient.config.DelayLevel;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class LbgAiAsyncReplyHandler {

    @Autowired
    protected WmbProperties wmbProperties;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5, 10,
            1, TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("lbg_ai_async_reply-%d").build(),
            new BlockPolicy(Runnable::run)
    );


    @PostConstruct
    protected void init() {
        try {
            Method initMethod = wmbHandler.getClass().getDeclaredMethod("init");
            initMethod.setAccessible(true);
            initMethod.invoke(wmbHandler);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 故意的没有构建成Spring的Bean
     */
    private final AbstractWmbHandler wmbHandler = new AbstractWmbHandler() {

        private ESBClient client;

        private WmbProperties.Client.Publish publish;

        @Override
        protected int getWmbSubjectId() {
            return publish.getSubjectId();
        }

        @Override
        protected ESBClient getWmbClient() {
            return this.client;
        }

        @Override
        protected String getWarningPrefix() {
            return "[lbg_ai_chat]";
        }

        @Override
        public void init() {
            WmbProperties properties = LbgAiAsyncReplyHandler.this.wmbProperties;
            publish = properties.getPublish("lbg_ai_chat");
            WmbProperties.Client.Subscribe subscribe = properties.getSubscribe("lbg_ai_chat");

            if (Objects.isNull(publish) && Objects.isNull(subscribe)) {
                log.info("No configuration item exists, {} wmb client init failure. ", getWarningPrefix());
                return;
            }

            try {
                client = new ESBClient(properties.getPath());
                log.info("{} esb client init success", getWarningPrefix());

                if (!subscribe.isEnabled()) {
                    log.info("{} esb subscribe function has not been activated", getWarningPrefix());
                    return;
                }

                client.setReceiveSubject(new ESBSubject(
                        subscribe.getSubjectId(), subscribe.getClientId(), SubMode.PULL
                ));
                client.setReceiveQmaxLen(32);
                client.setReceiveHandler(new ESBReceiveHandler() {
                    @Override
                    public void messageReceived(final ESBMessage esbMessage) {
                        handlerMessage(esbMessage);
                    }
                });
                client.startAsyncPull();
                log.info("{} esb receive handler init success", getWarningPrefix());
            } catch (Exception e) {
                log.error(String.format(getWarningPrefix() + " esb client init failed: %s", e.getMessage()), e);
            }
        }
    };

    public void commitAsyncAiReplyTask(@NonNull final String corpId,
                                       @NonNull final String botUserId,
                                       @NonNull final String externalUserId,
                                       @NonNull final String contactName) {

        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("corpId", corpId);
        bodyInfoObj.put("botUserId", botUserId);
        bodyInfoObj.put("externalUserId", externalUserId);
        bodyInfoObj.put("contactName", contactName);
        bodyInfoObj.put("type", "async_customer_ai_chat");

        if ("Product".equals(System.getenv("WCloud_Env"))) {
            wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), DelayLevel.DELAY_30S);

        } else {
            new Thread(() -> { // 沙箱MOCK数据，30S后执行
                try {
                    TimeUnit.SECONDS.sleep(30);
                } catch (InterruptedException ignored) {
                }
                // 沙箱直接执行
                ESBMessage message = new ESBMessage();
                message.setBody(JSON.toJSONString(bodyInfoObj).getBytes(StandardCharsets.UTF_8));
                handlerMessage(message);
            }).start();
        }
    }

    // public void commitAsyncAskChatFinish(@NonNull final String corpId,
    //                                             @NonNull final String botUserId,
    //                                             @NonNull final String externalUserId) {
    //
    //     Map<String, Object> bodyInfoObj = new HashMap<>();
    //     bodyInfoObj.put("corpId", corpId);
    //     bodyInfoObj.put("botUserId", botUserId);
    //     bodyInfoObj.put("externalUserId", externalUserId);
    //     bodyInfoObj.put("type", "async_customer_ai_chat_finish");
    //
    //     wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), DelayLevel.DELAY_5M);
    // }

    public void commitDelayCheckHumanNotJoinThenSendTip2ExternalUser(final @NonNull String corpId,
                                                                     final @NonNull String userId,
                                                                     final @NonNull String externalUserId) {
        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("corpId", corpId);
        bodyInfoObj.put("botUserId", userId);
        bodyInfoObj.put("externalUserId", externalUserId);
        bodyInfoObj.put("type", "human_not_join_then_send_tip_2_external_user");

        wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), 4 * 60);
    }

    public void commitDelayCheckHumanNotJoinThenForceTipUser(final @NonNull String corpId,
                                                             final @NonNull String userId,
                                                             final @NonNull String externalUserId,
                                                             final @NonNull String contactName) {

        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("corpId", corpId);
        bodyInfoObj.put("botUserId", userId);
        bodyInfoObj.put("externalUserId", externalUserId);
        bodyInfoObj.put("contactName", contactName);
        bodyInfoObj.put("type", "human_not_join_then_force_tip_user");

        wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), 3 * 60);
    }

    /**
     * 提交延迟检查任务
     */
    public void commitDelayCheckFeedbackTask(@NonNull final String corpId,
                                             @NonNull final String userId,
                                             @NonNull final String externalUserId,
                                             @NonNull final JuziSingleChatContentRecord singleChatContentRecord) {
        Date eventTimestamp = singleChatContentRecord.getCreateTime();

        // 是否上班时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(eventTimestamp);
        final int curHour = calendar.get(Calendar.HOUR_OF_DAY);

        // 09:00 - 20:00
        if (curHour >= 9 && curHour <= 19) {

            Map<String, Object> bodyInfoObj = new HashMap<>();
            bodyInfoObj.put("corpId", corpId);
            bodyInfoObj.put("botUserId", userId);
            bodyInfoObj.put("externalUserId", externalUserId);
            bodyInfoObj.put("timestamp", System.currentTimeMillis());
            bodyInfoObj.put("type", "check_feedback_recovery");

            wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), 30 * 60);
        }
    }

    private void handlerMessage(ESBMessage esbMessage) {
        String esbMsg = new String(esbMessage.getBody(), StandardCharsets.UTF_8);
        if (ObjectUtils.isEmpty(esbMsg)) {
            return;
        }

        Runnable runnable = () -> {
            log.info("receive lbg ai async ai chat job, body = {}", esbMsg);

            JSONObject esbMsgObj = JSON.parseObject(esbMsg);
            String corpId = esbMsgObj.getString("corpId");
            String botUserId = esbMsgObj.getString("botUserId");
            String externalUserId = esbMsgObj.getString("externalUserId");

            String type = esbMsgObj.getString("type");

            if (StringUtils.isBlank(botUserId) || StringUtils.isBlank(externalUserId) || StringUtils.isBlank(type)) {
                return;
            }

            LbgAiReplyComponent lbgAiReplyComponent =
                    ApplicationUtils.getBean(LbgAiReplyComponent.class);

            LbgAiHumanNotJoinComp humanNotJoinComp =
                    ApplicationUtils.getBean(LbgAiHumanNotJoinComp.class);

            try {
                if (StringUtils.equalsIgnoreCase(type, "async_customer_ai_chat")) {
                    String contactName = esbMsgObj.getString("contactName");
                    lbgAiReplyComponent.tryReply(corpId, botUserId, externalUserId, contactName);

                } else if (StringUtils.equalsIgnoreCase(type, "human_not_join_then_send_tip_2_external_user")) {

                    humanNotJoinComp.humanNotJoinThenSendTip2ExternalUser(corpId, botUserId, externalUserId);

                } else if (StringUtils.equalsIgnoreCase(type, "human_not_join_then_force_tip_user")) {
                    String contactName = esbMsgObj.getString("contactName");
                    humanNotJoinComp.humanNotJoinThenForceTipUser(corpId, botUserId, externalUserId, contactName);
                } else if (StringUtils.equalsIgnoreCase(type, "check_feedback_recovery")) {
                    Long timestamp = esbMsgObj.getLong("timestamp");
                    lbgAiReplyComponent.checkAndSendFeedbackRecovery(corpId, botUserId, externalUserId, timestamp);
                }

                log.info("lbg ai chat job process completion, corpId = {}, botUserId = {}, externalUserId = {}",
                        corpId, botUserId, externalUserId);

            } catch (Exception e) {

                log.error("lbg ai chat job process completion, corpId = {}, botUserId = {}, externalUserId = {}, " +
                        "ex msg = {}", corpId, botUserId, externalUserId, e.getMessage(), e);
            }

        };

        executor.execute(runnable);
    }
}
