package com.bj58.hy.wx.qywxbiz.interfaces.wmb.juzi;

import com.alibaba.fastjson.JSON;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb.WmbProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework.AbstractWmbHandler;
import com.bj58.hy.wx.qywxbiz.interfaces.scf.jiafu.GroupMemberInviteTransactionService;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.spat.esbclient.ESBClient;
import com.bj58.spat.esbclient.ESBMessage;
import com.bj58.spat.esbclient.ESBReceiveHandler;
import com.bj58.spat.esbclient.ESBSubject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

@Slf4j
@Component
public class GroupEventCallback extends AbstractWmbHandler {

    private ESBClient client;

    private WmbProperties.Client.Publish publish;

    @Autowired
    private GroupMemberInviteTransactionService groupMemberInviteTransactionService;

    @Override
    protected int getWmbSubjectId() {
        return publish.getSubjectId();
    }

    @Override
    protected ESBClient getWmbClient() {
        return this.client;
    }

    @Override
    protected String getWarningPrefix() {
        return "[group_event]";
    }

    @Override
    protected void init() {
        publish = wmbProperties.getPublish("group_event");

        WmbProperties.Client.Subscribe subscribe = wmbProperties.getSubscribe("group_event");

        if (Objects.isNull(subscribe)) {
            log.info("No configuration item exists, " + getWarningPrefix() + " wmb client init failure. ");
            return;
        }

        if (!subscribe.isEnabled()) {
            log.info(getWarningPrefix() + " esb subscribe function has not been activated");
            return;
        }

        try {
            client = new ESBClient(wmbProperties.getPath());
            client.setReceiveSubject(new ESBSubject(
                    subscribe.getSubjectId(), subscribe.getClientId()
            ));
            client.setReceiveHandler(new ESBReceiveHandler() {
                @Override
                public void messageReceived(final ESBMessage esbMessage) {
                    handler(esbMessage);
                }
            });

            log.info(getWarningPrefix() + " esb client init success");
        } catch (Exception e) {
            log.error(String.format(getWarningPrefix() + " esb client init failed: %s", e.getMessage()), e);
        }
    }


    public void handler(ESBMessage esbMessage) {
        String esbMsg = new String(esbMessage.getBody(), StandardCharsets.UTF_8);
        handler(esbMsg);
    }


    public void handler(String esbMsg) {
        log.info("receive group event callback, body = {}", esbMsg);
        if (ObjectUtils.isEmpty(esbMsg)) {
            return;
        }

        this.messageReceived(esbMsg);

    }

    public void messageReceived(String body) {

        EventMsg event = JSON.parseObject(body, EventMsg.class);

        if (!Objects.equals(event.getCorpId(), "ww5cfa32107e9a1f20") ||
                !Objects.equals(event.getBizCustomizedResultFilterKey(), BizSceneEnum.家服_阿姨拉群加企微.getSceneName())) {
            return;
        }

        // 处理群创建成功的回调
        if (Objects.nonNull(event.getChatId())) {
            try {
                groupMemberInviteTransactionService.updateCityGroupIdsByResultId(event.getResultId(), event.getChatId());

                log.info("更新城市群ID列表缓存成功: resultId={}, chatId={}", event.getResultId(), event.getChatId());
            } catch (Exception e) {
                log.error("更新城市群ID列表缓存失败: resultId={}, chatId={}, error={}",
                        event.getResultId(), event.getChatId(), e.getMessage(), e);
            }
        }
    }

    @Data
    public static class EventMsg {
        private String type;
        private String resultId;
        private String corpId;
        private String chatId;
        private String roomWxid;
        private String failureReason;
        private String externalUserId;
        private String userId;
        private String bizCustomizedResultFilterKey;
    }

}
