package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.strategy;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.support.pojo.PagedResult;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo.OrderQueryBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.CommonOrderQueryService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.bo.OrderInfoBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.AbstractQueryDataStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatIdRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.bo.LbgAiChatPartnersBo;
import com.bj58.lbg.business.plaform.oms.api.dto.query.SearchQuerySortField;
import lombok.NonNull;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:18
 */
@Component
public class QueryAiUserOrderStrategy extends AbstractQueryDataStrategy {


    @Autowired
    private CommonOrderQueryService commonOrderQueryService;

    @Autowired
    private ExternalContactRemoteService externalContactRemoteService;

    @Autowired
    private LbgAiChatIdRepository aiChatIdRepository;

    @Override
    public boolean matched(@NonNull Object biztypeObj) {

        if (StringUtils.equalsIgnoreCase(biztypeObj.toString(), "queryAiUserOrder")) {
            return true;
        }

        return false;
    }

    @Override
    public Result<Object> process(@NonNull Map<String, Object> queryParam) {

        Object chatIdObj = queryParam.get("chatId");
        // 通过chatId和四级类目id查询最近的用户订单
        Object cateIdObj = queryParam.get("cateId");
        if (ObjectUtils.isEmpty(cateIdObj)) {
            return Result.failure("cateId is empty");
        }
        String chatId = chatIdObj.toString();
        LbgAiChatPartnersBo chatIdRelation = aiChatIdRepository.getChatPartnersBo(chatId);
        if (ObjectUtils.isEmpty(chatIdRelation)) {
            return Result.failure("chatIdRelation is empty");
        }

        GetExternalContactRelationshipResp relationship = externalContactRemoteService.getRelationship(
                chatIdRelation.getCorpId(), chatIdRelation.getBotUserId(), chatIdRelation.getExternalUserId());

        if (ObjectUtils.isEmpty(relationship)) {
            return Result.failure("relationship is empty");
        }

        Long wbUserId = null;
        if (ObjectUtils.isNotEmpty(relationship.getStateMappedValue())) {
            JSONObject stateJsonObj = JSONObject.parseObject(relationship.getStateMappedValue());
            if (ObjectUtils.isNotEmpty(stateJsonObj)) {
                JSONObject bindWuBa = stateJsonObj.getJSONObject("_BindWuBa_");
                if (ObjectUtils.isNotEmpty(bindWuBa)) {
                    wbUserId = bindWuBa.getLong("id");
                }
            }
        }
        if (ObjectUtils.isEmpty(wbUserId)) {
            return Result.failure("wbUserId is empty");
        }

        Integer cateId = Integer.parseInt(cateIdObj.toString());

        OrderQueryBo orderQueryBo = new OrderQueryBo();
        orderQueryBo.setWbUserId(wbUserId);
        orderQueryBo.setCateId(cateId);
        orderQueryBo.setQuerySearchSortEntityList(Collections
                .singletonList(SearchQuerySortField
                        .builder()
                        .orderType("desc")
                        .sortField("create_time").build()));
        orderQueryBo.setPage(1);
        orderQueryBo.setSize(2);

        PagedResult<OrderInfoBo> orderInfoBoPagedResult = commonOrderQueryService.pageOrder(orderQueryBo);
        if (ObjectUtils.isEmpty(orderInfoBoPagedResult) || ObjectUtils.isEmpty(orderInfoBoPagedResult.getResults())) {
            return Result.success(null);
        }
        return Result.success(orderInfoBoPagedResult.getResults().stream().map(OrderInfoBo::getOrderId).collect(Collectors.toList()));
    }
}
