package com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.exception.ServiceException;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.bj58.hy.wx.qywxbiz.entity.JuziSingleChatContentRecordAiBizInfo;
import com.bj58.hy.wx.qywxbiz.entity.enums.AiType;
import com.bj58.hy.wx.qywxbiz.entity.enums.NotReqAiReason;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ChatContentRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.ChatflowRequest;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.ChatflowResponse;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.DifyRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.AbstractMeishiMessageHandler;
import com.bj58.hy.wx.qywxbiz.service.common.dify.DifyComponent;
import com.bj58.hy.wx.qywxbiz.service.common.dify.bo.DifyApiInfoBo;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.bo.DifyMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.comp.DifyChatSendMessageComp;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.comp.DifyChatflowAlarmComponent;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.constants.DifyChatConstants;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.repository.DifyChatConversationIdRepository;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.repository.DifyChatExternalUserRecentMessageRecordRepository;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.repository.DifyChatResultRepository;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DifyChatflowComponent {

    @Autowired
    private DifyChatflowAlarmComponent difyChatflowAlarmComponent;

    @Autowired
    private ExternalContactRemoteService externalContactRemoteService;

    @Autowired
    private DifyChatExternalUserRecentMessageRecordRepository externalUserRecentChatMessageRecordRepository;

    @Autowired
    private DifyChatConversationIdRepository aiChatIdRepository;

    @Autowired
    private DifyChatResultRepository aiResultRepository;

    @Autowired
    private ChatContentRemoteService chatContentRemoteService;

    @Autowired
    private DifyChatSendMessageComp sendMessageComp;

    @Autowired
    private DifyRemoteService difyRemoteService;

    @Autowired
    private DifyComponent difyComponent;

    @Autowired
    private ObjectProvider<AbstractMeishiMessageHandler> meishiMessageHandlers;

    @Autowired
    private RedissonClient redisson;


    public void tryReply(@NonNull final String corpId,
                         @NonNull final String corpUserId,
                         @NonNull final String externalUserId,
                         @NonNull final String contactName) {

        // 取出最近的聊天内容，并且从记录中删除
        List<DifyMessageChatRecordBo> reqAiMessages = externalUserRecentChatMessageRecordRepository
                .getRecentChatMessagesThenClear(corpId, corpUserId, externalUserId);
        if (ObjectUtils.isEmpty(reqAiMessages)) {
            return;
        }

        GetExternalContactRelationshipResp contactRelationshipResult = externalContactRemoteService.getRelationship(
                corpId, corpUserId, externalUserId);

        if (Objects.isNull(contactRelationshipResult)) {
            // 这里应该满足，但是可能存在消息聚合期间，好友关系删除的情况
            recordNotReqAiBizInfo(reqAiMessages, NotReqAiReason.NOT_FOUND_RELATIONSHIP);
            return;
        }

        @Nullable final String conversationId = aiChatIdRepository.getConversationId(
                corpId, corpUserId, externalUserId);

        // 调用AI客服
        Result<ChatflowResponse> aiResult = getAIResult(conversationId, corpId, corpUserId, externalUserId, reqAiMessages);

        recordSuccessReqAiBizInfo(reqAiMessages, aiResult);

        if (ObjectUtils.isNull(aiResult) || aiResult.isFailed() || ObjectUtils.isNull(aiResult.getData())) {

            replyIfAiBusy(corpId, corpUserId, externalUserId, contactName,
                    reqAiMessages, aiResult);

        } else {

            ChatflowResponse aiChatflowResponse = aiResult.getData();

            // 可能是第一次和AI交互，需要保存会话id
            aiChatIdRepository.saveConversationId(
                    corpId, corpUserId, externalUserId, aiChatflowResponse.getConversationId());

            if (ObjectUtils.isEmpty(aiChatflowResponse.getAnswer())) {
                log.error("ai result is empty, req messages = {}, ai result = {}",
                        JacksonUtils.format(reqAiMessages), JacksonUtils.format(aiResult));

                // replyIfAiBusy(corpId, corpUserId, externalUserId, contactName, reqAiMessages);

            } else {
                String answer = aiChatflowResponse.getAnswer();

                // 记录一下AI应答的文本结果，对应的是哪几条请求
                // 用于后续收到AI应答的文本会话内容回调时，存储对应的数据
                recordAiReplyTextMappedReqMessageIds(aiChatflowResponse.getConversationId(), answer, reqAiMessages);

                String tipUserMessage = String.format(DifyChatConstants.AI_TO_USER_MESSAGE, contactName);
                sendMessageComp.sendTextToExternalUser(corpId, corpUserId, externalUserId,
                        answer, tipUserMessage);
            }

        }
    }

    public Result<ChatflowResponse> getAIResult(@Nullable final String conversationId,
                                                @NonNull final String corpId,
                                                @NonNull final String corpUserId,
                                                @NonNull final String externalUserId,
                                                @NonNull final List<DifyMessageChatRecordBo> messageRecords) {
        @Nullable final DifyApiInfoBo.Item apiInfo = difyComponent.getChatflowApiInfo(corpId, corpUserId);
        if (ObjectUtils.isNull(apiInfo)) {
            return Result.failure("not found chatflow api info");
        }

        String question = messageRecords.stream()
                .map(DifyMessageChatRecordBo::getMessage)
                .collect(Collectors.joining("\n"));

        ChatflowRequest chatflowRequest = new ChatflowRequest();

        String user = String.format("%s:%s:%s", corpId, corpUserId, externalUserId);
        chatflowRequest.setUser(user);
        chatflowRequest.setConversationId(conversationId);
        chatflowRequest.setQuestion(question);

        chatflowRequest.getInputs().put("corpId", corpId);
        chatflowRequest.getInputs().put("userId", corpUserId);
        chatflowRequest.getInputs().put("externalUserId", externalUserId);
        chatflowRequest.getInputs().put("triggerType", "chat");

        try {
            log.info("request dify ai, req = {}", JacksonUtils.format(chatflowRequest));

            Result<ChatflowResponse> result = difyRemoteService.chatflow(
                    apiInfo.getApiKey(), chatflowRequest);

            log.info("request dify ai done, req = {}, resp = {}",
                    JacksonUtils.format(chatflowRequest), JacksonUtils.format(result));

            return result;
        } catch (ServiceException e) {
            log.error("service exception, chat request = {}", JacksonUtils.format(chatflowRequest), e);
            return Result.failure(e.getErrResult().getCode(), e.getErrResult().getMsg());

        } catch (Exception e) {
            log.error("unknown exception, chat request = {}", JacksonUtils.format(chatflowRequest), e);
            return Result.failure(e.getMessage());
        }
    }

    private void replyIfAiBusy(@NonNull final @NotNull String corpId,
                               @NonNull final @NotNull String botUserId,
                               @NonNull final @NotNull String externalUserId,
                               @NonNull final @NotNull String contactName,
                               @NonNull final List<DifyMessageChatRecordBo> reqMessages,
                               @Nullable final Result<ChatflowResponse> aiResult) {
        // AI处理失败，转人工
        String tipCorpUserWhenError = String.format(DifyChatConstants.AI_TO_USER_MESSAGE, contactName);
        sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                DifyChatConstants.AI_TO_EXTERNAL_USER_MESSAGE_WHEN_ERROR, tipCorpUserWhenError);

        sendMessageComp.sendTextToUser(corpId, botUserId, tipCorpUserWhenError);

        // AI处理失败，发送群提醒
        @Nullable final DifyApiInfoBo.Item apiInfo = difyComponent.getChatflowApiInfo(corpId, botUserId);
        if (ObjectUtils.notEmpty(apiInfo)) {
            difyChatflowAlarmComponent.sendTipsToMeiShi_1V1(corpId, botUserId, apiInfo, aiResult);
        }
    }

    private void recordAiReplyTextMappedReqMessageIds(@NonNull final String conversationId,
                                                      @NonNull final String aiRespTxt,
                                                      @NonNull final List<DifyMessageChatRecordBo> messageRecords) {
        if (ObjectUtils.isEmpty(messageRecords)) {
            // not happened
            return;
        }

        List<String> togetherMessageIds = messageRecords.stream()
                .map(DifyMessageChatRecordBo::getMessageId)
                .collect(Collectors.toList());

        aiResultRepository.save(conversationId, aiRespTxt, togetherMessageIds);
    }

    private void recordNotReqAiBizInfo(@NonNull final List<DifyMessageChatRecordBo> messageRecords,
                                       @NonNull final NotReqAiReason notReqAiReason) {
        if (ObjectUtils.isEmpty(messageRecords)) {
            return;
        }

        for (DifyMessageChatRecordBo messageRecord : messageRecords) {
            JuziSingleChatContentRecord singleChatContentRecord =
                    chatContentRemoteService.getSingleChatContentRecord(messageRecord.getMessageId());

            if (ObjectUtils.isNull(singleChatContentRecord)) { // 肯定存在
                log.error("not found standard 1v1 chat content record, message id = {}", messageRecord.getMessageId());
                continue;
            }

            final String messageId = singleChatContentRecord.getMessageId();

            // 当前聊天是肯定是外部联系人发送的
            if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                log.error("curr chat content not send by external user, message id = {}", messageRecord.getMessageId());
                continue;
            }

            chatContentRemoteService.recordSingleChatBizInfo(
                    messageId,
                    JuziSingleChatContentRecordAiBizInfo.builder()
                            .messageId(messageId)
                            .aiType(AiType.DIFY_AI)
                            .sendBy(SingleChatSendBy.EXTERNAL_USER)
                            .reqToAiBizInfo(
                                    JuziSingleChatContentRecordAiBizInfo.ReqToAiBizInfo.builder()
                                            .flag(false)
                                            .notReqReason(notReqAiReason)
                                            .build()
                            )
                            .build()
            );
        }
    }

    private void recordSuccessReqAiBizInfo(@NonNull final List<DifyMessageChatRecordBo> messageRecords,
                                           @Nullable final Object aiResult) {

        if (ObjectUtils.isEmpty(messageRecords)) {
            return;
        }

        List<String> messageIds = messageRecords.stream()
                .map(DifyMessageChatRecordBo::getMessageId)
                .collect(Collectors.toList());

        for (DifyMessageChatRecordBo messageRecord : messageRecords) {

            JuziSingleChatContentRecord singleChatContentRecord =
                    chatContentRemoteService.getSingleChatContentRecord(messageRecord.getMessageId());

            if (ObjectUtils.isNull(singleChatContentRecord)) { // 肯定存在
                log.error("not found standard 1v1 chat content record, message id = {}", messageRecord.getMessageId());
                continue;
            }

            final String messageId = singleChatContentRecord.getMessageId();

            // 当前聊天是肯定是外部联系人发送的
            if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                log.error("curr chat content not send by external user, message id = {}", messageRecord.getMessageId());
                continue;
            }

            chatContentRemoteService.recordSingleChatBizInfo(
                    messageId,
                    JuziSingleChatContentRecordAiBizInfo.builder()
                            .messageId(messageId)
                            .aiType(AiType.DIFY_AI)
                            .sendBy(SingleChatSendBy.EXTERNAL_USER)
                            .reqToAiBizInfo(
                                    JuziSingleChatContentRecordAiBizInfo.ReqToAiBizInfo.builder()
                                            .flag(true)
                                            .togetherMessageIds(messageIds)
                                            .aiResult(aiResult)
                                            .build()
                            )
                            .build()
            );
        }
    }
}
