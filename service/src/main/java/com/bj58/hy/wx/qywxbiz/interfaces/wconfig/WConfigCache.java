package com.bj58.hy.wx.qywxbiz.interfaces.wconfig;

import com.bj58.wconfig.client.WConfigClient;
import com.bj58.wconfig.client.exceptions.WConfigClientException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: hezhe
 * @Date: 2025/04/28 15:18
 */
@Slf4j
@Component
public class WConfigCache {

    @Resource
    private WConfigClient wConfigClient;

    // 自动回填类型，可复用上文类型，但不必实现 WConfigCallback 接口
    private final ConcurrentHashMap<String, String> cacheMap = new ConcurrentHashMap<>();

    public String getConfigValue(String key) {
        try {
            if (cacheMap.get(key) == null) {
                wConfigClient.subscribeConfig(
                        /* 参数都可为 null */
                        key,
                        null,
                        null,
                        /* 自动回填类型不必实现 callback 接口 */
                        null,
                        /* 订阅回调类型，实现 WConfigCallback 接口 */
                        /* 一个配置订阅可以关联多个 callback 实现 */
                        (namespace, newConfigs) -> {
                            cacheMap.put(key, newConfigs.get("default_key"));
                            log.info("callback:namespace:{},newConfigs{}", namespace, newConfigs);
                        });
            }
        } catch (WConfigClientException e) {
            log.error("wConfig获取失败", e);
        }

        log.info("{}拉取结果{}", key, cacheMap.get(key));
        return cacheMap.get(key);
    }
}