package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.impl.risk_rule;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.AbstractExternalContactEventBo;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.AddExternalContactEventBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.DateUtils;
import lombok.NonNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 企业 被动 添加 外部联系人的风控规则校验
 *
 * <AUTHOR>
 */
@Component
public class CorpProactivelyAddExternalUserRiskWxWorkEventHandler extends AbstractCorpAddExternalUserRiskWxWorkEventHandler {

    @Override
    public void process(final @NonNull AddExternalContactEventBo event) {
        // 判断是否是 企业成员 被动 添加 外部联系人
        AbstractExternalContactEventBo.Ext ext = event.getExt();
        if (ObjectUtils.isNull(ext)) {
            return;
        }

        String operUserId = ext.getOperUserId();
        if (ObjectUtils.isEmpty(operUserId)) {
            return;
        }

        if (!Objects.equals(event.getExternalUserId(), operUserId)) { // 非客户主动添加
            return;
        }

        // 过滤掉 在职、离职 继承的数据
        if (Objects.equals(202, ext.getAddWay())) {
            return;
        }

        checkHasBeenTriggeredCorpLimit(event);
        checkHasBeenTriggeredCorpUserLimit(event);
    }

    private void checkHasBeenTriggeredCorpLimit(final @NonNull AddExternalContactEventBo event) {
        // 判断当前企业 是否超过了 被动添加的限制
        RAtomicLong rAtomicLong = redisson.getAtomicLong(
                // CorpProactivelyAddExternalUserCount:ww5cfa32107e9a1f20
                String.format("CorpProactivelyAddExternalUserCount:%s", event.getCorpId())
        );

        long currCount = rAtomicLong.incrementAndGet();

        if (currCount == 1) {
            long seconds = DateUtils.getTomorrowZeroTimeInterval();
            seconds = seconds < 5 ? 5 : seconds; // 最低设置个5s的过期时间吧~
            rAtomicLong.expire(seconds, TimeUnit.SECONDS);
        }

        Long threshold = getCorpProactivelyAddThreshold(event);
        if (ObjectUtils.isNull(threshold)) {
            return; // 没有设置任何报警阈值
        }

        log.info("[{}]当前企业被动添加客户个数：{}，风控阈值：{}", event.getCorpId(), currCount, threshold);

        if (currCount >= threshold) { // 报警
            RBucket<String> bucket = redisson.getBucket(rAtomicLong.getName() + ":SEND_MEISHI_FLAG", StringCodec.INSTANCE);
            if (bucket.isExists()) {
                return;
            }

            lockSupport.executeWithoutResult(
                    bucket.getName() + "_LOCK_",
                    () -> {
                        if (bucket.isExists()) { // 已经发送过告警，不再重复发送
                            return;
                        }

                        Map<String, String> content = new LinkedHashMap<>();
                        content.put("通知内容：", "当前企业被动添加客户个数已触达风控上限，请尽快确认情况");
                        content.put("企业主体：", event.getCorpId());
                        content.put("添加个数：", currCount + "个");
                        content.put("风控阈值：", threshold + "个/每天");
                        riskAlarmComponent.alarm("企业微信风控通知", content);

                        bucket.set("1", riskRuleComponent.getAlarmTimeInterval(), TimeUnit.SECONDS);
                    }
            );
        }
    }

    private void checkHasBeenTriggeredCorpUserLimit(final @NonNull AddExternalContactEventBo event) {
        // 判断当前企业的当前成员 是否超过了 被动添加的限制
        RAtomicLong rAtomicLong = redisson.getAtomicLong(
                // CorpProactivelyAddExternalUserCount:ww5cfa32107e9a1f20:WangYanRui
                String.format("CorpProactivelyAddExternalUserCount:%s:%s", event.getCorpId(), event.getUserId())
        );

        long currCount = rAtomicLong.incrementAndGet();

        if (currCount == 1) {
            long seconds = DateUtils.getTomorrowZeroTimeInterval();
            seconds = seconds < 5 ? 5 : seconds; // 最低设置个5s的过期时间吧~
            rAtomicLong.expire(seconds, TimeUnit.SECONDS);
        }

        Long threshold = getCorpUserProactivelyAddThreshold(event);
        if (ObjectUtils.isNull(threshold)) {
            return; // 没有设置任何报警阈值
        }

        log.info("[{},{}]当前企业成员被动添加客户个数：{}，风控阈值：{}", event.getCorpId(), event.getUserId(), currCount, threshold);

        if (currCount >= threshold) {// 报警
            RBucket<String> bucket = redisson.getBucket(rAtomicLong.getName() + ":SEND_MEISHI_FLAG", StringCodec.INSTANCE);
            if (bucket.isExists()) {
                return;
            }

            lockSupport.executeWithoutResult(
                    bucket.getName() + "_LOCK_",
                    () -> {
                        if (bucket.isExists()) { // 已经发送过告警，不再重复发送
                            return;
                        }

                        Map<String, String> content = new LinkedHashMap<>();
                        content.put("通知内容：", "当前企业成员被动添加客户个数已触达风控上限，请尽快确认情况");
                        content.put("企业主体：", event.getCorpId());
                        content.put("企业成员：", event.getUserId());
                        content.put("添加个数：", currCount + "个");
                        content.put("风控阈值：", threshold + "个/每天");
                        riskAlarmComponent.alarm("企业微信风控通知", content);

                        bucket.set("1", riskRuleComponent.getAlarmTimeInterval(), TimeUnit.SECONDS);
                    }
            );
        }
    }

}
