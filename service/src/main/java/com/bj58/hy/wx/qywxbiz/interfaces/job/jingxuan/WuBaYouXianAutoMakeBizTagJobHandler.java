package com.bj58.hy.wx.qywxbiz.interfaces.job.jingxuan;

import cn.hutool.core.thread.BlockPolicy;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ValidationUtils;
import com.bj58.hy.wx.qywx.contract.ICorpCustomerTagService;
import com.bj58.hy.wx.qywx.contract.IExternalContactService;
import com.bj58.hy.wx.qywx.contract.dto.corp_tag.CorpCustomerTagResp;
import com.bj58.hy.wx.qywx.contract.dto.corp_tag.GetCorpCustomerTagsReq;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.ListResp;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.UpdateExternalContactTagsReq;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.enums.ExternalContactStatus;
import com.bj58.hy.wx.qywxbiz.entity.AutoMakeDjjxBizTagDpEntity;
import com.bj58.hy.wx.qywxbiz.entity.QAutoMakeDjjxBizTagDpEntity;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.constants.WMonitorEnum;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.IMMessageBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.enums.IMMessageLevelEnum;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.utils.IMMessageUtils;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.bj58.job.core.biz.model.ReturnT;
import com.bj58.job.core.handler.IJobHandler;
import com.bj58.job.core.handler.annotation.JobHandler;
import com.bj58.job.core.log.WJobLogger;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.bj58.wmonitor.javaclient.WMonitor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.*;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * https://ee.58corp.com/base2/w/items/GRD-103
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@JobHandler(value = "WuBaYouXianAutoMakeBizTagJobHandler")
public class WuBaYouXianAutoMakeBizTagJobHandler extends IJobHandler {

    @SCFClient(lookup = ICorpCustomerTagService.SCF_URL)
    private ICorpCustomerTagService corpCustomerTagService;

    @SCFClient(lookup = IExternalContactService.SCF_URL)
    private IExternalContactService externalContactService;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private EntityManager entityManager;

    private static final String WARNING_REDIS_OA_KEY = "auto_make_djjx_biz_tag_oa_warning";
    private static final String WARNING_REDIS_GROUP_KEY = "auto_make_djjx_biz_tag_group_warning";

    private final Set<String> missingTags = Sets.newConcurrentHashSet();
    private final Set<String> missingTagGroups = Sets.newConcurrentHashSet();

    private final Executor executor = new ThreadPoolExecutor(
            0, 4,
            1, TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("auto_make_djjx_biz_tag-%d").build(),
            new BlockPolicy(Runnable::run)
    );

    private RRateLimiter updateExternalContactTagsRateLimiter;

    private RRateLimiter fetchExternalContactsRateLimiter;

    @PostConstruct
    public void init() {
        updateExternalContactTagsRateLimiter = redisson.getRateLimiter("HYQYWX:updateExternalContactTags");
        updateExternalContactTagsRateLimiter.setRate(RateType.OVERALL, 10, 1, RateIntervalUnit.SECONDS);

        fetchExternalContactsRateLimiter = redisson.getRateLimiter("FetchExternalContacts");
        fetchExternalContactsRateLimiter.setRate(RateType.OVERALL, 10, 1, RateIntervalUnit.SECONDS);
    }


    @Override
    public ReturnT<String> execute(String param) throws Exception {
        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }

        JSONObject jobParam = JSONObject.parseObject(param);
        if (ObjectUtils.isEmpty(jobParam)) {
            return ReturnT.FAIL;
        }

        String corpId = jobParam.getString("corpId");
        if (StringUtils.isBlank(corpId)) {
            return ReturnT.FAIL;
        }

        try {
            execute0(corpId);

        } catch (Exception e) {
            String logInfo = String.format("auto make biz tag error, ex msg = %s", e.getMessage());
            log.error(logInfo, e);
            WJobLogger.log(logInfo);
            WJobLogger.log(e);
            return ReturnT.FAIL;

        } finally {

            missingTags.clear();
            missingTagGroups.clear();
        }

        return ReturnT.SUCCESS;
    }

    private void execute0(@NonNull final String corpId) throws Exception {
        // 1. 获得当前企业下 所有的 标签组和对应的标签
        // key = tag group name
        // value = all tags in tag group with corresponding id
        final Map<String, Map<String, String>> allTagsInfo = getCorpCustomerTags(corpId);
        if (ObjectUtils.isEmpty(allTagsInfo)) { // 没有查找到任何的标签
            throw new RuntimeException("not found any corp customer tags");
        }

        String logInfo = String.format("found corp customer tags map, info = %s", JacksonUtils.format(allTagsInfo));
        log.info(logInfo);
        WJobLogger.log(logInfo);

        // 2. 获得当前企业下 自动打标签时，需要处理的一些标签组
        // 当前企业下，需要保持内部标签去重的标签组列表
        RSet<String> TAG_GROUP_NAME_WHOSE_TAGS_NEED_TO_BE_UNIQUE =
                redisson.getSet(String.format("%s:TAG_GROUP_NAME_WHOSE_TAGS_NEED_TO_BE_UNIQUE", corpId), StringCodec.INSTANCE);
        // 当前企业下，每次打标签需要强制删除的标签组列表
        RSet<String> TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE =
                redisson.getSet(String.format("%s:TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE", corpId), StringCodec.INSTANCE);

        // 3. 组装成metadata
        final Metadata metadata = new Metadata(allTagsInfo, TAG_GROUP_NAME_WHOSE_TAGS_NEED_TO_BE_UNIQUE, TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE);

        // 4. 遍历全量好友关系，处理标签数据
        int limit = 500;

        String cursor = null;

        // RBucket<String> bucket = redisson.getBucket(String.format("%s:AutoMakeBizTagInitCursor", corpId), StringCodec.INSTANCE);
        // String initCursor = bucket.getAndDelete();
        // if (ObjectUtils.isNotEmpty(initCursor)) {
        //     cursor = initCursor;
        // }

        while (true) {
            fetchExternalContactsRateLimiter.acquire();

            Result<ListResp> externalResp = externalContactService.list(corpId, cursor, limit);

            if (ObjectUtils.isEmpty(externalResp)
                    || externalResp.isFailed()
                    || ObjectUtils.isEmpty(externalResp.getData())
                    || ObjectUtils.isEmpty(externalResp.getData().getList())) {
                break;
            }

            List<ListResp.Item> list = externalResp.getData().getList();
            cursor = externalResp.getData().getNextCursor();

            for (ListResp.Item item : list) {

                executor.execute(() -> {
                    AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo =
                            buildAutoMakeBizTagExternalContactInfo(corpId, item, metadata);
                    if (ObjectUtils.isEmpty(autoMakeBizTagExternalContactInfo)) {
                        return;
                    }

                    try {
                        autoMarkBizTag(autoMakeBizTagExternalContactInfo);
                    } catch (Exception e) {
                        String errInfo = String.format("auto make biz tag error, auto make info = %s, ex msg = %s", JacksonUtils.format(autoMakeBizTagExternalContactInfo), e.getMessage());
                        log.error(errInfo, e);
                        WJobLogger.log(errInfo);
                        WJobLogger.log(e);
                    }
                });
            }

            // bucket.set(cursor);
        }

        // bucket.delete();

        tips();
    }

    private void tips() {
        List<String> oaNames = this.getOaNames();

        if (ObjectUtils.isNotEmpty(oaNames) &&
                (CollectionUtils.isNotEmpty(missingTags) || CollectionUtils.isNotEmpty(missingTagGroups))) {

            IMMessageBo imMessageBo = new IMMessageBo();
            imMessageBo.setTitle("标签组、标签缺失通知");
            imMessageBo.setLevel(IMMessageLevelEnum.Warning);
            imMessageBo.setReceives(oaNames);

            IMMessageBo.TitleTextMap titleText = new IMMessageBo.TitleTextMap();
            if (ObjectUtils.isNotEmpty(missingTagGroups)) {
                titleText.put("标签组名称", String.join(",", missingTagGroups));
            }
            if (CollectionUtils.isNotEmpty(missingTags)) {
                titleText.put("标签信息(标签组-标签名称)", String.join(",", missingTags));
            }
            imMessageBo.setContent(titleText);

            IMMessageUtils.sendSimpleMessageToOas(imMessageBo);
        }

        List<String> groupIds = this.getGroupIds();
        if (ObjectUtils.isEmpty(groupIds)) {
            return;
        }
        IMMessageBo imMessageBo = new IMMessageBo();
        imMessageBo.setTitle("离线打标签任务完成通知");
        imMessageBo.setLevel(IMMessageLevelEnum.Info);
        imMessageBo.setReceives(groupIds);

        IMMessageBo.TitleTextMap titleText = new IMMessageBo.TitleTextMap();
        titleText.put("完成时间", DateUtils.dateToStr(DateUtils.yyyyMMdd4HHmm, new Date()));
        imMessageBo.setContent(titleText);

        IMMessageUtils.sendSimpleMessageToGroup(imMessageBo);
    }

    private List<String> getOaNames() {
        RSet<String> oaNames;
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            oaNames = redisson.getSet(WARNING_REDIS_OA_KEY, StringCodec.INSTANCE);
        } else {
            oaNames = redisson.getSet(WARNING_REDIS_OA_KEY + ":SANDBOX", StringCodec.INSTANCE);
        }

        return new ArrayList<>(oaNames);
    }

    private List<String> getGroupIds() {
        RSet<String> groupIds;
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            groupIds = redisson.getSet(WARNING_REDIS_GROUP_KEY, StringCodec.INSTANCE);
        } else {
            groupIds = redisson.getSet(WARNING_REDIS_GROUP_KEY + ":SANDBOX", StringCodec.INSTANCE);
        }

        return new ArrayList<>(groupIds);
    }

    @Nullable
    private AutoMakeBizTagExternalContactInfo buildAutoMakeBizTagExternalContactInfo(@NonNull final String corpId,
                                                                                     final ListResp.Item item,
                                                                                     @NonNull final Metadata metadata) {
        if (!couldAutoMakeBizTag(item)) {
            return null;
        }

        String corpUserId = Objects.requireNonNull(item.getUserId());
        String externalUserId = Objects.requireNonNull(item.getExternalUserId());

        Long externalUser58Id = item.getExternalUser58Id();

        AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo =
                new AutoMakeBizTagExternalContactInfo(metadata, corpId, corpUserId, externalUserId, externalUser58Id);

        fillingAutoMakeBizTags(autoMakeBizTagExternalContactInfo);

        fillingAutoRemoveBizTags(autoMakeBizTagExternalContactInfo);

        return autoMakeBizTagExternalContactInfo;
    }


    private void fillingAutoMakeBizTags(@NonNull final AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo) {
        final Long wubaId = autoMakeBizTagExternalContactInfo.getExternalUser58Id();
        if (ObjectUtils.isEmpty(wubaId)) {
            return;
        }

        final Set<AutoMakeTag> autoAddTags = autoMakeBizTagExternalContactInfo.getAutoAddTags();
        final Set<AutoMakeTag> autoRemoveTags = autoMakeBizTagExternalContactInfo.getAutoRemoveTags();

        List<AutoMakeDjjxBizTagDpEntity> autoMakeDjjxBizTagDpEntities = getAutoMakeDjjxBizTagDpEntitiesBy58id(wubaId);
        if (ObjectUtils.isEmpty(autoMakeDjjxBizTagDpEntities)) {
            return;
        }

        @NonNull final Metadata metadata = autoMakeBizTagExternalContactInfo.getMetadata();
        @NonNull final Map<String, Map<String, String>> allTagsInfo = metadata.getAllTagsInfo();

        for (AutoMakeDjjxBizTagDpEntity autoMakeDjjxBizTagDpEntity : autoMakeDjjxBizTagDpEntities) {
            String tagGroupName = autoMakeDjjxBizTagDpEntity.getTagGroupName();
            String tagName = autoMakeDjjxBizTagDpEntity.getTagName();

            Map<String, String> tagName2TagIdMap = allTagsInfo.get(tagGroupName);
            if (ObjectUtils.isEmpty(tagName2TagIdMap)) { // 不应该出现此情况
                String logInfo = String.format("标签组不存在，标签组名字：%s", tagGroupName);
                missingTagGroups.add(tagGroupName);
                log.error(logInfo);
                WJobLogger.log(logInfo);
                WMonitor.sum(WMonitorEnum.DAO_JIA_JING_XUAN_AUTO_MAKE_BIZ_TAG_NOT_FOUND_TAG_GROUP.getAttribute(), 1);
                continue;
            }

            String tagId = tagName2TagIdMap.get(tagName);
            if (ObjectUtils.isEmpty(tagId)) { // 不应该出现此情况
                String logInfo = String.format("标签不存在，标签组名字：%s，标签名字：%s", tagGroupName, tagName);
                missingTags.add(String.format("标签组：%s，标签：%s", tagGroupName, tagName));
                log.error(logInfo);
                WJobLogger.log(logInfo);
                WMonitor.sum(WMonitorEnum.DAO_JIA_JING_XUAN_AUTO_MAKE_BIZ_TAG_NOT_FOUND_TAG.getAttribute(), 1);
                continue;
            }

            AutoMakeTag autoAddTag = new AutoMakeTag(tagGroupName, tagName, tagId);

            int operationType = autoMakeDjjxBizTagDpEntity.getOperationType();
            if (operationType == 1) {
                autoAddTags.add(autoAddTag);
            } else if (operationType == 2) {
                autoRemoveTags.add(autoAddTag);
            } else {
                throw new RuntimeException("unsupported operation type, val = " + operationType);
            }
        }

    }

    private void fillingAutoRemoveBizTags(@NonNull final AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo) {
        // 填充当前企业下，每次打标签需要强制删除的标签组 下需要删除的标签
        fillingAutoRemoveBizTagsEmployTagGroupsWhoseTagsNeedForceDelete(autoMakeBizTagExternalContactInfo);

        // 填充当前企业下，需要保持其下标签去重的标签组列表 下需要删除的标签
        fillingAutoRemoveBizTagsEmployTagGroupsWhoseTagsNeedToBeUnique(autoMakeBizTagExternalContactInfo);

        // 处理之后，可能存在新增的标签，也在删除的标签之中 的情况，需要二次处理一下
        final Set<AutoMakeTag> autoRemoveTags = autoMakeBizTagExternalContactInfo.getAutoRemoveTags();
        final Set<AutoMakeTag> autoAddTags = autoMakeBizTagExternalContactInfo.getAutoAddTags();
        autoRemoveTags.removeIf(autoAddTags::contains);
    }

    private static void fillingAutoRemoveBizTagsEmployTagGroupsWhoseTagsNeedForceDelete(@NonNull final AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo) {
        @NonNull final Metadata metadata = autoMakeBizTagExternalContactInfo.getMetadata();
        @NonNull final Map<String, Map<String, String>> allTagsInfo = metadata.getAllTagsInfo();

        final Set<AutoMakeTag> autoRemoveTags = autoMakeBizTagExternalContactInfo.getAutoRemoveTags();

        Set<String> TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE = metadata.TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE;
        for (String tagGroupName : TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE) {
            Map<String, String> tagName2TagIdMap = allTagsInfo.get(tagGroupName);
            if (ObjectUtils.isEmpty(tagName2TagIdMap)) {
                continue;
            }
            for (Map.Entry<String, String> entry : tagName2TagIdMap.entrySet()) {
                String tagName = entry.getKey();
                String tagId = entry.getValue();

                AutoMakeTag autoRemoveTag = new AutoMakeTag(tagGroupName, tagName, tagId);
                autoRemoveTags.add(autoRemoveTag);
            }
        }
    }

    private static void fillingAutoRemoveBizTagsEmployTagGroupsWhoseTagsNeedToBeUnique(@NonNull final AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo) {
        final Set<AutoMakeTag> autoAddTags = autoMakeBizTagExternalContactInfo.getAutoAddTags();
        if (ObjectUtils.isEmpty(autoAddTags)) { // 如果没有新增标签，无需处理
            return;
        }

        @NonNull final Metadata metadata = autoMakeBizTagExternalContactInfo.getMetadata();
        @NonNull final Map<String, Map<String, String>> allTagsInfo = metadata.getAllTagsInfo();

        // 获取当前企业下，需要保持其下标签去重的标签组列表
        Set<String> TAG_GROUP_SET_WHOSE_TAGS_NEED_TO_BE_UNIQUE = metadata.TAG_GROUP_NAME_WHOSE_TAGS_NEED_TO_BE_UNIQUE;
        if (ObjectUtils.isEmpty(TAG_GROUP_SET_WHOSE_TAGS_NEED_TO_BE_UNIQUE)) {
            return;
        }

        // 判断当前新增标签，过滤出 需要标签唯一处理的标签组
        Set<String> tagGroups = new HashSet<>();
        for (AutoMakeTag autoAddTag : autoAddTags) {
            String tagGroupName = autoAddTag.getTagGroupName();

            // 当前标签组是否需要标签唯一处理？
            if (TAG_GROUP_SET_WHOSE_TAGS_NEED_TO_BE_UNIQUE.contains(tagGroupName)) {
                tagGroups.add(tagGroupName);
            }
        }

        final Set<AutoMakeTag> autoRemoveTags = autoMakeBizTagExternalContactInfo.getAutoRemoveTags();

        // 遍历需要标签唯一处理的标签组，将未在 新增标签列表 中的标签添加到 待删除列表 中
        for (String tagGroupName : tagGroups) {
            // 当前标签组下的所有标签
            Map<String, String> tagName2TagIdMap = Objects.requireNonNull( // 前一步填充时有校验
                    allTagsInfo.get(tagGroupName)
            );

            // 当前标签组下，本次需要打上的标签
            Set<String> autoAddTagNames = autoAddTags.stream()
                    .filter(autoAddTag -> tagGroupName.equals(autoAddTag.getTagGroupName()))
                    .map(AutoMakeTag::getTagName)
                    .collect(Collectors.toSet());

            for (Map.Entry<String, String> entry : tagName2TagIdMap.entrySet()) {
                String tagName = entry.getKey();
                String tagId = entry.getValue();

                if (autoAddTagNames.contains(tagName)) {
                    continue;
                }

                AutoMakeTag autoRemoveTag = new AutoMakeTag(tagGroupName, tagName, tagId);
                autoRemoveTags.add(autoRemoveTag);
            }
        }
    }

    /**
     * 自动打标签允许的状态
     */
    private static final Set<Integer> ALLOW_STATUS = new HashSet<>(Arrays.asList(
            ExternalContactStatus.ENABLE.getCode(),
            ExternalContactStatus.DELETE_BY_EXTERNAL_USER.getCode()
    ));

    private boolean couldAutoMakeBizTag(@NonNull final ListResp.Item item) {

        Integer status = item.getStatus();
        if (!ALLOW_STATUS.contains(status)) { // 状态不对的数据不需要处理
            return false;
        }

        // Long externalUser58Id = item.getExternalUser58Id();
        // if (ObjectUtils.isNull(externalUser58Id)) { // 没有58id的数据不需要处理
        //     return false;
        // }

        return true;
    }


    private void autoMarkBizTag(@NonNull final AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo) throws Exception {

        String logInfo = String.format("[external user id = %s, corp user id = %s] try auto make biz tag.",
                autoMakeBizTagExternalContactInfo.getExternalUserId(), autoMakeBizTagExternalContactInfo.getUserId());
        log.info(logInfo);
        WJobLogger.log(logInfo);

        // 本次需要新增的标签
        final Set<String> addTagIds = autoMakeBizTagExternalContactInfo.getAutoAddTags().stream()
                .map(AutoMakeTag::getTagId)
                .collect(Collectors.toSet());
        // 需要清除的旧标签
        final Set<String> delTagIds = autoMakeBizTagExternalContactInfo.getAutoRemoveTags().stream()
                .map(AutoMakeTag::getTagId)
                .collect(Collectors.toSet());

        if (ObjectUtils.isEmpty(addTagIds) && ObjectUtils.isEmpty(delTagIds)) {
            logInfo = String.format("[external user id = %s, corp user id = %s] not found any biz tag, auto make biz tag info = %s",
                    autoMakeBizTagExternalContactInfo.getExternalUserId(), autoMakeBizTagExternalContactInfo.getUserId(),
                    JacksonUtils.format(autoMakeBizTagExternalContactInfo));
            log.info(logInfo);
            WJobLogger.log(logInfo);
            return;
        }

        UpdateExternalContactTagsReq updateExternalContactTagsReq = new UpdateExternalContactTagsReq();
        updateExternalContactTagsReq.setCorpId(autoMakeBizTagExternalContactInfo.getCorpId());
        updateExternalContactTagsReq.setUserId(autoMakeBizTagExternalContactInfo.getUserId());
        updateExternalContactTagsReq.setExternalUserId(autoMakeBizTagExternalContactInfo.getExternalUserId());
        updateExternalContactTagsReq.setAddTagIds(addTagIds);
        updateExternalContactTagsReq.setRemoveTagIds(delTagIds);

        updateExternalContactTagsRateLimiter.acquire();
        externalContactService.updateExternalContactTags(updateExternalContactTagsReq);

        Set<String> addTagNames = autoMakeBizTagExternalContactInfo.getAutoAddTags()
                .stream()
                .map(AutoMakeTag::getTagName)
                .collect(Collectors.toSet());
        Set<String> removeTagNames = autoMakeBizTagExternalContactInfo.getAutoRemoveTags()
                .stream()
                .map(AutoMakeTag::getTagName)
                .collect(Collectors.toSet());
        logInfo = String.format("[external user id = %s, corp user id = %s] make new biz tag done, " +
                        "auto make new tags = %s, auto make remove tags = %s",
                autoMakeBizTagExternalContactInfo.getExternalUserId(), autoMakeBizTagExternalContactInfo.getUserId(),
                JacksonUtils.format(addTagNames), JacksonUtils.format(removeTagNames));
        log.info(logInfo);
        WJobLogger.log(logInfo);
    }

    /**
     * 获取 所有标签组 和 对应的标签 以及 其标签id
     * key = tag group name
     * value = all tags in tag group with corresponding id
     */
    @NotNull
    private Map<String, Map<String, String>> getCorpCustomerTags(@NonNull final String corpId) throws Exception {
        //  获取所有的企业客户标签
        GetCorpCustomerTagsReq corpCustomerTagsReq = new GetCorpCustomerTagsReq();
        corpCustomerTagsReq.setCorpId(corpId);
        Result<List<CorpCustomerTagResp>> corpCustomerTags = corpCustomerTagService
                .getCorpCustomerTags(corpCustomerTagsReq);

        List<CorpCustomerTagResp> allTags = corpCustomerTags.getData();
        if (ObjectUtils.isEmpty(allTags)) {
            return Collections.emptyMap();
        }

        // key = tag group name
        // value = all tags in tag group with corresponding id
        Map<String, Map<String, String>> result = new HashMap<>();

        for (CorpCustomerTagResp tagGroup : allTags) {

            String tagGroupName = tagGroup.getGroupName();

            result.put(tagGroupName, new HashMap<>());

            for (CorpCustomerTagResp.Tag tag : tagGroup.getTags()) {

                result.get(tagGroupName).put(tag.getName(), tag.getId());
            }
        }

        return result;
    }

    @Data
    private static class AutoMakeBizTagExternalContactInfo {

        @JSONField(serialize = false) // fastjson
        @JsonIgnore // jackson
        @javax.validation.constraints.NotNull
        private final Metadata metadata;

        @NotEmpty
        private final String corpId;

        @NotEmpty
        private final String userId;

        @NotEmpty
        private final String externalUserId;

        @Nullable
        private final Long externalUser58Id;

        // 需要自动打上的标签
        private final Set<AutoMakeTag> autoAddTags = new LinkedHashSet<>();

        // 需要强制删除的标签
        private final Set<AutoMakeTag> autoRemoveTags = new LinkedHashSet<>();

        public AutoMakeBizTagExternalContactInfo(@NonNull final Metadata metadata,
                                                 @NonNull final String corpId,
                                                 @NonNull final String userId,
                                                 @NonNull final String externalUserId,
                                                 @Nullable final Long externalUser58Id) {
            this.metadata = metadata;

            this.corpId = corpId;
            this.userId = userId;
            this.externalUserId = externalUserId;
            this.externalUser58Id = externalUser58Id;

            ValidationUtils.validate(this);
        }
    }

    @Data
    private static class AutoMakeTag {

        @NotEmpty
        private final String tagGroupName;

        @NotEmpty
        private final String tagName;

        @NotEmpty
        private final String tagId;

        public AutoMakeTag(@NonNull final String tagGroupName,
                           @NonNull final String tagName,
                           @NonNull final String tagId) {
            this.tagGroupName = tagGroupName;
            this.tagName = tagName;
            this.tagId = tagId;

            ValidationUtils.validate(this);
        }
    }

    @Data
    private static class Metadata {
        // 当前企业下 所有的 标签组和对应的标签
        // key = tag group name
        // value = all tags in tag group with corresponding id
        @NotEmpty
        private final Map<String, Map<String, String>> allTagsInfo;

        // 当前企业下，需要保持内部标签去重的标签组列表
        private final Set<String> TAG_GROUP_NAME_WHOSE_TAGS_NEED_TO_BE_UNIQUE;

        // 当前企业下，每次打标签需要强制删除的标签组列表
        private final Set<String> TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE;

        public Metadata(@NonNull final Map<String, Map<String, String>> allTagsInfo,
                        @Nullable final Set<String> TAG_GROUP_NAME_WHOSE_TAGS_NEED_TO_BE_UNIQUE,
                        @Nullable final Set<String> TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE) {

            this.allTagsInfo = Collections.unmodifiableMap(allTagsInfo);

            this.TAG_GROUP_NAME_WHOSE_TAGS_NEED_TO_BE_UNIQUE = ObjectUtils.isNotEmpty(TAG_GROUP_NAME_WHOSE_TAGS_NEED_TO_BE_UNIQUE) ?
                    Collections.unmodifiableSet(TAG_GROUP_NAME_WHOSE_TAGS_NEED_TO_BE_UNIQUE) : Collections.emptySet();

            this.TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE = ObjectUtils.isNotEmpty(TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE) ?
                    Collections.unmodifiableSet(TAG_GROUP_NAME_WHOSE_TAGS_NEED_FORCE_DELETE) : Collections.emptySet();

            ValidationUtils.validate(this);
        }
    }

    private RRateLimiter getAutoMakeDjjxBizTagDpEntitiesRateLimiter;

    @PostConstruct
    public void initGetTuplesRateLimiter() {
        getAutoMakeDjjxBizTagDpEntitiesRateLimiter = redisson.getRateLimiter("WuBaYouXianAutoMakeBizTagJobHandler:DB:RateLimiter");
        getAutoMakeDjjxBizTagDpEntitiesRateLimiter.setRate(RateType.OVERALL, 500, 1, RateIntervalUnit.SECONDS);
    }

    @NotNull
    private List<AutoMakeDjjxBizTagDpEntity> getAutoMakeDjjxBizTagDpEntitiesBy58id(long wubaId) {
        getAutoMakeDjjxBizTagDpEntitiesRateLimiter.acquire(1); // 稍微限一下速，避免没有标签需要打的时候，频繁的SELECT DB导致CPU报警（无语）

        return new JPAQueryFactory(entityManager)
                .selectFrom(QAutoMakeDjjxBizTagDpEntity.autoMakeDjjxBizTagDpEntity)
                .where(QAutoMakeDjjxBizTagDpEntity.autoMakeDjjxBizTagDpEntity.externalUser58Id.eq(wubaId))
                .orderBy(QAutoMakeDjjxBizTagDpEntity.autoMakeDjjxBizTagDpEntity.id.asc())
                .limit(1000) // 一个人，还能几千个标签？够用了！
                .fetch();
    }
}
