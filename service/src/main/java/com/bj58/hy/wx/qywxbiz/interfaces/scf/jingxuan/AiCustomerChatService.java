package com.bj58.hy.wx.qywxbiz.interfaces.scf.jingxuan;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.spring.support.aspect.GlobalExceptionWrapper;
import com.bj58.hy.lib.spring.support.aspect.VerifiedParams;
import com.bj58.hy.wx.qywxbiz.contract.IAiCustomerChatService;
import com.bj58.hy.wx.qywxbiz.contract.dto.ai.AiWelcomeGetLatestSendTimeReq;
import com.bj58.hy.wx.qywxbiz.contract.dto.ai.AiWelcomeGetLatestSendTimeResp;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.CommonOrderQueryService;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.AbstractQueryDataStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatIdRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatMessageRecordRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiWelcomeLatestSendTimeRepository;
import com.bj58.spat.scf.server.contract.annotation.ServiceBehavior;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@ServiceBehavior
public class AiCustomerChatService implements IAiCustomerChatService {

    @Autowired
    private LbgAiChatMessageRecordRepository chatMessageRecordRepository;

    @Autowired
    private LbgAiWelcomeLatestSendTimeRepository aiWelcomeLatestSendTimeRepository;

    @Autowired
    private CommonOrderQueryService commonOrderQueryService;

    @Autowired
    private LbgAiChatIdRepository aiChatIdRepository;

    @Autowired
    private ExternalContactRemoteService externalContactRemoteService;

    @Autowired
    private ObjectProvider<AbstractQueryDataStrategy> queryDataStrategyObjectProvider;

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<Object> queryBizData(@NotNull @Valid final Map<String, Object> queryParam) throws Exception {
        if (MapUtils.isEmpty(queryParam)) {
            return Result.failure("queryParam is empty");
        }
        Object biztypeObj = queryParam.get("biztype");
        Object chatIdObj = queryParam.get("chatId");
        if (ObjectUtils.isEmpty(biztypeObj) || ObjectUtils.isEmpty(chatIdObj)) {
            return Result.failure("biztype or chatId is empty");
        }

        AbstractQueryDataStrategy queryDataStrategy = queryDataStrategyObjectProvider.stream()
                .filter(p -> p.matched(biztypeObj))
                .findFirst()
                .orElse(null);

        if (queryDataStrategy == null) {
            return Result.failure("biztype is not allow");
        }

        Result<Object> result = queryDataStrategy.process(queryParam);
        log.info("queryParam : " + JacksonUtils.format(queryParam) + "result:" + JacksonUtils.format(result));
        return result;

    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<AiWelcomeGetLatestSendTimeResp> aiWelcomeGetLatestSendTime(@NotNull @Valid final AiWelcomeGetLatestSendTimeReq req) {
        AiWelcomeGetLatestSendTimeResp resp = new AiWelcomeGetLatestSendTimeResp();
        List<AiWelcomeGetLatestSendTimeResp.Item> timestamp = aiWelcomeLatestSendTimeRepository.get(req.getCorpId(),
                req.getBizLine(), req.getBizScene(),
                req.getUserId(), req.getExternalUserId());
        resp.setAiWelcomeSendTimes(timestamp);
        return Result.success(resp);
    }

}
