package com.bj58.hy.wx.qywxbiz.repository;

import com.bj58.hy.wx.qywxbiz.entity.BizDictEntity;
import com.bj58.hy.wx.qywxbiz.entity.QBizDictEntity;
import com.bj58.hy.wx.qywxbiz.repository.jpa.BizDictJpaRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.persistence.EntityManager;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BizDictRepository {

    private final BizDictJpaRepository repository;

    protected final TransactionTemplate transactionTemplate;

    private final EntityManager entityManager;

    @Transactional(readOnly = true)
    public BizDictEntity getOne(@NonNull final Integer bizLine,
                                @NonNull final Integer bizScene) {
        return new JPAQueryFactory(entityManager)
                .selectFrom(QBizDictEntity.bizDictEntity)
                .where(QBizDictEntity.bizDictEntity.bizLine.eq(bizLine))
                .where(QBizDictEntity.bizDictEntity.bizScene.eq(bizScene))
                .fetchFirst();
    }


}
