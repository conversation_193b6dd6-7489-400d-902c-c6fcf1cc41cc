package com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywx.contract.dto.message.MessagePayload;
import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.entity.JuziSingleChatContentRecordAiBizInfo;
import com.bj58.hy.wx.qywxbiz.entity.enums.AiType;
import com.bj58.hy.wx.qywxbiz.entity.enums.NotReqAiReason;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ChatContentRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.bo.DifyMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.comp.DifyChatInterventionConditionsComponent;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.comp.DifyChatSendMessageComp;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.constants.DifyChatConstants;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.repository.DifyChatConversationIdRepository;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.repository.DifyChatExternalUserRecentMessageRecordRepository;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.repository.DifyChatResultRepository;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziChatEventHandler;
import lombok.NonNull;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class JuziChatEventThenDifyChatflowHandler extends AbstractJuziChatEventHandler {

    @Autowired
    private ExternalContactRemoteService externalContactRemoteService;

    @Autowired
    private DifyAsyncChatflowHandler asyncReplyHandler;

    @Autowired
    private DifyChatInterventionConditionsComponent aiInterventionConditionsComponent;

    @Autowired
    private DifyChatSendMessageComp sendMessageComp;

    @Autowired
    private DifyChatExternalUserRecentMessageRecordRepository externalUserRecentMessageRecordRepository;

    @Autowired
    private DifyChatConversationIdRepository aiConversationIdRepository;

    @Autowired
    private DifyChatResultRepository aiResultRepository;

    @Autowired
    private ChatContentRemoteService chatContentRemoteService;

    @Override
    public void process(@NonNull final ChatMessagesEventBo.Body event) {
        // 是否标准化了聊天记录?
        JuziSingleChatContentRecord singleChatContentRecord = getCurrentSingleChatContentRecord();
        if (ObjectUtils.isNull(singleChatContentRecord)) {
            return;
        }

        // 是否是 AI能承接的 客服+外部联系人 聊天
        if (!checkCanBeJoinChatflow(singleChatContentRecord)) {
            return;
        }

        if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
            // 监听用户发送的消息，尝试AI应答

            String corpId = singleChatContentRecord.getCorpId();
            String botUserId = singleChatContentRecord.getUserId();
            String externalUserId = singleChatContentRecord.getExternalUserId();

            if (Objects.equals(event.getMessageType(), 7) ||
                    Objects.equals(event.getMessageType(), 2)) {
                // 目前AI仅可回复文本消息

                final DifyMessageChatRecordBo messageRecord = buildAiMessageChatRecordBo(singleChatContentRecord);

                // 存储聊天记录，供AI以回复
                externalUserRecentMessageRecordRepository.saveRecentChatMessages(
                        corpId, botUserId, externalUserId, messageRecord);

                asyncReplyHandler.commitAsyncAiReplyTask( // 提交一个处理的任务
                        corpId, botUserId, externalUserId,
                        event.getContactName());

            } else {

                String messageInfo = String.format(DifyChatConstants.NOT_SUPPORTED_MESSAGE_TYPE,
                        event.getContactName());
                sendMessageComp.sendTextToUser(corpId, botUserId, messageInfo);

                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.NOT_SUPPORTED_MESSAGE_TYPE);
            }

        } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            // 监听客服发送的消息

            // 假定当前是AI客服回复的，尝试分析数据并且记录
            tryTagAiBizInfoIfReplyByAi(singleChatContentRecord);
        }
    }


    @Override
    public boolean matched(@NonNull final ChatMessagesEventBo.Body event) {
        // 群聊，不走AI
        return ObjectUtils.isEmpty(event.getRoomWecomChatId());
    }

    private boolean checkCanBeJoinChatflow(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        String corpId = singleChatContentRecord.getCorpId();
        String botUserId = singleChatContentRecord.getUserId();
        String externalUserId = singleChatContentRecord.getExternalUserId();

        if (!aiInterventionConditionsComponent.isAiUser(corpId, botUserId)) {
            if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.NOT_HAS_AI_AUTH);
            } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                recordNotReplyByAiBizInfo(singleChatContentRecord); // 肯定不是AI回复的
            }

            return false;
        }

        GetExternalContactRelationshipResp contactRelationshipResult = externalContactRemoteService.getRelationship(
                corpId, botUserId, externalUserId);

        if (ObjectUtils.isNull(contactRelationshipResult)) {
            if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.NOT_FOUND_RELATIONSHIP);
            } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                recordNotReplyByAiBizInfo(singleChatContentRecord); // 没有好友关系，肯定不是AI回复的
            }

            return false;
        }

        return true;
    }

    private void tryTagAiBizInfoIfReplyByAi(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            return;
        }

        // 检查是否是SOP消息
        if (Objects.equals(singleChatContentRecord.getSendSource(), 7)) { // 7- sop功能
            // 标记为SOP消息
            chatContentRemoteService.recordSopMessage(singleChatContentRecord.getMessageId());
            return;
        }

        if (!Objects.equals(singleChatContentRecord.getSendSource(), 6)) { // 6. api发消息
            recordNotReplyByAiBizInfo(singleChatContentRecord);
            return;
        }

        if (Objects.equals(singleChatContentRecord.getMessageType(), 7)) { // 7. 文本消息
            String payload = singleChatContentRecord.getPayload();
            if (ObjectUtils.isEmpty(payload)) {
                log.error("curr chat content is text but not found content, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            JSONObject jsonObj = JSON.parseObject(payload);
            String text = jsonObj.getString("text");
            if (ObjectUtils.isEmpty(text)) {
                log.error("curr chat content is text but not found content, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            @Nullable final String conversationId = aiConversationIdRepository.getConversationId(
                    singleChatContentRecord.getCorpId(),
                    singleChatContentRecord.getUserId(),
                    singleChatContentRecord.getExternalUserId());

            if (ObjectUtils.isEmpty(conversationId)) { // 未和AI交互过
                recordNotReplyByAiBizInfo(singleChatContentRecord);

            } else {

                List<String> replyMessageIds = aiResultRepository.get(conversationId, text);

                if (ObjectUtils.notEmpty(replyMessageIds)) {
                    recordReplyByAiBizInfo(singleChatContentRecord, replyMessageIds);
                } else {
                    recordNotReplyByAiBizInfo(singleChatContentRecord);
                }
            }


        } else {

            recordNotReplyByAiBizInfo(singleChatContentRecord);

        }

    }

    private void recordNotReqAiBizInfo(@NonNull final JuziSingleChatContentRecord singleChatContentRecord,
                                       @NonNull final NotReqAiReason notReqAiReason) {

        final String messageId = singleChatContentRecord.getMessageId();

        // 当前聊天是肯定是外部联系人发送的
        if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
            log.error("curr chat content not send by external user, message id = {}", singleChatContentRecord.getMessageId());
            return;
        }

        chatContentRemoteService.recordSingleChatBizInfo(
                messageId,
                JuziSingleChatContentRecordAiBizInfo.builder()
                        .messageId(messageId)
                        .aiType(AiType.DIFY_AI)
                        .sendBy(SingleChatSendBy.EXTERNAL_USER)
                        .reqToAiBizInfo(
                                JuziSingleChatContentRecordAiBizInfo.ReqToAiBizInfo.builder()
                                        .flag(false)
                                        .notReqReason(notReqAiReason)
                                        .build()
                        )
                        .build()
        );
    }

    private void recordNotReplyByAiBizInfo(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        final String messageId = singleChatContentRecord.getMessageId();

        // 当前聊天是肯定是企业成员人发送的
        if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            log.error("curr chat content not send by corp user, message id = {}", singleChatContentRecord.getMessageId());
            return;
        }

        // 当前聊天是否是企业成员发送的？
        chatContentRemoteService.recordSingleChatBizInfo(
                messageId,
                JuziSingleChatContentRecordAiBizInfo.builder()
                        .messageId(messageId)
                        .aiType(AiType.DIFY_AI)
                        .sendBy(SingleChatSendBy.CORP_USER)
                        .respByAiBizInfo(
                                JuziSingleChatContentRecordAiBizInfo.RespByAiBizInfo.builder()
                                        .flag(false)
                                        .build()
                        )
                        .build()
        );
    }

    private void recordReplyByAiBizInfo(@NonNull final JuziSingleChatContentRecord singleChatContentRecord,
                                        @NonNull final List<String> replyMessageIds) {

        final String messageId = singleChatContentRecord.getMessageId();

        // 当前聊天是否是外部联系人发送的？
        if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            chatContentRemoteService.recordSingleChatBizInfo(
                    messageId,
                    JuziSingleChatContentRecordAiBizInfo.builder()
                            .messageId(messageId)
                            .aiType(AiType.DIFY_AI)
                            .sendBy(SingleChatSendBy.CORP_USER)
                            .respByAiBizInfo(
                                    JuziSingleChatContentRecordAiBizInfo.RespByAiBizInfo.builder()
                                            .flag(true)
                                            .replyMessageIds(replyMessageIds)
                                            .build()
                            )
                            .build()
            );
        }
    }

    private static @NotNull DifyMessageChatRecordBo buildAiMessageChatRecordBo(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        String text = "";

        if (Objects.equals(singleChatContentRecord.getMessageType(), 7)) {
            final MessagePayload.TextPayload textPayload = JacksonUtils.parse(
                    singleChatContentRecord.getPayload(),
                    MessagePayload.TextPayload.class);

            text = textPayload.getText();
        }

        if (Objects.equals(singleChatContentRecord.getMessageType(), 2)) {
            final MessagePayload.VoicePayload voicePayload = JacksonUtils.parse(
                    singleChatContentRecord.getPayload(),
                    MessagePayload.VoicePayload.class);

            text = voicePayload.getText();
        }

        return new DifyMessageChatRecordBo(
                singleChatContentRecord.getCreateTime().getTime(),
                text,
                singleChatContentRecord.getUserId(),
                singleChatContentRecord.getExternalUserId(),
                singleChatContentRecord.getMessageId(),
                singleChatContentRecord.getSendBy().getCode()
        );
    }
}
