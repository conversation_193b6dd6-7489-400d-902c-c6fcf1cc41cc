package com.bj58.hy.wx.qywxbiz.infrastructure.configuration.aop;

import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.aspect.BaseAspect;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.core.Ordered;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * Description: 异常包裹, 通常用于RPC接口上
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@RequiredArgsConstructor
public class RedisLockWrapperAspect extends BaseAspect {

    private final RedissonClient redisson;

    private final ExpressionParser parser = new SpelExpressionParser();

    private final ParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();


    public static final int ORDER = Ordered.LOWEST_PRECEDENCE;

    @Around("@annotation(com.bj58.hy.wx.qywxbiz.infrastructure.configuration.aop.RedisLockWrapper)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        final String lockKey = getLockKey(point);
        final RLock lock = redisson.getLock(lockKey);

        try {
            lock.lock();

            return point.proceed();

        } finally {
            lock.unlock();
        }
    }

    @NotNull
    private String getLockKey(final ProceedingJoinPoint point) {
        final MethodSignature signature = (MethodSignature) point.getSignature();
        final RedisLockWrapper annotation = signature.getMethod().getAnnotation(RedisLockWrapper.class);

        String key = annotation.value();
        if (ObjectUtils.isEmpty(key)) {
            String methodInfo = getSimpleMethodInfoStr(point);
            throw new RuntimeException(String.format("lock key is null, method = %s", methodInfo));
        }

        if (!annotation.spel()) {
            return key;
        }

        final String[] paramNames = getParamNames(point);
        final Object[] args = point.getArgs();
        if (ObjectUtils.isEmpty(args) || ObjectUtils.isEmpty(paramNames)) {
            return key;
        }

        return parseExpression(point, key, paramNames, args);
    }

    private String parseExpression(@NonNull final ProceedingJoinPoint point,
                                   @NonNull final String spel,
                                   @NonNull final String[] argNames,
                                   @NonNull final Object[] argValues) {

        EvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < argNames.length; i++) {
            context.setVariable(argNames[i], argValues[i]);
        }

        String value = parser.parseExpression(spel).getValue(context, String.class);
        if (ObjectUtils.isEmpty(value)) {
            String methodInfo = getSimpleMethodInfoStr(point);

            throw new RuntimeException(String.format(
                    "spel result is null, method = %s, args = %s",
                    methodInfo, JacksonUtils.format(argValues)
            ));
        }

        return value;
    }

    @NotNull
    private static String getSimpleMethodInfoStr(@NonNull final ProceedingJoinPoint point) {
        Method method = ((MethodSignature) point.getSignature()).getMethod();

        String className = method.getDeclaringClass().getName();
        String methodName = method.getName();
        String parameterTypes = Arrays.stream(method.getParameterTypes())
                .map(Class::getName)
                .collect(Collectors.joining(", "));

        String methodInfo = className + "#" + methodName + "(" + parameterTypes + ")";
        return methodInfo;
    }

    @Nullable
    private String[] getParamNames(@NonNull final ProceedingJoinPoint point) {

        final MethodSignature signature = (MethodSignature) point.getSignature();
        final Method method = signature.getMethod();

        final String[] paramNames;
        if (method.getDeclaringClass().isInterface()) {
            try {
                paramNames = discoverer.getParameterNames(
                        point.getTarget()
                                .getClass()
                                .getDeclaredMethod(
                                        signature.getName(),
                                        method.getParameterTypes()
                                )
                );
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            }

        } else {
            paramNames = discoverer.getParameterNames(method);
        }

        return paramNames;
    }

}