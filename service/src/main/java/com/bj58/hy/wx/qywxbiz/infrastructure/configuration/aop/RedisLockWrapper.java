package com.bj58.hy.wx.qywxbiz.infrastructure.configuration.aop;

import java.lang.annotation.*;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface RedisLockWrapper {

    /**
     * lock key
     */
    String value() default "";

    /**
     * 是否spel表达式
     */
    boolean spel() default false;


}
