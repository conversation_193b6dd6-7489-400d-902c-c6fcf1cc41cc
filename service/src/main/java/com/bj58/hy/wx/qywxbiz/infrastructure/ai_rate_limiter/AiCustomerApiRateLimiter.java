package com.bj58.hy.wx.qywxbiz.infrastructure.ai_rate_limiter;

import cn.hutool.core.collection.ConcurrentHashSet;
import com.bj58.hy.lib.core.ResultStatus;
import com.bj58.hy.lib.core.exception.ServiceException;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.constants.WMonitorEnum;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.ApplicationUtils;
import com.bj58.wmonitor.javaclient.WMonitor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 精选AI客服接口限制
 * 300次/30秒
 */
@Slf4j
public class AiCustomerApiRateLimiter {

    private final String corpId;

    /**
     * 限流器类型
     */
    private final AiCustomerApiRateLimiterType rateLimiterType;

    /**
     * 限流器
     */
    private final List<RRateLimiter> rateLimiters = new ArrayList<>();


    @NotNull
    public static AiCustomerApiRateLimiter getRateLimiter(@NonNull final String corpId,
                                                          @NonNull final AiCustomerApiRateLimiterType rateLimiterType) {

        RedissonClient redisson = ApplicationUtils.getBean(RedissonClient.class);

        return new AiCustomerApiRateLimiter(redisson, corpId, rateLimiterType);
    }

    /**
     * 本地记录一下是否已经存在RateLimiter，避免每次都exist查询，浪费性能
     */
    private static final Set<String> HAS_BEEN_EXISTED_LOCAL_CACHE = new ConcurrentHashSet<>();


    private AiCustomerApiRateLimiter(@NonNull final RedissonClient redisson,
                                     @NonNull final String corpId,
                                     @NonNull final AiCustomerApiRateLimiterType rateLimiterType) {
        this.corpId = corpId;
        this.rateLimiterType = rateLimiterType;

        for (AiCustomerApiRateLimiterType.RateRule rateRule : rateLimiterType.getRateRules()) {
            final String limiterKeyTemplate = rateRule.getRate_limiter_key_prefix_template();
            final String limiterKey = String.format(limiterKeyTemplate, corpId);

            RRateLimiter rateLimiter = redisson.getRateLimiter(limiterKey);
            if (!HAS_BEEN_EXISTED_LOCAL_CACHE.contains(limiterKey)) {
                if (!rateLimiter.isExists()) { // 不包含，不一定是真的不存在，需要判断一下
                    rateLimiter.setRate(RateType.OVERALL, rateRule.getRate(), rateRule.getRateInterval(), rateRule.getRateIntervalUnit());
                }
                HAS_BEEN_EXISTED_LOCAL_CACHE.add(limiterKey);
            }

            rateLimiters.add(rateLimiter);
        }
    }

    private boolean tryAcquire(long timeout, TimeUnit unit) {
        for (RRateLimiter rateLimiter : this.rateLimiters) {
            boolean flag = rateLimiter.tryAcquire(1, timeout, unit);
            if (!flag) {
                return false;
            }
        }
        return true;
    }

    public void tryAcquireOrThrowEx(WMonitorEnum wMonitor) {
        tryAcquireOrThrowEx(1, TimeUnit.SECONDS, wMonitor);
    }

    private void tryAcquireOrThrowEx(long timeout, TimeUnit unit,
                                     @Nullable WMonitorEnum wMonitorEnum) {

        boolean rateLimiterResult = tryAcquire(timeout, unit);

        if (!rateLimiterResult) {
            if (ObjectUtils.notNull(wMonitorEnum)) {
                WMonitor.sum(wMonitorEnum.getAttribute(), 1);
            }

            String logInfo = String.format("juzi api freq out of limit, corp id = %s" +
                            "business = %s, curr available permits = %s",
                    corpId, rateLimiterType, JacksonUtils.format(getAvailablePermits()));
            log.error(logInfo);
            ServiceException.throwNotLoggedErrorMsgEx(ResultStatus.LIMITING.getCode(), logInfo);
        }

        log.info("acquire juzi rate permit success, corp id = {}, business = {}, curr available permits = {}",
                corpId, rateLimiterType, JacksonUtils.format(getAvailablePermits()));
    }

    private Map<String, Long> getAvailablePermits() {

        Map<String, Long> result = new LinkedHashMap<>();
        for (RRateLimiter rateLimiter : this.rateLimiters) {
            String rateLimiterName = rateLimiter.getName();
            long availablePermits = rateLimiter.availablePermits();
            result.put(rateLimiterName, availablePermits);
        }

        return result;
    }
}