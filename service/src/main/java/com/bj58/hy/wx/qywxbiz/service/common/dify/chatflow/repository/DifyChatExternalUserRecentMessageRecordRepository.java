package com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.repository;

import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.aop.RedisLockWrapper;
import com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.bo.DifyMessageChatRecordBo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Description: 聊天记录的缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DifyChatExternalUserRecentMessageRecordRepository {

    private final RedissonClient redisson;

    private static final String CHAT_MESSAGES_KEY_TEMPLATE = "Dify:ExternalUserChatMessages:%s:%s:%s";

    @RedisLockWrapper(
            value = "'DifyAiExternalUserChatMessages' + #corpId + #userId + #externalUserId + ':LOCK'",
            spel = true)
    public List<DifyMessageChatRecordBo> getRecentChatMessagesThenClear(@NonNull final String corpId,
                                                                        @NonNull final String userId,
                                                                        @NonNull final String externalUserId) {
        String key = String.format(CHAT_MESSAGES_KEY_TEMPLATE, corpId, userId, externalUserId);

        RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);

        List<DifyMessageChatRecordBo> result = JacksonUtils.parse(bucket.get(), new TypeReference<List<DifyMessageChatRecordBo>>() {
        });

        bucket.delete();

        return result;
    }

    @RedisLockWrapper(
            value = "'DifyAiExternalUserChatMessages' + #corpId + #userId + #externalUserId + ':LOCK'",
            spel = true)
    public void saveRecentChatMessages(@NonNull final String corpId,
                                       @NonNull final String userId,
                                       @NonNull final String externalUserId,
                                       @NonNull final DifyMessageChatRecordBo message) {
        RBucket<String> messageBucket = redisson.getBucket(
                String.format(CHAT_MESSAGES_KEY_TEMPLATE, corpId, userId, externalUserId),
                StringCodec.INSTANCE);

        List<DifyMessageChatRecordBo> res = Lists.newArrayList();
        if (ObjectUtils.notEmpty(messageBucket.get())) {
            res = JacksonUtils.parse(messageBucket.get(), new TypeReference<List<DifyMessageChatRecordBo>>() {
            });
        }
        res.add(message);
        messageBucket.set(JacksonUtils.format(res));
        messageBucket.expire(1, TimeUnit.HOURS);
    }
}
