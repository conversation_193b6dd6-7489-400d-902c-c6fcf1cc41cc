package com.bj58.hy.wx.qywxbiz.interfaces.scf.jiafu;

import cn.hutool.core.lang.UUID;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywx.contract.ICorpUserService;
import com.bj58.hy.wx.qywx.contract.IGroupChatService;
import com.bj58.hy.wx.qywx.contract.dto.group_chat.*;
import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.InviteExternalUserToGroupResp;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.GroupChatRemoteService;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.IntegerCodec;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 群成员邀请事务服务
 */
@Slf4j
@Service
public class GroupMemberInviteTransactionService {

    @SCFClient(lookup = IGroupChatService.SCF_URL)
    private IGroupChatService groupChatService;

    @SCFClient(lookup = ICorpUserService.SCF_URL)
    private ICorpUserService corpUserService;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private GroupChatRemoteService groupChatRemoteService;

    // 群成员数量上限为180人
    private static final int MAX_GROUP_MEMBER_COUNT = 180;

    // Redis中存储群成员数量上限的Key
    private static final String MAX_GROUP_MEMBER_COUNT_KEY = "GROUP_MEMBER_MAX_COUNT:%s";

    // 群主所在部门ID列表
    private static final List<Integer> GROUP_OWNER_DEPT_IDS = Arrays.asList(103, 61, 63, 64, 67);

    // Redis缓存Key前缀
    private static final String GROUP_OWNER_CACHE_KEY = "GROUP_OWNER_LIST:%s";

    // 城市群ID列表缓存Key
    private static final String CITY_GROUP_IDS_CACHE_KEY = "CITY_GROUP_IDS:%s:%s";

    // 结果ID与城市名称的映射缓存Key
    private static final String RESULT_CITY_MAPPING_KEY = "RESULT_CITY_MAPPING:%s";

    // 默认城市名称
    private static final String DEFAULT_CITY_NAME = "北京";

    /**
     * 获取群成员数量上限，优先从Redis缓存获取，缓存不存在则使用默认值
     */
    private int getMaxGroupMemberCount(String corpId) {
        // 尝试从缓存获取
        String cacheKey = String.format(MAX_GROUP_MEMBER_COUNT_KEY, corpId);
        RBucket<Integer> bucket = redisson.getBucket(cacheKey, IntegerCodec.INSTANCE);
        Integer maxCount = bucket.get();

        if (ObjectUtils.isNotEmpty(maxCount)) {
            log.info("从Redis获取群成员数量上限: corpId={}, maxCount={}", corpId, maxCount);
            return maxCount;
        }

        // 缓存不存在或解析失败，使用默认值
        log.info("使用默认群成员数量上限: corpId={}, defaultMaxCount={}", corpId, MAX_GROUP_MEMBER_COUNT);
        return MAX_GROUP_MEMBER_COUNT;
    }

    /**
     * 将外部联系人拉入群聊（事务方法）
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<InviteExternalUserToGroupResp> inviteExternalUserToGroup(String corpId, String userId, String externalUserId, String cityName) {
        try {
            // 处理城市名称，如果未传入则使用默认值
            if (StringUtils.isEmpty(cityName)) {
                cityName = DEFAULT_CITY_NAME;
                log.info("未指定城市名称，使用默认城市: corpId={}, userId={}, city={}",
                        corpId, userId, cityName);
            }

            // 0. 获取群主
            List<String> groupOwnerList = getGroupOwnerList(corpId);
            if (ObjectUtils.isEmpty(groupOwnerList)) {
                log.error("未找到群主: corpId={}, userId={}",
                        corpId, userId);
                return Result.failure("未找到群主");
            }

            // 1. 获取群主创建的所有群聊，找到人数小于180人的群，并且包含当前userId
            QueryGroupsByConditionsReq listReq = new QueryGroupsByConditionsReq();
            listReq.setOwnerUserIds(groupOwnerList);
            listReq.setMaxMemberCount(getMaxGroupMemberCount(corpId));
            listReq.setMemberUserId(userId);
            listReq.setCorpId(corpId);

            // 从缓存中获取城市对应的群ID列表
            List<String> cityGroupIds = getCityGroupIds(corpId, cityName);
            if (ObjectUtils.isEmpty(cityGroupIds)) {
                // 缓存中未找到城市群ID列表，需要建群
                return createNewGroupAndInvite(corpId, userId, externalUserId, cityName);
            }

            listReq.setChatIds(cityGroupIds);
            log.info("使用缓存中的城市群ID列表: corpId={}, city={}, groupCount={}",
                    corpId, cityName, cityGroupIds.size());

            // 使用并发流查询群信息，检查用户是否已在群中
            List<String> userInChatIds = cityGroupIds.parallelStream()
                    .map(chatId -> {
                        try {
                            ExternalContactGroupChatSnapshotResp groupInfo =
                                    groupChatRemoteService.getExternalContactGroupChatInfoFromSnapshot(corpId, chatId);
                            if (ObjectUtils.isNotEmpty(groupInfo) && ObjectUtils.isNotEmpty(groupInfo.getMembers())) {
                                // 检查外部用户是否在群成员列表中
                                boolean userInGroup = groupInfo.getMembers().stream()
                                        .anyMatch(member -> {
                                            // 首先检查用户ID是否匹配
                                            if (!externalUserId.equals(member.getUserId())) {
                                                return false;
                                            }
                                            // 检查成员类型，type=2表示外部联系人，为了兼容性也检查type为null的情况
                                            Integer memberType = member.getMemberType();
                                            return memberType == null || memberType == 2;
                                        });
                                return userInGroup ? chatId : null;
                            }
                        } catch (Exception e) {
                            log.warn("查询群信息失败: corpId={}, chatId={}, error={}", corpId, chatId, e.getMessage());
                        }
                        return null;
                    })
                    .filter(ObjectUtils::isNotEmpty)
                    .collect(Collectors.toList());

            if (ObjectUtils.isNotEmpty(userInChatIds)) {
                log.info("外部联系人已在群聊中: corpId={}, externalUserId={}, chatIds={}",
                        corpId, externalUserId, userInChatIds);
                return Result.build(100, "外部联系人已在群聊中");
            }

            Result<QueryGroupsByConditionsResp> listResult = groupChatService.queryGroupsByConditions(listReq);
            if (!listResult.isSuccess()) {
                log.error("获取群聊列表失败: corpId={}, groupOwnerId={}, error={}",
                        corpId, userId, listResult.getCode());
                return Result.failure("获取群聊列表失败");
            }

            QueryGroupsByConditionsResp listResp = listResult.getData();
            List<QueryGroupsByConditionsResp.GroupInfo> chatList = listResp.getGroups();

            // 2. 如果没有群聊，则创建新群
            if (ObjectUtils.isEmpty(chatList)) {
                return createNewGroupAndInvite(corpId, userId, externalUserId, cityName);
            }

            // 3. 可用的群进行邀请
            return inviteToExistingGroup(corpId, userId, externalUserId, chatList.get(0).getChatId());

        } catch (Exception e) {
            log.error("邀请外部联系人到群聊失败", e);
            return Result.failure("邀请外部联系人到群聊失败: " + e.getMessage());
        }
    }

    /**
     * 获取群主列表，优先从Redis缓存获取，缓存不存在则查询服务并缓存结果
     */
    private List<String> getGroupOwnerList(String corpId) {
        // 尝试从缓存获取
        String cacheKey = String.format(GROUP_OWNER_CACHE_KEY, corpId);
        RBucket<String> bucket = redisson.getBucket(cacheKey, StringCodec.INSTANCE);
        String cachedData = bucket.get();

        if (StringUtils.isNotEmpty(cachedData)) {
            log.info("从缓存获取群主列表: corpId={}", corpId);
            return Arrays.stream(cachedData.split(",")).collect(Collectors.toList());
        }

        // 缓存不存在，查询服务
        List<String> allGroupOwners = new ArrayList<>();
        for (Integer deptId : GROUP_OWNER_DEPT_IDS) {
            Result<List<String>> ownersResult = corpUserService.getByDepartmentId(corpId, deptId);
            if (ownersResult.isSuccess() && !ObjectUtils.isEmpty(ownersResult.getData())) {
                allGroupOwners.addAll(ownersResult.getData());
            }
        }

        if (!allGroupOwners.isEmpty()) {
            // 缓存查询结果
            String cacheValue = String.join(",", allGroupOwners);
            bucket.set(cacheValue);
            log.info("缓存群主列表: corpId={}, count={}", corpId, allGroupOwners.size());
        }

        return allGroupOwners;
    }

    /**
     * 获取城市对应的群ID列表，优先从Redis缓存获取
     */
    private List<String> getCityGroupIds(String corpId, String cityName) {
        // 尝试从缓存获取
        String cacheKey = String.format(CITY_GROUP_IDS_CACHE_KEY, corpId, cityName);
        RBucket<String> bucket = redisson.getBucket(cacheKey, StringCodec.INSTANCE);
        String cachedData = bucket.get();

        if (StringUtils.isNotEmpty(cachedData)) {
            log.info("从缓存获取城市群ID列表: corpId={}, city={}", corpId, cityName);
            return Arrays.stream(cachedData.split(",")).collect(Collectors.toList());
        }

        // 缓存不存在，返回空列表
        return new ArrayList<>();
    }

    /**
     * 更新城市对应的群ID列表缓存
     */
    private void updateCityGroupIds(String corpId, String cityName, String chatId) {
        // 获取现有缓存
        List<String> existingGroupIds = getCityGroupIds(corpId, cityName);

        // 如果群ID已存在，则不重复添加
        if (existingGroupIds.contains(chatId)) {
            return;
        }

        // 添加新的群ID
        existingGroupIds.add(chatId);

        // 更新缓存
        String cacheKey = String.format(CITY_GROUP_IDS_CACHE_KEY, corpId, cityName);
        RBucket<String> bucket = redisson.getBucket(cacheKey, StringCodec.INSTANCE);
        String cacheValue = String.join(",", existingGroupIds);
        bucket.set(cacheValue);

        log.info("更新城市群ID列表缓存: corpId={}, city={}, chatId={}, totalCount={}",
                corpId, cityName, chatId, existingGroupIds.size());
    }

    /**
     * 存储结果ID与城市名称的映射关系
     */
    private void storeResultCityMapping(String resultId, String corpId, String cityName) {
        String cacheKey = String.format(RESULT_CITY_MAPPING_KEY, resultId);
        RBucket<String> bucket = redisson.getBucket(cacheKey, StringCodec.INSTANCE);
        String cacheValue = String.format("%s:%s", corpId, cityName);
        bucket.set(cacheValue);

        log.info("存储结果ID与城市名称的映射关系: resultId={}, corpId={}, city={}",
                resultId, corpId, cityName);
    }

    /**
     * 根据结果ID更新城市群ID列表缓存（供GroupEventCallback调用）
     */
    public void updateCityGroupIdsByResultId(String resultId, String chatId) {
        // 获取结果ID对应的企业ID和城市名称
        String cacheKey = String.format(RESULT_CITY_MAPPING_KEY, resultId);
        RBucket<String> bucket = redisson.getBucket(cacheKey, StringCodec.INSTANCE);
        String cachedData = bucket.get();

        if (StringUtils.isEmpty(cachedData)) {
            log.warn("未找到结果ID对应的企业ID和城市名称: resultId={}", resultId);
            return;
        }

        String[] parts = cachedData.split(":");
        if (parts.length != 2) {
            log.warn("结果ID与城市名称的映射格式错误: resultId={}, data={}", resultId, cachedData);
            return;
        }

        String corpId = parts[0];
        String cityName = parts[1];

        // 更新城市群ID列表缓存
        updateCityGroupIds(corpId, cityName, chatId);

        // 删除映射关系（已使用）
        bucket.delete();

        log.info("根据结果ID更新城市群ID列表缓存: resultId={}, corpId={}, city={}, chatId={}",
                resultId, corpId, cityName, chatId);
    }

    /**
     * 获取群名称
     */
    private String getGroupName(String corpId, String cityName) {
        // 获取城市对应的群ID列表
        List<String> cityGroupIds = getCityGroupIds(corpId, cityName);
        
        // 使用列表大小加1作为新群号
        int groupNumber = cityGroupIds.size() + 1;
        
        log.info("使用城市群ID列表的大小生成群号: corpId={}, city={}, existingGroups={}, newGroupNumber={}",
                corpId, cityName, cityGroupIds.size(), groupNumber);
        
        // 返回格式化的群名称
        return String.format("%s-58到家派单学习群【禁言】%d", cityName, groupNumber);
    }

    /**
     * 邀请外部联系人到现有群聊
     */
    private Result<InviteExternalUserToGroupResp> inviteToExistingGroup(String corpId, String userId, String externalUserId, String chatId) throws Exception {
        AsyncInviteJoinGroupChatReq inviteReq = new AsyncInviteJoinGroupChatReq();
        inviteReq.setCorpId(corpId);
        inviteReq.setUserId(userId);
        inviteReq.setExternalUserId(externalUserId);
        inviteReq.setWecomChatId(chatId);
        inviteReq.setResultId(UUID.randomUUID().toString());

        Result<String> inviteResult = groupChatService.asyncInviteJoinGroupChat(inviteReq);
        if (!inviteResult.isSuccess()) {
            log.error("邀请外部联系人加入群聊失败: corpId={}, userId={}, externalUserId={}, chatId={}, error={}",
                    corpId, userId, externalUserId, chatId, inviteResult.getCode());
            return Result.failure(inviteResult.getMsg());
        } else {
            log.info("成功邀请外部联系人加入群聊: corpId={}, userId={}, externalUserId={}, chatId={}",
                    corpId, userId, externalUserId, chatId);
        }
        InviteExternalUserToGroupResp resp = new InviteExternalUserToGroupResp();
        resp.setResultId(inviteReq.getResultId());
        resp.setChatId(chatId);
        return Result.success(resp);
    }

    /**
     * 创建新群并邀请外部联系人
     */
    private Result<InviteExternalUserToGroupResp> createNewGroupAndInvite(String corpId, String groupOwnerId, String externalUserId, String cityName) throws Exception {
        // 1. 创建新群
        CreateGroupChatReq createReq = new CreateGroupChatReq();
        createReq.setBanChangeName(true);

        createReq.setCorpId(corpId);
        createReq.setGroupOwnerUserId(groupOwnerId);

        // 获取群名称
        String groupName = getGroupName(corpId, cityName);
        createReq.setName(groupName);
        createReq.setGreeting("欢迎！"); // 可以根据业务需要设置欢迎语

        // 添加外部联系人
        List<String> externalUserIds = new ArrayList<>();
        externalUserIds.add(externalUserId);
        createReq.setExternalUserIds(externalUserIds);

        List<String> allUserIds = this.getGroupOwnerList(corpId);
        allUserIds.remove(groupOwnerId);
        createReq.setMemberIds(allUserIds);
        createReq.setBizLine(BizSceneEnum.家服_阿姨拉群加企微.getLineId());
        createReq.setBizScene(BizSceneEnum.家服_阿姨拉群加企微.getSceneId());
        createReq.setBizCustomizedResultFilterKey(BizSceneEnum.家服_阿姨拉群加企微.getSceneName());

        Result<String> createResult = groupChatService.asyncCreateGroupChat(createReq);
        if (!createResult.isSuccess()) {
            log.error("创建新群失败: corpId={}, groupOwnerId={}, externalUserId={}, cityName={}, error={}",
                    corpId, groupOwnerId, externalUserId, cityName, createResult.getCode());
            return Result.failure(createResult.getMsg());
        }

        String resultId = createResult.getData();

        // 存储结果ID与城市名称的映射关系，供GroupEventCallback使用
        storeResultCityMapping(resultId, corpId, cityName);

        log.info("成功创建新群并邀请外部联系人: corpId={}, groupOwnerId={}, externalUserId={}, resultId={}, cityName={}, groupName={}",
                corpId, groupOwnerId, externalUserId, resultId, cityName, groupName);
        InviteExternalUserToGroupResp resp = new InviteExternalUserToGroupResp();
        resp.setResultId(createResult.getData());

        return Result.success(resp);
    }
} 