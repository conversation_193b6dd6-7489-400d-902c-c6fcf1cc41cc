
package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 *
 */
@Data
public class ChatflowRequest {

    private String user;

    /**
     * 会话唯一标识
     * 第一次请求时传空
     */
    private String conversationId;

    /**
     * 完整的问题
     */
    private String question;

    /**
     * 允许传入 App 定义的各变量值。
     */
    private Map<String, String> inputs = new LinkedHashMap<>();

}
