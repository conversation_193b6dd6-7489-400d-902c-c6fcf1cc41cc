package com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IJuziApiService;
import com.bj58.hy.wx.qywx.contract.dto.group.GetGroupResp;
import com.bj58.hy.wx.qywx.contract.enums.AccountLimitRestrictTypeEnum;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.AccountLimitBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.AbstractMeishiMessageHandler;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.MeishiMessageBo;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziEventHandler;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.enums.JuziAccountAlarmEnum;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/11/15 10:17
 */
@Component
public class AccountLimitMonitorEventHandler extends AbstractJuziEventHandler {

    @Autowired
    private RedissonClient redisson;

    @SCFClient(lookup = IJuziApiService.SCF_URL)
    private IJuziApiService juziApiService;

    @Autowired
    private ObjectProvider<AbstractMeishiMessageHandler> meishiMessageHandlers;

    @Override
    public void process(@NonNull JSONObject event) {

        AccountLimitBo eventBo = JSON.toJavaObject(event, AccountLimitBo.class);

        AccountLimitRestrictTypeEnum limitType = AccountLimitRestrictTypeEnum.getEnum(
                eventBo.getBody().getRestrictType()
        );
        if (ObjectUtils.isNull(limitType)) {
            return; // 不会发生
        }

        MeishiMessageBo meishiBo = buildMeishiMessageBo(limitType, eventBo);

        for (AbstractMeishiMessageHandler meishiMessageHandler : meishiMessageHandlers) {
            try {
                meishiMessageHandler.process(meishiBo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        RBucket<String> limitBucket = redisson.getBucket(
                String.format("AccountLimitMonitor_%s_%s_%s",
                        eventBo.getEvent(),
                        eventBo.getBody().getBotId(),
                        eventBo.getBody().getRestrictType()),
                StringCodec.INSTANCE);

        RBucket<Long> bucket = redisson.getBucket("AccountLimitMonitorFreq", LongCodec.INSTANCE);
        Long freq = bucket.get();
        if (ObjectUtils.isNull(freq)) {
            freq = 10L;
        }
        if (freq < 0) {
            freq = 10L;
        }

        limitBucket.set(
                String.format("%s_%s_%s",
                        System.currentTimeMillis(),
                        eventBo.getBody().getBotId(),
                        limitType),
                freq, TimeUnit.MINUTES);
    }

    private @NotNull MeishiMessageBo buildMeishiMessageBo(@NonNull final AccountLimitRestrictTypeEnum limitType,
                                                          @NonNull final AccountLimitBo eventBo) {
        Map<String, String> contentMap = new LinkedHashMap<>();

        contentMap.put("通知内容：", limitType.getDesc());

        try {
            Result<String> wecomUserIdResult = juziApiService.getUserIdByJuZiBotId(
                    "default", eventBo.getBody().getBotId());
            if (wecomUserIdResult.isSuccess()) {
                String wecomUserId = wecomUserIdResult.getData();

                contentMap.put("托管账号企业微信员工id：", wecomUserId);
            } else {
                contentMap.put("托管账号id：", eventBo.getBody().getBotId());
            }
        } catch (Exception e) {
            log.error("get user id by bot id error, bot id = {}", eventBo.getBody().getBotId(), e);
            contentMap.put("托管账号id：", eventBo.getBody().getBotId());
        }

        try {
            Result<GetGroupResp> groupResult = juziApiService.getJuZiGroupInfo(
                    "default", eventBo.getBody().getGroupId());

            if (groupResult.isSuccess()) {
                contentMap.put("小组名称：", groupResult.getData().getName());
            } else {
                contentMap.put("小组id：", eventBo.getBody().getGroupId());
            }
        } catch (Exception e) {
            log.error("get group info by group id error, group id = {}", eventBo.getBody().getGroupId(), e);
            contentMap.put("小组id：", eventBo.getBody().getGroupId());
        }

        // 3.推送美事消息
        MeishiMessageBo meishiBo = new MeishiMessageBo();
        meishiBo.setContentMap(contentMap);
        meishiBo.setTitle(JuziAccountAlarmEnum.托管账号被限制回调.alarmTitle);

        // 判断是否需要 发送到特定的美事群里
        RMap<String, String> rMap;
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            rMap = redisson.getMap("ALARM_MEISHI_GROUP_ID", StringCodec.INSTANCE);
        } else {
            rMap = redisson.getMap("ALARM_MEISHI_GROUP_ID_SANDBOX", StringCodec.INSTANCE);
        }

        for (Map.Entry<String, String> entry : contentMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            String ruleStr = key + value;
            String specialGroupId = rMap.get(ruleStr);

            if (ObjectUtils.notEmpty(specialGroupId)) {
                log.info("发现特定的告警群组，规则 = {}, 对应的群id = {}", ruleStr, specialGroupId);
                meishiBo.getSpecialGroupIds().add(specialGroupId); // set, 不需要手动去重
            }
        }

        return meishiBo;
    }

    @Override
    public boolean matched(@NonNull JSONObject event) {
        if (!"Product".equals(System.getenv("WCloud_Env"))) {
            return false; // 沙箱不需要
        }

        if (!JuziAccountAlarmEnum.matched(event, JuziAccountAlarmEnum.托管账号被限制回调.getEvent())) {
            return false;
        }

        AccountLimitBo eventBo = JSON.toJavaObject(event, AccountLimitBo.class);
        if (eventBo == null) {
            return false;
        }

        // 具体事件过滤非报警信息 需要自定义 因为每个事件需要报警的信息不一样
        int limitTypeCode = eventBo.getBody().getRestrictType();

        boolean b = limitTypeCode == AccountLimitRestrictTypeEnum.发送消息.getType()
                || limitTypeCode == AccountLimitRestrictTypeEnum.搜索联系人.getType()
                || limitTypeCode == AccountLimitRestrictTypeEnum.添加联系人.getType()
                || limitTypeCode == AccountLimitRestrictTypeEnum.接受好友请求.getType()
                || limitTypeCode == AccountLimitRestrictTypeEnum.拉人入群.getType()
                || limitTypeCode == AccountLimitRestrictTypeEnum.创建群聊.getType();

        if (!b) {
            return false;
        }

        if (isFiltered(eventBo) || isFreqLimited(eventBo)) {
            return false;
        }

        return true;
    }

    private boolean isFiltered(@NonNull AccountLimitBo eventBo) {
        if (ObjectUtils.isNull(eventBo.getBody())
                || ObjectUtils.isNull(eventBo.getBody().getBotId())
                || ObjectUtils.isEmpty(eventBo.getBody().getGroupId())
        ) {
            return true;
        }

        // 小组过滤
        RMap<String, String> filterGroups =
                redisson.getMap("ACCOUNT_LIMIT_EVENT_ALARM_IGNORE_GROUPS", StringCodec.INSTANCE);
        if (ObjectUtils.notEmpty(filterGroups)) {
            boolean contains = filterGroups.values().stream()
                    .anyMatch(value -> value.equals(eventBo.getBody().getGroupId()));
            if (contains) {
                return true;
            }
        }

        final String botId = eventBo.getBody().getBotId();
        String wecomUserId = "";
        try {
            Result<String> wecomUserIdResult = juziApiService.getUserIdByJuZiBotId(
                    "default", botId);
            if (wecomUserIdResult.isSuccess()) {
                wecomUserId = wecomUserIdResult.getData();
            }
        } catch (Exception e) {
            log.error("get user id by bot id error, bot id = {}", botId, e);
        }

        if (ObjectUtils.isEmpty(wecomUserId)) {
            // 未查询到对应的企业成员id ?
            // return true;

            // 这种暂时也报出来吧~
            return false;
        }

        // 账号过滤
        RSet<String> filterUsers = redisson.getSet("ACCOUNT_LIMIT_EVENT_ALARM_IGNORE_USERS", StringCodec.INSTANCE);
        if (ObjectUtils.notEmpty(filterUsers)) {
            if (filterUsers.contains(wecomUserId)) {
                return true;
            }
        }

        // 获取配置，查询是否需要禁止提醒
        AccountLimitRestrictTypeEnum eventType = AccountLimitRestrictTypeEnum.getEnum(
                eventBo.getBody().getRestrictType()
        );
        if (ObjectUtils.isNull(eventType)) {
            return true; // 不会发生
        }

        try {
            RBucket<Long> config = redisson.getBucket(String.format("AccountLimitMonitor:%s:%s",
                    wecomUserId, eventType.getType()), LongCodec.INSTANCE);
            Long timestamp = config.get();

            if (ObjectUtils.isEmpty(timestamp)) {
                return false; // 没有配置

            } else {
                // 查看配置的截止时间
                if (timestamp > System.currentTimeMillis()) {
                    log.info("当前客服对应类型告警禁用规则启用中，corp user id = {}, alarm type = {}",
                            wecomUserId, eventType);
                    return true;

                } else {
                    log.info("当前客服对应类型告警禁用规则已失效，corp user id = {}, alarm type = {}",
                            wecomUserId, eventType);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("判断客服对应告警类型是否开启时发生错误, event = {}", JacksonUtils.format(eventBo), e);
        }

        return false;
    }

    private boolean isFreqLimited(@NonNull AccountLimitBo eventBo) {
        if (ObjectUtils.isNull(eventBo.getBody())
                || ObjectUtils.isNull(eventBo.getBody().getBotId())
                || ObjectUtils.isEmpty(eventBo.getBody().getGroupId())
        ) {
            return true;
        }

        RBucket<String> limitBucket = redisson.getBucket(
                String.format("AccountLimitMonitor_%s_%s_%s",
                        eventBo.getEvent(),
                        eventBo.getBody().getBotId(),
                        eventBo.getBody().getRestrictType()),
                StringCodec.INSTANCE);

        String limitValue = limitBucket.get();

        if (ObjectUtils.notEmpty(limitValue)) {
            log.warn("当前告警频繁触发已被忽略, event = {}", JacksonUtils.format(eventBo));
            return true;
        }

        return false;
    }

}
