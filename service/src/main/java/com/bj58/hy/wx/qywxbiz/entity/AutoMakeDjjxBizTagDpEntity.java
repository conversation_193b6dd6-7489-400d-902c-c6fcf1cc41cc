package com.bj58.hy.wx.qywxbiz.entity;

import com.bj58.hy.lib.spring.support.jpa.superclass.Identifiable;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * Description:
 *
 * <AUTHOR>
 */
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "t_qywx_djjx_biz_tag_dp", indexes = {
        @Index(name = "idx_58_id", columnList = "user_58_id"),
})
@Getter
public class AutoMakeDjjxBizTagDpEntity extends Identifiable {

    /**
     * 外部联系人 58 id
     */
    @Column(name = "user_58_id", nullable = false)
    private Long externalUser58Id;

    /**
     * 标签组名字
     */
    @Column(name = "tag_group_name", nullable = true)
    private String tagGroupName;

    /**
     * 标签名字
     */
    @Column(name = "tag_name", nullable = true)
    private String tagName;

    /**
     * 操作类型
     * 1. 新增
     * 2. 删除
     */
    @Column(name = "operation_type", nullable = false)
    private int operationType = 1;

}
