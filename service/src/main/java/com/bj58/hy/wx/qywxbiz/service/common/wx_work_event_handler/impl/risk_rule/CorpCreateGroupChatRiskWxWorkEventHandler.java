package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.impl.risk_rule;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.group_chat.ExternalContactGroupChatSnapshotResp;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_chat.CreateExternalChatEventBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.GroupChatRemoteService;
import com.bj58.hy.wx.qywxbiz.service.bo.RateLimiterWrapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RateIntervalUnit;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Description: 企业成员建群的风控规则校验
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CorpCreateGroupChatRiskWxWorkEventHandler extends AbstractCorpGroupChatRiskWxWorkEventHandler {

    @Autowired
    private GroupChatRemoteService groupChatRemoteService;

    @Override
    public void process(final @NonNull CreateExternalChatEventBo event) {
        String chatId = event.getChatId();
        if (ObjectUtils.isEmpty(chatId)) {
            return;
        }

        ExternalContactGroupChatSnapshotResp groupChatInfoFromSnapshot = groupChatRemoteService.getExternalContactGroupChatInfoFromSnapshot(event.getCorpId(), chatId);
        if (ObjectUtils.isEmpty(groupChatInfoFromSnapshot)) {
            return;
        }

        // 检查频率限制
        checkHasBeenTriggeredCorpFreqLimit(event.getCorpId());
        checkHasBeenTriggeredCorpUserFreqLimit(event.getCorpId(), groupChatInfoFromSnapshot);
    }

    private void checkHasBeenTriggeredCorpFreqLimit(@NonNull final String corpId) {
        // 判断当前企业是否超过了建群的频率限制
        RateLimiterWrapper rateLimiter = getCorpCreateGroupChatRateLimiter(corpId);
        if (rateLimiter.couldExecute()) {
            return; // 没有超过频率限制
        }

        long rate = rateLimiter.getRate();
        long rateInterval = rateLimiter.getRateInterval();
        RateIntervalUnit rateIntervalUnit = rateLimiter.getRateIntervalUnit();

        log.info("[{}]当前企业建群频率过高, rate = {}, rate interval = {}, rate interval unit = {}", corpId,
                rate, rateInterval, rateIntervalUnit.name());

        RBucket<String> bucket = redisson.getBucket(
                String.format("CorpCreateGroupChatFreqRisk:%s:SEND_MEISHI_FLAG", corpId), StringCodec.INSTANCE);
        if (bucket.isExists()) {
            return;
        }

        lockSupport.executeWithoutResult(
                bucket.getName() + "_LOCK_",
                () -> {
                    if (bucket.isExists()) { // 已经发送过告警，不再重复发送
                        return;
                    }

                    Map<String, String> content = new LinkedHashMap<>();
                    content.put("通知内容：", "当前企业建群频率过高，请尽快确认情况");
                    content.put("企业主体：", corpId);
                    content.put("告警频率：", String.format("%s %s内(滚动时间) 建群 %s个",
                            rateInterval, rateIntervalUnit.name().toLowerCase(), rate));
                    riskAlarmComponent.alarm("企业微信风控通知", content);

                    bucket.set("1", riskRuleComponent.getAlarmTimeInterval(), TimeUnit.SECONDS);
                }
        );
    }

    private void checkHasBeenTriggeredCorpUserFreqLimit(@NonNull final String corpId, final @NonNull ExternalContactGroupChatSnapshotResp info) {
        // 判断当前企业成员是否超过了建群的频率限制
        RateLimiterWrapper rateLimiter = getCorpUserCreateGroupChatRateLimiter(corpId, info.getOwner());
        if (rateLimiter.couldExecute()) {
            return; // 没有超过频率限制
        }

        long rate = rateLimiter.getRate();
        long rateInterval = rateLimiter.getRateInterval();
        RateIntervalUnit rateIntervalUnit = rateLimiter.getRateIntervalUnit();

        log.info("[{},{}]当前企业成员建群频率过高, rate = {}, rate interval = {}, rate interval unit = {}",
                corpId, info.getOwner(),
                rate, rateInterval, rateIntervalUnit.name());

        RBucket<String> bucket = redisson.getBucket(
                String.format("CorpUserCreateGroupChatFreqRisk:%s:%s:SEND_MEISHI_FLAG", corpId, info.getOwner()), StringCodec.INSTANCE);
        if (bucket.isExists()) {
            return;
        }

        lockSupport.executeWithoutResult(
                bucket.getName() + "_LOCK_",
                () -> {
                    if (bucket.isExists()) { // 已经发送过告警，不再重复发送
                        return;
                    }

                    Map<String, String> content = new LinkedHashMap<>();
                    content.put("通知内容：", "当前企业成员建群频率过高，请尽快确认情况");
                    content.put("企业主体：", corpId);
                    content.put("企业成员：", info.getOwner());
                    content.put("告警频率：", String.format("%s %s内(滚动时间) 建群 %s个",
                            rateInterval, rateIntervalUnit.name().toLowerCase(), rate));
                    riskAlarmComponent.alarm("企业微信风控通知", content);

                    bucket.set("1", riskRuleComponent.getAlarmTimeInterval(), TimeUnit.SECONDS);
                }
        );
    }

} 