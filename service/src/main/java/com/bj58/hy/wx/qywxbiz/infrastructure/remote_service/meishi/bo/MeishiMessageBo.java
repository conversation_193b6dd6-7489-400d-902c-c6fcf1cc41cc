package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/26 10:54
 */
@Data
public class MeishiMessageBo {

    /**
     * 是否有指定的 告警美事人？
     */
    @JSONField(name = "specialBspIds") // fastjson
    @JsonProperty("specialBspIds")// jackson
    private Set<String> specialBspIds = new LinkedHashSet<>();

    /**
     * 是否有指定的 告警美事群组？
     */
    @JSONField(name = "specialGroupIds") // fastjson
    @JsonProperty("specialGroupIds")// jackson
    private Set<String> specialGroupIds = new LinkedHashSet<>();

    @JSONField(name = "title") // fastjson
    @JsonProperty("title")// jackson
    private String title;


    @JSONField(name = "contentMap") // fastjson
    @JsonProperty("contentMap")// jackson
    private Map<String, String> contentMap;

}
