package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IJuziApiService;
import com.bj58.hy.wx.qywx.contract.dto.corp_user.CorpUserDetailForThirdResp;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class JuziApiRemoteService {

    @SCFClient(lookup = IJuziApiService.SCF_URL)
    private IJuziApiService juziApiService;

    public CorpUserDetailForThirdResp getUserInfoByUserId(@NonNull final String corpId,
                                                          @NonNull final String userId) {

        Result<List<CorpUserDetailForThirdResp>> contactRelationshipResult;
        try {
            contactRelationshipResult = juziApiService.getJuZiUserInfoByUserId(corpId, userId);

        } catch (Exception e) {
            log.error("get juzi user id error, " +
                    "corp id = {}, user id = {}", corpId, userId, e);
            return null;
        }

        if (ObjectUtils.isNull(contactRelationshipResult) ||
                contactRelationshipResult.isFailed()) {
            log.error("get juzi user id error, " +
                    "corp id = {}, user id = {}", corpId, userId);
            return null;
        }

        return contactRelationshipResult.getData().get(0);
    }

}
