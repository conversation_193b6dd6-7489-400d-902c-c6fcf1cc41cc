package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IMessageService;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziGroupChatContentRecord;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywxbiz.entity.JuziSingleChatContentRecordAiBizInfo;
import com.bj58.hy.wx.qywxbiz.entity.enums.AiType;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.constants.WMonitorEnum;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.enums.MessageMarkEnum;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.bj58.wmonitor.javaclient.WMonitor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class ChatContentRemoteService {

    @SCFClient(lookup = IMessageService.SCF_URL)
    private IMessageService iMessageService;


    public void recordSingleChatBizInfo(@NonNull final String messageId,
                                        @NonNull final JuziSingleChatContentRecordAiBizInfo aiBizInfo) {

        // 记录AI相关信息
        log.info("record single chat ai biz info, message id = {}, ai biz info = {}",
                messageId, JacksonUtils.format(aiBizInfo));

        if (!"Product".equals(System.getenv("WCloud_Env"))) {
            return; // 线下环境无需记录，避免覆盖
        }

        if (ObjectUtils.isNull(aiBizInfo.getAiType())) {
            throw new RuntimeException("not found ai type");
        }

        try {
            AiType aiType = aiBizInfo.getAiType();

            Result<Boolean> result = iMessageService.recordBizInfo(messageId, aiType.name(), JacksonUtils.format(aiBizInfo), true);

            log.info("record single chat ai biz info success, message id = {}, ai biz info = {}, biz result = {}",
                    messageId, JacksonUtils.format(aiBizInfo), JacksonUtils.format(result));

        } catch (Exception e) {
            log.error("record chat chat content ai biz info error, message id = {}, biz info = {}",
                    messageId, JacksonUtils.format(aiBizInfo), e);

            WMonitor.sum(WMonitorEnum.RECORD_SINGLE_CHAT_CONTENT_BIZ_INFO_ERROR.getAttribute(), 1);
        }
    }

    public void recordSingleChatBizInfo(@NonNull final String messageId,
                                        @NonNull final String key,
                                        @NonNull final String value) {
        // 记录AI相关信息
        log.info("record single chat biz info, message id = {}, key = {}, value = {}",
                messageId, key, value);

        try {

            Result<Boolean> result = iMessageService.recordBizInfo(messageId, key, value, true);

            log.info("record single chat biz info success, message id = {}, key = {},value = {} , biz result = {}",
                    messageId, key, value, JacksonUtils.format(result));

        } catch (Exception e) {
            log.error("record chat chat content biz info error, message id = {}, key = {}, value = {}",
                    messageId, key, value, e);

            WMonitor.sum(WMonitorEnum.RECORD_SINGLE_CHAT_CONTENT_BIZ_INFO_ERROR.getAttribute(), 1);
        }
    }

    /**
     * 标记SOP消息
     * SOP消息通过sendSource=7标记，不参与倒计时逻辑
     *
     * @param messageId 消息ID
     */
    public void recordSopMessage(@NonNull final String messageId) {
        log.info("record sop message, message id = {}", messageId);

        try {
            // 标记为SOP消息
            recordSingleChatBizInfo(messageId, "MESSAGE_TYPE_MARK", MessageMarkEnum.SOP.getName());

        } catch (Exception e) {
            log.error("record sop message error, message id = {}", messageId, e);
        }
    }

    public JuziSingleChatContentRecord getSingleChatContentRecord(@NonNull final String messageId) {

        Result<JuziSingleChatContentRecord> result;
        try {
            result = iMessageService.getSingleChatContentRecord(messageId);

        } catch (Exception e) {
            log.error("get single chat content error, message id = {}", messageId, e);
            return null;
        }

        if (ObjectUtils.isNull(result) || result.isFailed() || ObjectUtils.isNull(result.getData())) {
            log.error("not found single chat content, message id = {}", messageId);
            return null;
        }

        return result.getData();
    }

    public JuziGroupChatContentRecord getGroupChatContentRecord(@NonNull final String messageId) {

        Result<JuziGroupChatContentRecord> result;
        try {
            result = iMessageService.getGroupChatContentRecord(messageId);

        } catch (Exception e) {
            log.error("get group chat content error, message id = {}", messageId, e);
            return null;
        }

        if (ObjectUtils.isNull(result) || result.isFailed() || ObjectUtils.isNull(result.getData())) {
            log.error("not found group chat content, message id = {}", messageId);
            return null;
        }

        return result.getData();
    }

}
