package com.bj58.hy.wx.qywxbiz.repository;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.entity.QWxWorkContactWayBizAccountConfEntity;
import com.bj58.hy.wx.qywxbiz.entity.WxWorkContactWayBizAccountConfEntity;
import com.bj58.hy.wx.qywxbiz.repository.bo.PagedQueryWxWorkContactWayBizAccountConf;
import com.bj58.hy.wx.qywxbiz.repository.jpa.WxWorkContactWayBizAccountConfJpaRepository;
import com.querydsl.core.QueryResults;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.*;

/**
 *
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WxWorkContactWayBizAccountConfRepository {

    private final EntityManager entityManager;

    protected final WxWorkContactWayBizAccountConfJpaRepository repository;

    @Transactional(readOnly = true)
    public List<WxWorkContactWayBizAccountConfEntity> getAvailableUser(@NonNull final Integer bizLine,
                                                                       @NonNull final Set<Integer> bizScene,
                                                                       @NonNull final String corpId,
                                                                       @Nullable final Integer cityId) {

        JPAQuery<WxWorkContactWayBizAccountConfEntity> query = new JPAQueryFactory(entityManager)
                .selectFrom(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity)
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.bizLine.eq(bizLine))
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.bizScene.in(bizScene))
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.corpId.eq(corpId))
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.state.eq(1)); // 仅看有效

        if (ObjectUtils.notNull(cityId)) {
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.cityId.like("%," + cityId + ",%"));
        }

        return query.fetch();
    }

    /**
     * 获取指定业务线的所有可用客服账号（不限制企业ID）
     */
    @Transactional(readOnly = true)
    public List<WxWorkContactWayBizAccountConfEntity> getAllAvailableUsersByBizLine(@NonNull final Integer bizLine) {
        return new JPAQueryFactory(entityManager)
                .selectFrom(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity)
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.bizLine.eq(bizLine))
                .fetch();
    }

    @Transactional(readOnly = true)
    public List<WxWorkContactWayBizAccountConfEntity> listUserByUserIds(@NonNull final Integer bizLine,
                                                                        @NonNull final Set<Integer> bizScene,
                                                                        @NonNull final String corpId,
                                                                        @NonNull final Set<String> userIds) {
        return new JPAQueryFactory(entityManager)
                .selectFrom(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity)
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.bizLine.eq(bizLine))
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.bizScene.in(bizScene))
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.userId.in(userIds))
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.corpId.eq(corpId))
                .fetch();
    }

    /**
     * 根据corpId和userId查询该用户的所有配置记录
     */
    @Transactional(readOnly = true)
    public List<WxWorkContactWayBizAccountConfEntity> findByCorpIdAndUserId(@NonNull final String corpId,
                                                                            @NonNull final String userId) {
        return new JPAQueryFactory(entityManager)
                .selectFrom(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity)
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.corpId.eq(corpId))
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.userId.eq(userId))
                .fetch();
    }

    @Transactional(readOnly = true)
    public QueryResults<WxWorkContactWayBizAccountConfEntity> listUserByParams(@NonNull final PagedQueryWxWorkContactWayBizAccountConf pagedQuery) {

        JPAQuery<WxWorkContactWayBizAccountConfEntity> query = new JPAQueryFactory(entityManager)
                .selectFrom(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity)
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.bizLine.eq(pagedQuery.getBizLine()))
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.bizScene.in(pagedQuery.getBizScenes()))
                .where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.corpId.eq(pagedQuery.getCorpId()))
                .orderBy(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.updateTime.desc())
                .orderBy(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.id.desc())
                .limit(pagedQuery.getLimit())
                .offset(pagedQuery.getOffset());

        if (ObjectUtils.notEmpty(pagedQuery.getUserIds())) {
            // 使用IN查询，只查询指定的userIds
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.userId.in(
                    pagedQuery.getUserIds()
            ));
        }

        if (ObjectUtils.notEmpty(pagedQuery.getNotInUserIds())) {
            // 使用NOT IN查询，排除指定的userIds
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.userId.notIn(
                    pagedQuery.getNotInUserIds()
            ));
        }

        if (ObjectUtils.notNull(pagedQuery.getCityId())) {
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.cityId.like(
                    "%," + pagedQuery.getCityId() + ",%"
            ));
        }

        if (ObjectUtils.notNull(pagedQuery.getAiState())) {
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.aiState.eq(
                    pagedQuery.getAiState()
            ));
        }

        if (ObjectUtils.notNull(pagedQuery.getCreateStartTime())) {
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.createTime.after(
                    pagedQuery.getCreateStartTime()
            ));
        }

        if (ObjectUtils.notNull(pagedQuery.getCreateEndTime())) {
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.createTime.before(
                    pagedQuery.getCreateEndTime()
            ));
        }

        if (ObjectUtils.notNull(pagedQuery.getUpdateStartTime())) {
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.updateTime.after(
                    pagedQuery.getUpdateStartTime()
            ));
        }

        if (ObjectUtils.notNull(pagedQuery.getUpdateEndTime())) {
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.updateTime.before(
                    pagedQuery.getUpdateEndTime()
            ));
        }

        if (ObjectUtils.notNull(pagedQuery.getState())) {
            query.where(QWxWorkContactWayBizAccountConfEntity.wxWorkContactWayBizAccountConfEntity.state.eq(
                    pagedQuery.getState()
            ));
        }

        return query.fetchResults();
    }

    @Transactional
    public WxWorkContactWayBizAccountConfEntity save(WxWorkContactWayBizAccountConfEntity wxWorkContactWayBizAccountConfEntity) {
        return repository.save(wxWorkContactWayBizAccountConfEntity);
    }

    @Transactional(readOnly = true)
    public Optional<WxWorkContactWayBizAccountConfEntity> findById(@NonNull final Long id) {
        return repository.findById(id);
    }

    @Transactional
    public void deleteById(@NonNull final Long id) {
        repository.deleteById(id);
    }

}
