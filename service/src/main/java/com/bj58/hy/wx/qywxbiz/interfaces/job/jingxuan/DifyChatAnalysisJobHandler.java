package com.bj58.hy.wx.qywxbiz.interfaces.job.jingxuan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.entity.WxWorkContactWayBizAccountConfEntity;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.DifyRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowRequest;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowResponse;
import com.bj58.hy.wx.qywxbiz.repository.DifyChatAnalysisResultRepository;
import com.bj58.hy.wx.qywxbiz.repository.JuziSingleChatContentRecordDirectRepository;
import com.bj58.hy.wx.qywxbiz.repository.WxWorkContactWayBizAccountConfRepository;
import com.bj58.hy.wx.qywxbiz.service.bo.BizLineEnum;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.bj58.job.core.biz.model.ReturnT;
import com.bj58.job.core.handler.IJobHandler;
import com.bj58.job.core.handler.annotation.JobHandler;
import com.bj58.job.core.log.WJobLogger;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 精选客服Dify聊天分析定时任务
 */
@Slf4j
@Component
@JobHandler(value = "DifyChatAnalysisJobHandler")
public class DifyChatAnalysisJobHandler extends IJobHandler {

    @Autowired
    private WxWorkContactWayBizAccountConfRepository wxWorkContactWayBizAccountConfRepository;

    @Autowired
    private JuziSingleChatContentRecordDirectRepository juziSingleChatContentRecordDirectRepository;

    @Autowired
    private DifyRemoteService difyRemoteService;

    @Autowired
    private DifyChatAnalysisResultRepository difyChatAnalysisResultRepository;

    // Dify工作流API Key，实际使用时需要配置
    private static final String DIFY_WORKFLOW_API_KEY = "app-MNKfY8RzFXnfnl767durVTcy";

    // 精选客服固定企业ID
    private static final String JINGXUAN_CORP_ID = "ww5cfa32107e9a1f20";

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String logInfo = "开始执行精选客服Dify聊天分析定时任务";
        log.info(logInfo);
        WJobLogger.log(logInfo);

        try {
            // 获取分析日期：优先使用传入的参数，否则使用昨天的日期
            String analysisDate = getAnalysisDate(param);
            String logInfo2 = String.format("分析日期：%s", analysisDate);
            log.info(logInfo2);
            WJobLogger.log(logInfo2);

            // 1. 获取精选客服的用户ID列表（biz_line=2，固定企业ww5cfa32107e9a1f20）
            List<String> jingxuanUserIds = getJingxuanUserIds();

            if (ObjectUtils.isEmpty(jingxuanUserIds)) {
                logInfo = "未找到精选客服账号，任务结束";
                log.info(logInfo);
                WJobLogger.log(logInfo);
                return ReturnT.SUCCESS;
            }

            logInfo = String.format("找到 %d 个精选客服账号", jingxuanUserIds.size());
            log.info(logInfo);
            WJobLogger.log(logInfo);

            // 2. 处理所有客服的聊天记录
            int totalProcessed = processAllCustomerServiceChats(jingxuanUserIds, analysisDate);

            logInfo = String.format("精选客服Dify聊天分析定时任务执行完成，共处理 %d 个外部用户对话", totalProcessed);
            log.info(logInfo);
            WJobLogger.log(logInfo);

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            String errorMsg = String.format("精选客服Dify聊天分析定时任务执行失败：%s", e.getMessage());
            log.error(errorMsg, e);
            WJobLogger.log(errorMsg);
            return ReturnT.FAIL;
        }
    }

    /**
     * 获取分析日期
     *
     * @param param 任务参数，格式为yyyyMMdd，如果为空则使用昨天的日期
     * @return 分析日期字符串
     */
    private String getAnalysisDate(String param) {
        try {
            if (StringUtils.isNotEmpty(param)) {
                // 验证传入的日期格式
                Date paramDate = DateUtils.strToDate(param, DateUtils.yyyyMMdd);
                if (paramDate != null) {
                    log.info("使用传入的分析日期：{}", param);
                    return param;
                } else {
                    log.warn("传入的日期参数格式不正确：{}，使用默认日期", param);
                }
            }

            // 使用昨天的日期作为默认值
            String defaultDate = DateUtils.dateToStr(DateUtils.yyyyMMdd, DateUtils.addDay(new Date(), -1));
            log.info("使用默认分析日期（昨天）：{}", defaultDate);
            return defaultDate;

        } catch (Exception e) {
            log.error("解析日期参数失败，使用默认日期", e);
            return DateUtils.dateToStr(DateUtils.yyyyMMdd, DateUtils.addDay(new Date(), -1));
        }
    }

    /**
     * 获取精选客服用户ID列表（固定企业ww5）
     */
    private List<String> getJingxuanUserIds() {
        try {
            List<WxWorkContactWayBizAccountConfEntity> configs =
                    wxWorkContactWayBizAccountConfRepository.getAllAvailableUsersByBizLine(BizLineEnum.精选.getCode());

            return configs.stream()
                    .filter(config -> JINGXUAN_CORP_ID.equals(config.getCorpId()))
                    .map(WxWorkContactWayBizAccountConfEntity::getUserId)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取精选客服用户ID失败", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 并行处理所有客服的聊天记录
     */
    private int processAllCustomerServiceChats(List<String> userIds, String analysisDate) {

        AtomicInteger res = new AtomicInteger();
        userIds.parallelStream().forEach(userId -> {
            int processed = processChatRecordsForUser(userId, analysisDate);
            res.set(res.get() + processed);
        });

        return res.get();
    }

    /**
     * 处理指定客服的聊天记录（固定企业ww5）
     */
    private int processChatRecordsForUser(String userId, String analysisDate) {
        try {
            log.info("处理客服 {} 在企业 {} 的聊天记录", userId, JINGXUAN_CORP_ID);

            Map<String, List<LbgAiMessageChatRecordBo>> chatRecordsByExternalUser = getChatRecordsByExternalUser(userId, analysisDate);

            if (ObjectUtils.isEmpty(chatRecordsByExternalUser)) {
                log.info("客服 {} 当天无聊天记录", userId);
                return 0;
            }

            log.info("客服 {} 找到 {} 个外部用户的聊天记录", userId, chatRecordsByExternalUser.size());

            // 并行处理聊天记录
            return processExternalUserChats(userId, analysisDate, chatRecordsByExternalUser);

        } catch (Exception e) {
            log.error("处理客服 {} 的聊天记录失败", userId, e);
            return 0;
        }
    }

    /**
     * 并行处理外部用户聊天记录
     */
    private int processExternalUserChats(String userId, String analysisDate,
                                         Map<String, List<LbgAiMessageChatRecordBo>> chatRecordsByExternalUser) {

        // 过滤出需要处理的记录
        List<Map.Entry<String, List<LbgAiMessageChatRecordBo>>> entriesToProcess =
                chatRecordsByExternalUser.entrySet().stream()
                        .filter(entry -> !difyChatAnalysisResultRepository.existsByUserAndDate(userId, entry.getKey(), analysisDate))
                        .collect(Collectors.toList());

        if (entriesToProcess.isEmpty()) {
            return 0;
        }

        for (Map.Entry<String, List<LbgAiMessageChatRecordBo>> entry : entriesToProcess) {
            processSingleUserChatRecord(userId, entry.getKey(), analysisDate, entry.getValue());
        }

        return entriesToProcess.size();
    }

    /**
     * 处理单个用户的聊天记录
     */
    private void processSingleUserChatRecord(String userId, String externalUserId,
                                             String analysisDate, List<LbgAiMessageChatRecordBo> chatMessages) {
        try {
            String difyResult = callDifyForChatAnalysis(userId, externalUserId, chatMessages);

            if (StringUtils.isNotEmpty(difyResult)) {
                // 提取category_4id字段
                String category4Id = extractCategory4Id(difyResult);

                difyChatAnalysisResultRepository.save(userId, externalUserId, analysisDate, difyResult, chatMessages.size(), category4Id);
                log.info("成功分析客服 {} 与外部用户 {} 的聊天记录，消息数量：{}，category4Id：{}",
                        userId, externalUserId, chatMessages.size(), category4Id);
            } else {
                saveChatAnalysisFailure(userId, externalUserId, analysisDate, "Dify分析返回空结果", chatMessages.size());
            }

        } catch (Exception e) {
            saveChatAnalysisFailure(userId, externalUserId, analysisDate, "处理异常：" + e.getMessage(), chatMessages.size());
            log.error("处理客服 {} 与外部用户 {} 的聊天记录失败", userId, externalUserId, e);
        }
    }

    /**
     * 从Dify分析结果中提取category_4id字段
     */
    private String extractCategory4Id(String difyResult) {
        try {
            if (StringUtils.isEmpty(difyResult)) {
                return null;
            }

            JSONObject jsonObject = JSON.parseObject(difyResult);
            if (jsonObject != null) {
                // 先尝试从outputs字段中提取
                JSONObject outputs = jsonObject.getJSONObject("outputs");
                if (outputs != null && outputs.containsKey("category_4id")) {
                    String category4Id = outputs.getString("category_4id");
                    return StringUtils.isNotEmpty(category4Id) && !"0".equals(category4Id) ? category4Id : null;
                }

                // 兼容：如果outputs中没有，尝试从根级别提取
                if (jsonObject.containsKey("category_4id")) {
                    String category4Id = jsonObject.getString("category_4id");
                    return StringUtils.isNotEmpty(category4Id) && !"0".equals(category4Id) ? category4Id : null;
                }
            }

            return null;

        } catch (Exception e) {
            log.warn("解析Dify结果提取category_4id失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 保存聊天分析失败记录
     */
    private void saveChatAnalysisFailure(String userId, String externalUserId, String analysisDate,
                                         String failureReason, int messageCount) {
        try {
            difyChatAnalysisResultRepository.saveFailure(userId, externalUserId, analysisDate, failureReason, messageCount);
            log.warn("Dify分析失败，客服：{}，外部用户：{}，原因：{}", userId, externalUserId, failureReason);
        } catch (Exception e) {
            log.error("保存失败记录时发生异常", e);
        }
    }

    /**
     * 获取指定客服当天的聊天记录（按external_user_id分组）
     */
    private Map<String, List<LbgAiMessageChatRecordBo>> getChatRecordsByExternalUser(String userId, String analysisDate) {
        try {
            return juziSingleChatContentRecordDirectRepository.getChatRecordsByExternalUser(JINGXUAN_CORP_ID, userId, analysisDate);
        } catch (Exception e) {
            log.error("获取客服 {} 当天聊天记录失败", userId, e);
            return Maps.newHashMap();
        }
    }

    /**
     * 调用Dify分析聊天记录
     */
    private String callDifyForChatAnalysis(String userId, String externalUserId,
                                           List<LbgAiMessageChatRecordBo> chatMessages) {
        if (ObjectUtils.isEmpty(chatMessages)) {
            log.info("聊天记录为空，跳过Dify分析，客服：{}，外部用户：{}", userId, externalUserId);
            return null;
        }

        String user = String.format("%s:%s:%s", JINGXUAN_CORP_ID, userId, externalUserId);
        log.info("调用Dify分析聊天记录，用户：{}，消息数量：{}", user, chatMessages.size());

        try {
            WorkflowRequest request = new WorkflowRequest();
            request.setUser(user);
            request.getInputs().put("chatList", JacksonUtils.format(chatMessages));

            Result<WorkflowResponse> workflow = difyRemoteService.workflow(DIFY_WORKFLOW_API_KEY, request);

            validateDifyResponse(workflow, user);

            String result = JacksonUtils.format(workflow.getData().getData());
            log.info("Dify分析成功，用户：{}，结果长度：{}", user, result.length());
            return result;

        } catch (Exception e) {
            log.error("调用Dify分析聊天记录失败，用户：{}", user, e);
            throw e;
        }
    }

    /**
     * 验证Dify响应
     */
    private void validateDifyResponse(Result<WorkflowResponse> workflow, String user) {
        if (workflow == null) {
            throw new RuntimeException("Dify服务返回null");
        }
        if (workflow.isFailed()) {
            throw new RuntimeException("Dify服务调用失败：" + workflow.getMsg());
        }
        if (ObjectUtils.isEmpty(workflow.getData()) || ObjectUtils.isEmpty(workflow.getData().getData())) {
            log.warn("Dify分析返回空数据，用户：{}", user);
            throw new RuntimeException("Dify分析返回空数据");
        }
    }

}
