package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository;

import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywxbiz.contract.dto.ai.AiWelcomeGetLatestSendTimeResp;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Description: AI欢迎语的时间戳
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LbgAiWelcomeLatestSendTimeRepository {

    private final RedissonClient redisson;

    private static final String CHAT_ID_KEY_TEMPLATE = "AI_WELCOME_LATEST_SEND_TIME:%s:%s:%s:%s";

    public void save(@NonNull final String corpId,
                     @NonNull final Integer bizLine,
                     @NonNull final Integer bizScene,
                     @NonNull final String botUserId,
                     @NonNull final String externalUserId) {
        String key = String.format(CHAT_ID_KEY_TEMPLATE, corpId, bizLine, botUserId, externalUserId);

        RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);
        String aiWelcomeLatestSendTimes = bucket.get();
        Map<Integer, Long> map = Maps.newHashMap();
        if (ObjectUtils.isNotEmpty(aiWelcomeLatestSendTimes)) {
            map = JacksonUtils.parse(aiWelcomeLatestSendTimes, new TypeReference<HashMap<Integer, Long>>() {
            });
        }
        map.put(bizScene, System.currentTimeMillis());
        bucket.set(JacksonUtils.format(map));
    }

    public List<AiWelcomeGetLatestSendTimeResp.Item> get(@NonNull final String corpId,
                                                         @NonNull final Integer bizLine,
                                                         @NonNull final Set<Integer> bizScene,
                                                         @NonNull final String botUserId,
                                                         @NonNull final String externalUserId) {
        String key = String.format(CHAT_ID_KEY_TEMPLATE, corpId, bizLine, botUserId, externalUserId);

        RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);
        String aiWelcomeLatestSendTimes = bucket.get();
        if (ObjectUtils.isEmpty(aiWelcomeLatestSendTimes)) {
            return null;
        }
        Map<Integer, Long> map = JacksonUtils.parse(aiWelcomeLatestSendTimes, new TypeReference<HashMap<Integer, Long>>() {
        });
        List<AiWelcomeGetLatestSendTimeResp.Item> res = Lists.newArrayList();
        for (Integer scene : bizScene) {
            Long latestSendTime = map.get(scene);
            if (ObjectUtils.isNotEmpty(latestSendTime)) {
                res.add(new AiWelcomeGetLatestSendTimeResp.Item(scene, latestSendTime));
            }
        }
        return res;
    }

}
