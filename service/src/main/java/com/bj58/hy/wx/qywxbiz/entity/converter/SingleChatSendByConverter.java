package com.bj58.hy.wx.qywxbiz.entity.converter;


import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;

import javax.persistence.AttributeConverter;

/**
 * Description: 私聊时，消息发送人 的类型
 *
 * <AUTHOR>
 */
@javax.persistence.Converter
public class SingleChatSendByConverter implements AttributeConverter<SingleChatSendBy, Integer> {

    @Override
    public Integer convertToDatabaseColumn(SingleChatSendBy attribute) {
        return attribute.getCode();
    }

    @Override
    public SingleChatSendBy convertToEntityAttribute(Integer dbData) {
        return SingleChatSendBy.strictOf(dbData);
    }

}
