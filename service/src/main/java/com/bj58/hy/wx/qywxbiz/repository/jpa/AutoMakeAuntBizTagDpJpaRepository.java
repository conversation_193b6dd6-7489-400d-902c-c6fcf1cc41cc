package com.bj58.hy.wx.qywxbiz.repository.jpa;

import com.bj58.hy.wx.qywxbiz.entity.AutoMakeAuntBizTagDpEntity;
import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Repository
public interface AutoMakeAuntBizTagDpJpaRepository
        extends JpaRepository<AutoMakeAuntBizTagDpEntity, Long> {

    @Transactional(readOnly = true)
    List<AutoMakeAuntBizTagDpEntity> getByExternalUser58IdAndImportDate(long externalUser58Id,
                                                                        @NonNull String importDate);

}
