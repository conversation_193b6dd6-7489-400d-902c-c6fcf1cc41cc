package com.bj58.hy.wx.qywxbiz.entity;

import com.bj58.hy.lib.spring.support.jpa.superclass.Identifiable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.util.Date;

@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "t_account_conf_operator_log")
public class AccountConfOperatorLogEntity extends Identifiable {

    /**
     * 操作人
     */
    @Getter
    @Setter
    @Column(name = "operator", nullable = false)
    private String operator;

    /**
     * 账号配置表id
     */
    @Getter
    @Setter
    @Column(name = "account_conf_id", nullable = false)
    private Long accountConfId;

    /**
     * 日志
     */
    @Getter
    @Setter
    @Column(name = "log", nullable = false)
    private String log;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    @Column(name = "create_time", nullable = false)
    private Date createTime;

    /**
     * 更新时间
     */
    @Getter
    @Setter
    @Column(name = "update_time", nullable = false)
    private Date updateTime;

}
