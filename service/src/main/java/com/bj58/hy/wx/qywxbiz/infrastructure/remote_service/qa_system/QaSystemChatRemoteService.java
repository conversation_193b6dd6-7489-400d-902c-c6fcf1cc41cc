package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.qa_system;

import com.alibaba.fastjson.JSONObject;
import com.bj58.huangye.alg.qasystem.client.contract.IChatService;
import com.bj58.huangye.alg.qasystem.client.entity.AiCustomerDaojiaInput;
import com.bj58.huangye.alg.qasystem.client.entity.AiCustomerDaojiaResult;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class QaSystemChatRemoteService {

    @SCFClient(lookup = IChatService.SCF_URL)
    private IChatService chatService;

    public AiCustomerDaojiaResult aiCustomerChat(AiCustomerDaojiaInput input) {
        try {
            log.info("QaSystemChatService.aiCustomerChat input = {}", JSONObject.toJSONString(input));
            AiCustomerDaojiaResult result = chatService.aiCustomerDaojia(input);
            log.info("QaSystemChatService.aiCustomerChat result = {}", JSONObject.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("QaSystemChatService.aiCustomerChat error", e);
        }
        return null;
    }

}
