package com.bj58.hy.wx.qywxbiz.interfaces.scf.common;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.aspect.GlobalExceptionWrapper;
import com.bj58.hy.lib.spring.support.aspect.VerifiedParams;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.enums.ExternalContactStatus;
import com.bj58.hy.wx.qywxbiz.contract.ICscAuthService;
import com.bj58.hy.wx.qywxbiz.contract.dto.csc_auth.GetCscAuthInfoReq;
import com.bj58.hy.wx.qywxbiz.contract.dto.csc_auth.GetCscAuthInfoResp;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.spat.scf.server.contract.annotation.ServiceBehavior;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;


@Slf4j
@Component
@ServiceBehavior
public class CscAuthService implements ICscAuthService {

    @Autowired
    private ExternalContactRemoteService externalContactService;

    @Autowired
    private RedissonClient redisson;


    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<GetCscAuthInfoResp> getCscAuthInfo(@NotNull @Valid final GetCscAuthInfoReq req) {
        // 1. 获取二者的好友关系
        GetExternalContactRelationshipResp relationship = externalContactService.getRelationship(
                req.getCorpId(), req.getUserId(), req.getExternalUserId());
        if (ObjectUtils.isNull(relationship)) {
            return Result.failure("好友关系不存在");
        }

        List<Integer> allowStatus = Arrays.asList(
                ExternalContactStatus.ENABLE.getCode(),
                ExternalContactStatus.DELETE_BY_EXTERNAL_USER.getCode()
        );
        if (!allowStatus.contains(relationship.getStatus())) {
            return Result.failure("当前企业成员未跟进此外部联系人");
        }


        @Nullable Long wubaId = externalContactService.get58IdByExternalUserId(req.getCorpId(), req.getExternalUserId());

        RMap<String, String> rMap = redisson.getMap(String.format("CSC_AUTO:%s", req.getCorpId()), StringCodec.INSTANCE);
        String oa = rMap.get(req.getUserId());
        if (ObjectUtils.isEmpty(oa)) {
            return Result.failure("当前企业成员未关联OA账户");
        }

        GetCscAuthInfoResp resp = new GetCscAuthInfoResp();
        resp.setUserOa(oa);
        resp.setExternalUser58Id(wubaId);

        return Result.success(resp);
    }
}
