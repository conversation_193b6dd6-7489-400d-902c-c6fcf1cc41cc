package com.bj58.hy.wx.qywxbiz.service.jiafu.group_chat;

import cn.hutool.core.collection.ConcurrentHashSet;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.GetRecommendedOwnerIdReq;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.CorpUserRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.GroupChatRemoteService;
import com.bj58.hy.wx.qywxbiz.service.bo.BizLineEnum;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.group_chat.AbstractGroupChatOwnerStrategy;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.NonNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class JiaFuAuntAutoCreateGroupChatStrategy extends AbstractGroupChatOwnerStrategy {

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private RedisLockSupport lockSupport;

    @Autowired
    private CorpUserRemoteService corpUserService;

    @Autowired
    private GroupChatRemoteService groupChatRemoteService;

    private static final Set<Integer> ALLOW_BIZ_LINES = new HashSet<>(Arrays.asList(
            BizLineEnum.家服.getCode()
    ));

    private static final Set<Integer> ALLOW_BIZ_SCENES = new HashSet<>(Arrays.asList(
            BizSceneEnum.家服_阿姨接单自动建群.getSceneId()
    ));

    @Override
    public boolean matched(@NonNull GetRecommendedOwnerIdReq req) {
        return ALLOW_BIZ_LINES.contains(req.getBizLine()) &&
                ALLOW_BIZ_SCENES.contains(req.getBizScene());
    }

    @Override
    @Nullable
    public String getUserId(@NonNull GetRecommendedOwnerIdReq req) {
        List<String> availableUserIds = getAvailableUserIds(req);

        if (ObjectUtils.isEmpty(availableUserIds)) {
            return req.getDefaultOwnerId();
        }
        if (availableUserIds.size() == 1) {
            return availableUserIds.get(0);
        }

        if (ObjectUtils.notEmpty(req.getDefaultOwnerId())) {
            if (availableUserIds.contains(req.getDefaultOwnerId())) {
                return req.getDefaultOwnerId();
            }
        }

        return this.getNextUserId(availableUserIds, req);
    }

    private List<String> getAllUserIds(@NonNull final GetRecommendedOwnerIdReq req) {
        RBucket<String> allUserIdsBucket = redisson.getBucket("家服阿姨接单自动建群:全量客服",
                StringCodec.INSTANCE);

        if (!allUserIdsBucket.isExists()) {
            RBucket<String> bucket = redisson.getBucket("家服阿姨接单自动建群:部门",
                    StringCodec.INSTANCE);
            String departmentsString = bucket.get();
            if (ObjectUtils.isEmpty(departmentsString)) {
                return Collections.emptyList();
            }

            Set<Integer> departmentIds = Arrays.stream(departmentsString.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toSet());
            if (ObjectUtils.isEmpty(departmentIds)) {
                return Collections.emptyList();
            }

            Set<String> corpUserIds = new ConcurrentHashSet<>();
            departmentIds.parallelStream().forEach(departmentId -> {
                List<String> list = corpUserService.getByDepartmentId(
                        req.getCorpId(), departmentId);
                corpUserIds.addAll(list);
            });

            allUserIdsBucket.set(JacksonUtils.format(corpUserIds), 1, TimeUnit.DAYS);
        }

        return JacksonUtils.parse(
                allUserIdsBucket.get(),
                new TypeReference<List<String>>() {
                }
        );
    }

    public List<String> getAvailableUserIds(@NonNull GetRecommendedOwnerIdReq req) {
        List<String> corpUserIds = getAllUserIds(req);

        // 查询这部分企微客服，目前都新建了多少个群，过滤掉超限客服
        long threshold = getThreshold(req);

        return corpUserIds.parallelStream()
                .filter(userId -> {
                    RBucket<Long> rBucket = redisson.getBucket(String.format("家服阿姨接单自动建群:客服今日建群数缓存:%s", userId), LongCodec.INSTANCE);

                    long count;
                    if (rBucket.isExists()) {
                        count = rBucket.get();
                    } else {
                        count = groupChatRemoteService.countCorpUserTodayCreateGroupChat(req.getCorpId(), userId);
                        rBucket.set(count, 5, TimeUnit.MINUTES);
                    }

                    return count <= threshold; // 大于阈值的，过滤即可
                })
                .collect(Collectors.toList());
    }

    private long getThreshold(@NonNull GetRecommendedOwnerIdReq req) {
        RBucket<Long> rBucket = redisson.getBucket("家服阿姨接单自动建群:阈值", LongCodec.INSTANCE);

        Long value = rBucket.get();
        if (ObjectUtils.isNull(value)) {
            value = 80L;
        }

        return value;
    }

    private String getNextUserId(@NonNull final List<String> availableUserIds,
                                 @NonNull final GetRecommendedOwnerIdReq req) {

        if (ObjectUtils.isEmpty(availableUserIds)) {
            return null;
        }

        return lockSupport.execute(
                String.format("%s:GROUP_CHAT_OWNER_PRE_ACCOUNT:BIZ_LINE:%s:BIZ_SCENE:%s:LOCK", req.getCorpId(), req.getBizLine(), req.getBizScene()),
                () -> {
                    // redis取上一次的user
                    RBucket<String> prevUserBucket = redisson.getBucket(
                            String.format("%s:RecommendedGroupChatOwnerId:bizLine:%s:bizScene:%s", req.getCorpId(), req.getBizLine(), req.getBizScene()),
                            StringCodec.INSTANCE);

                    String nextUserId = null;

                    String prevUser = prevUserBucket.get();
                    if (ObjectUtils.notEmpty(prevUser)) { // 如果之前处理过，则尝试取下一个
                        nextUserId = getNext(availableUserIds, prevUser);
                    }

                    // 如果之前没有记录，或者其他的特殊情况
                    if (ObjectUtils.isEmpty(nextUserId)) {
                        nextUserId = availableUserIds.get(0);
                    }

                    // 存入最新账号
                    prevUserBucket.set(nextUserId, 5, TimeUnit.DAYS);

                    return nextUserId;
                }
        );
    }

    private static String getNext(@NonNull final List<String> availableUserIds,
                                  @NonNull final String currUserId) {
        if (ObjectUtils.isEmpty(availableUserIds)) {
            return null;
        }

        int currentIndex = availableUserIds.indexOf(currUserId);

        if (currentIndex == -1) { // 之前处理过，但是最新的配置中没有对应的企业成员
            return null;
        }

        int nextIndex = (currentIndex + 1) % availableUserIds.size();
        return availableUserIds.get(nextIndex);
    }

}
