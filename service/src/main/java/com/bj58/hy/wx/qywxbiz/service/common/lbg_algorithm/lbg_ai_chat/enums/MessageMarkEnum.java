package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息类型标记枚举
 * <AUTHOR>
 * @date 2025/3/6 14:57
 */
@Getter
@AllArgsConstructor
public enum MessageMarkEnum {

    WELCOME("welcome", "对客服发送的消息，标记为加微欢迎语"),

    QUICK_RESPONSE("quick_response", "距离客户or客服最后一条消息>5分钟时且小于24h，用户再次发起聊天时，等待Ai回复期间，系统自动发一个应答语，并标记为应答语"),

    SOP("sop", "SOP功能发送的消息，sendSource=7，不参与倒计时逻辑"),

    ;
    private final String name;
    private final String desc;
}
