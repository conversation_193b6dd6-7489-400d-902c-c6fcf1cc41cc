package com.bj58.hy.wx.qywxbiz.service.bo;

import com.bj58.hy.lib.core.util.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 对应 表：t_biz_dict
 */
@Getter
@AllArgsConstructor
public enum BizLineEnum {

    主站(1, "主站"),
    精选(2, "精选"),
    家服(3, "家服"),

    开发专用(99, "开发专用"),
    开发一键拉群专用(54, "开发一键拉群专用"),
    ;

    private final int code;

    private final String desc;


    public static BizLineEnum strictOf(Integer code) {
        BizLineEnum value = of(code);
        if (ObjectUtils.isNull(value)) {
            throw new UnsupportedOperationException(
                    String.format("unsupported enum, biz line = %s", code));
        }

        return value;
    }

    public static BizLineEnum of(Integer code) {

        for (BizLineEnum item : BizLineEnum.values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
