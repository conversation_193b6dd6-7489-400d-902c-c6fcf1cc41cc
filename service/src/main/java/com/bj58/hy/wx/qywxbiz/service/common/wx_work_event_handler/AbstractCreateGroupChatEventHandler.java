package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_chat.CreateExternalChatEventBo;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_chat.ExternalChatEventCallbackType;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Description: 企业创建群聊相关的风控规则校验基类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCreateGroupChatEventHandler extends AbstractWxWorkEventHandler {

    @Autowired
    protected RedissonClient redisson;
    @Autowired
    protected RedisLockSupport lockSupport;

    @Override
    public void process(@NonNull JSONObject event) {
        // 校验是否是创建群聊相关的消息
        ExternalChatEventCallbackType callbackType =
                ExternalChatEventCallbackType.getEnumByJson(event);

        if (!ExternalChatEventCallbackType.CREATE_EXTERNAL_CHAT.equals(callbackType)) {
            return;
        }


        // 校验是否是创建群聊相关的消息
        CreateExternalChatEventBo eventMsg = JSON.parseObject(event.toJSONString(), CreateExternalChatEventBo.class);
        if (ObjectUtils.isEmpty(eventMsg)) {
            return;
        }

        process(eventMsg);
    }

    protected abstract void process(@NonNull CreateExternalChatEventBo event);

    @Override
    public boolean matched(final @NonNull JSONObject event) {
        return true;
    }
} 