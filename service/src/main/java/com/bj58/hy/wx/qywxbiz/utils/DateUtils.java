package com.bj58.hy.wx.qywxbiz.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

public class DateUtils {

    public static final String yyyyMM = "yyyy-MM";
    public static final String yyyyMM1 = "yyyyMM";
    public static final String yyyyMM2 = "yyyy/MM";
    public static final String yyyyMM3 = "yyyy.MM";
    public static final String yyyyMM4 = "yyyy年MM月";


    public static final String yyyyMMdd = "yyyy-MM-dd";
    public static final String yyyyMMdd1 = "yyyyMMdd";
    public static final String yyyyMMdd2 = "yyyy/MM/dd";
    public static final String yyyyMMdd3 = "yyyy.MM.dd";
    public static final String yyyyMMdd4 = "yyyy年MM月dd日";

    public static final String MMdd = "MM月dd日";

    public static final String MM_dd = "MM-dd";

    public static final String yyyyMMddHHmm = "yyyy-MM-dd HH:mm";
    public static final String yyyyMMdd4HHmm = "yyyy年MM月dd日 HH:mm";

    public static final String yyyyMMddHHmmss = "yyyy-MM-dd HH:mm:ss";
    public static final String yyyyMMddHH4mmss = "yyyy年MM月dd日 HH:mm:ss";
    public static final String yyyy_MM_dd4HHmm = "yyyy年-MM月-dd日 HH:mm";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MI_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String HH_MI = "HH:mm";
    public static final String YYYY_MM_DD_T_HH_MI_ss = "yyyy-MM-dd'T'HH:mm:ss";


    /**
     * 字符串转换为日期类型
     */
    public static Date strToDate(String dateStr, String format) {
        if (StringUtils.isBlank(dateStr)) return null;
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setLenient(false);
        try {
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String dateToStr(String formatStr, Date date) {
        if (date == null) return Strings.EMPTY;
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        return format.format(date);
    }


    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date localDateToDate(LocalDate localDate) {
        return localDateTimeToDate(localDate.atStartOfDay());
    }

    /**
     * 在日期时间上添加分钟
     */
    public static Date addMinute(Date date, int num) {
        return add(date, Calendar.MINUTE, num);
    }

    public static Date add(Date date, int field, int num) {
        // 设置周一为当前周的第一天
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        cal.setTime(date);
        cal.add(field, num);
        return cal.getTime();
    }

    /**
     * 在日期时间上添加秒
     */
    public static Date addSecond(Date date, int num) {
        return add(date, Calendar.SECOND, num);
    }

    public static Date addDay(Date date, int num) {
        return add(date, Calendar.DAY_OF_MONTH, num);
    }

    public static Date addMonth(Date date, int num) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.add(Calendar.MONTH, num);//设置1个月后
        return ca.getTime();
    }

    public static Date getDayStart(Date date) {
        return Date.from(LocalDate.from(date.toInstant().atZone(ZoneId.systemDefault())).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getDayEnd(Date date) {
        if (date == null) {
            return null;
        }
        return Date.from(LocalDate.from(date.toInstant().atZone(ZoneId.systemDefault())).atStartOfDay().plusDays(1).minusSeconds(1).atZone(ZoneId.systemDefault()).toInstant());
    }


    public static boolean isDifferenceGreaterThanMinutes(long timestamp1, long timestamp2, long min) {
        long differenceInMillis = Math.abs(timestamp1 - timestamp2);
        long differenceInMinutes = differenceInMillis / (60 * 1000);

        return differenceInMinutes > min; // 检查是否大于传入的时间 单位分
    }

}
