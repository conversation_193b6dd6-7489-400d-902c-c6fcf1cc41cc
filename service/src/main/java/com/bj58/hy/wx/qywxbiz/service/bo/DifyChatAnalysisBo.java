package com.bj58.hy.wx.qywxbiz.service.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Dify聊天分析业务对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DifyChatAnalysisBo {

    /**
     * 企业微信客服用户ID
     */
    private String userId;

    /**
     * 外部用户ID
     */
    private String externalUserId;

    /**
     * 聊天记录列表
     */
    private List<ChatMessageBo> chatMessages;

    /**
     * 聊天记录数量
     */
    private Integer messageCount;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChatMessageBo {
        /**
         * 消息发送时间戳
         */
        private Long dateStamp;

        /**
         * 消息内容
         */
        private String message;

        /**
         * 发送人
         */
        private String sender;

        /**
         * 接收人
         */
        private String receiver;

        /**
         * 消息ID
         */
        private String messageId;

        /**
         * 是否为外部用户发送
         */
        private Boolean isFromExternalUser;
    }

}
