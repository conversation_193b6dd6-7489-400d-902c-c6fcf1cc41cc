package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.card;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.AbstractMeishiMessageHandler;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.MeishiCardService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.MeishiMessageBo;
import com.bj58.meishi.openapi.client.MeishiOpenapiCardClient;
import com.bj58.meishi.openapi.entity.card.model.SendCardParam;
import com.bj58.meishi.openapi.entity.card.model.UserAndVar;
import com.bj58.meishi.openapi.entity.common.TransJsonData;
import lombok.NonNull;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/11/26 10:44
 */
@Component
public class MeishiGroupCardHandler extends AbstractMeishiMessageHandler {


    @Autowired
    private RedissonClient redisson;

    @Autowired
    private MeishiCardService meishiCardService;

    @Override
    public void process(@NonNull MeishiMessageBo bo) {


        Map<String, String> contentMap = bo.getContentMap();

        try {
            // 构建content
            String title = bo.getTitle();
            String content = meishiCardService.buildContent(contentMap);


            Set<String> groupIds = bo.getSpecialGroupIds();
            if (ObjectUtils.isEmpty(groupIds)) {

                RMap<String, String> to;

                if ("Product".equals(System.getenv("WCloud_Env"))) {
                    to = redisson.getMap("MEISHI_GROUPCARD_GROUPID", StringCodec.INSTANCE);
                } else {
                    to = redisson.getMap("MEISHI_GROUPCARD_GROUPID_SANDBOX", StringCodec.INSTANCE);
                }
                groupIds = new LinkedHashSet<>(to.values());
            }

            if (ObjectUtils.isEmpty(groupIds)) {
                log.warn("没有可告警的美事群");
                return;
            }

            List<UserAndVar> msgList = new ArrayList<>();
            for (String groupId : groupIds) {
                msgList.add(meishiCardService.buildCard(groupId, title, content));
            }

            SendCardParam param = new SendCardParam();
            param.setCardId("ctp_202411166b42b1");
            param.setRobotId("MIS_ROBOT_hyqywx");
            param.setGroup(true);
            param.setUidAndVarList(msgList);

            TransJsonData<String> sendResult = MeishiOpenapiCardClient
                    .sendCardByCardId(UUID.randomUUID().toString(), param);

            log.info("MeishiGroupCardHandler send result: {}, param: {}", sendResult, JSONObject.toJSONString(param));

        } catch (Exception e) {
            log.error("MeishiGroupCardHandler send error, contentMap:{}, param:{}",
                    JSONObject.toJSONString(contentMap), JSONObject.toJSONString(bo), e);
        }

    }
}
