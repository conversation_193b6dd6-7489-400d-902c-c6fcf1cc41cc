package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.ICorpUserService;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class CorpUserRemoteService {

    @SCFClient(lookup = ICorpUserService.SCF_URL)
    private ICorpUserService corpUserService;

    public List<String> getByDepartmentId(@NonNull final String corpId,
                                          @NonNull final Integer departmentId) {

        Result<List<String>> result = null;
        try {
            result = corpUserService.getByDepartmentId(corpId, departmentId);

        } catch (Exception e) {
            log.error("get corp users error, " +
                    "corp id = {}, department id = {}", corpId, departmentId, e);
            return null;
        }

        if (ObjectUtils.isNull(result) ||
                result.isFailed() ||
                ObjectUtils.isNull(result.getData())) {
            log.error("not found corp users, " +
                    "corp id = {}, department id = {}", corpId, departmentId);
            return null;
        }

        return result.getData();
    }

}
