package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.support.pojo.PagedResult;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo.OrderQueryBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.bo.OrderInfoBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.enums.*;
import com.bj58.lbg.business.plaform.oms.api.common.SecretToken;
import com.bj58.lbg.business.plaform.oms.api.common.enums.OrderKindEnum;
import com.bj58.lbg.business.plaform.oms.api.common.resp.OrderPageResult;
import com.bj58.lbg.business.plaform.oms.api.contract.IPlatformOrderCrossAuthQueryService;
import com.bj58.lbg.business.plaform.oms.api.dto.query.PlatformCommonOrderInfo;
import com.bj58.lbg.business.plaform.oms.api.dto.query.PlatformOrderQueryInfo;
import com.bj58.lbg.business.plaform.oms.api.dto.query.cross.auth.c.PlatformOrderQueryByCIndexNestedMultiDto;
import com.bj58.lbg.business.plaform.oms.api.dto.query.cross.auth.client.PlatformOrderQueryByCIndexExtClientNestedDto;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class CommonOrderQueryService {

    @SCFClient(lookup = IPlatformOrderCrossAuthQueryService.SCF_URL)
    private IPlatformOrderCrossAuthQueryService platformOrderCrossAuthQueryService;

    private static final SecretToken COMMON_SECRET_TOKEN = new SecretToken();
    private static final String COMMON_CLIENT_ID = "1874006922752163840";
    private static final String COMMON_KEY = "7df9f034dd5f46b29d08ed36b3e01d5b";

    static {
        COMMON_SECRET_TOKEN.setClientId(COMMON_CLIENT_ID);
        COMMON_SECRET_TOKEN.setSecretKey(COMMON_KEY);
    }

    public PagedResult<OrderInfoBo> pageOrder(OrderQueryBo req) {

        PlatformOrderQueryByCIndexNestedMultiDto query = new PlatformOrderQueryByCIndexNestedMultiDto();
        // 查询条件
        PlatformOrderQueryByCIndexExtClientNestedDto indexExtClientNestedDto = new PlatformOrderQueryByCIndexExtClientNestedDto();

        // 服务类目
        if (ObjectUtils.isNotEmpty(req.getCateId())) {
            indexExtClientNestedDto.setFullPathCateId(String.valueOf(req.getCateId()));
        }
        // 查询58id
        if (ObjectUtils.isNotEmpty(req.getWbUserId())) {
            indexExtClientNestedDto.setCuserId(req.getWbUserId());
        }

        // 订单状态
        if (ObjectUtils.isNotEmpty(req.getOrderStatus())) {
            indexExtClientNestedDto.setSubOrderState(req.getOrderStatus().toString());
        }

        if (ObjectUtils.isNotEmpty(req.getOrderCreateTimeStart())) {
            indexExtClientNestedDto.setCreateStartTime(req.getOrderCreateTimeStart().getTime());
        }
        if (ObjectUtils.isNotEmpty(req.getOrderCreateTimeEnd())) {
            indexExtClientNestedDto.setCreateEndTime(req.getOrderCreateTimeEnd().getTime());
        }

        if (ObjectUtils.isNotEmpty(req.getPayTimeStart())) {
            indexExtClientNestedDto.setPaymentStartTime(req.getPayTimeStart().getTime());
        }
        if (ObjectUtils.isNotEmpty(req.getPayTimeEnd())) {
            indexExtClientNestedDto.setPaymentEndTime(req.getPayTimeEnd().getTime());
        }

        if (ObjectUtils.isNotEmpty(req.getServiceCompletionTimeStart())) {
            indexExtClientNestedDto.setServiceStartTime(req.getServiceCompletionTimeStart().getTime());
        }
        if (ObjectUtils.isNotEmpty(req.getServiceCompletionTimeEnd())) {
            indexExtClientNestedDto.setServiceEndTime(req.getServiceCompletionTimeEnd().getTime());
        }

        List<PlatformOrderQueryByCIndexExtClientNestedDto> platformOrderQueryByCIndexExtNestedList = Lists.newArrayList();

        // 查询订单类型
        List<Long> clientIds = Lists.newArrayList();
        for (OrderClientIdEnum orderClientIdEnum : OrderClientIdEnum.values()) {
            clientIds.add(orderClientIdEnum.getClientId());
        }
        indexExtClientNestedDto.setClientIds(clientIds);
        platformOrderQueryByCIndexExtNestedList.add(indexExtClientNestedDto);

        query.setPlatformOrderQueryByCIndexExtNestedList(platformOrderQueryByCIndexExtNestedList);
        // 排序
        query.setQuerySearchSortEntityList(req.getQuerySearchSortEntityList());

        query.setPageNo(req.getPage());
        query.setPageSize(req.getSize());

        log.info("OrderRemoteService.pageOrder req:{}", JacksonUtils.format(query));
        OrderPageResult<List<PlatformCommonOrderInfo>> orderPageResult = platformOrderCrossAuthQueryService.findOrdersCrossAuthByCIndexWithNested(COMMON_SECRET_TOKEN, query);
        log.info("OrderRemoteService.pageOrder resp:{}", JacksonUtils.format(orderPageResult));

        List<OrderInfoBo> res = Lists.newLinkedList();
        PagedResult<OrderInfoBo> result = new PagedResult<>();
        result.setResults(res);
        result.setTotal(orderPageResult.getTotal());
        result.setOffset(req.getOffset());
        result.setLimit(req.getLimit());

        if (ObjectUtils.isEmpty(orderPageResult) || orderPageResult.isError()) {
            return result;
        }

        for (PlatformCommonOrderInfo datum : orderPageResult.getData()) {
            PlatformOrderQueryInfo orderInfo = datum.getOrderInfo();
            OrderClientIdEnum orderClientIdEnum = OrderClientIdEnum.getEnumByCode(Long.valueOf(orderInfo.getClientId()));
            if (ObjectUtils.isEmpty(orderClientIdEnum)) {
                continue;
            }
            OrderInfoBo orderInfoBo = new OrderInfoBo();
            orderInfoBo.setOrderId(orderInfo.getOrderId());
            orderInfoBo.setOrderCreateTime(orderInfo.getOrderInfo().getCreateTime());
            orderInfoBo.setServiceStartTime(orderInfo.getServiceInfo().getServiceTime());
            orderInfoBo.setServiceCompletionTime(orderInfo.getServiceInfo().getCompletionTime());
            orderInfoBo.setCateId(orderInfo.getOrderInfo().getFourCateId());
            Integer subOrderState = orderInfo.getOrderInfo().getSubOrderState();
            orderInfoBo.setOrderStatusName("");
            if (Objects.equals(orderClientIdEnum, OrderClientIdEnum.DJBAOJIE)) {
                Integer orderKind = orderInfo.getOrderInfo().getOrderKind();
                if (!Arrays.asList(OrderKindEnum.SVC_ORDER.getCode(), OrderKindEnum.CLUE_ORDER.getCode(), OrderKindEnum.GOODS_ORDER.getCode()).contains(orderKind)) {
                    continue;
                }
                orderInfoBo.setOrderStatusName(DjjxOrderStatusEnum.getNameByCode(subOrderState));
                orderInfoBo.setProductName(getProductNameByRemark(orderInfo.getOrderExtInfo().getRemark()));
            } else if (Objects.equals(orderClientIdEnum, OrderClientIdEnum.DJBANJIA)) {
                orderInfoBo.setOrderStatusName(BanjiaOrderStatusEnum.getNameByCode(subOrderState));
                orderInfoBo.setProductName(getProductNameByRemark(orderInfo.getOrderExtInfo().getRemark()));
            }
            res.add(orderInfoBo);
        }
        return result;
    }

    public static String getProductNameByRemark(String remark) {
        if (ObjectUtils.isEmpty(remark)) {
            return "-";
        }
        try {
            JSONObject parsed = JSONObject.parseObject(remark);
            if (ObjectUtils.isEmpty(parsed)) {
                return "-";
            }
            return parsed.getString("serviceName");
        } catch (Exception ignored) {
        }

        return "-";
    }

}
