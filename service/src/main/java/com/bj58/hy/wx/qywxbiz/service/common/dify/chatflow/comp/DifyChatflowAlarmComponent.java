package com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.comp;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.ChatflowResponse;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.AbstractMeishiMessageHandler;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.MeishiMessageBo;
import com.bj58.hy.wx.qywxbiz.service.common.dify.bo.DifyApiInfoBo;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DifyChatflowAlarmComponent {

    @Autowired
    protected RedissonClient redisson;

    @Autowired
    protected ObjectProvider<AbstractMeishiMessageHandler> meishiMessageHandlers;


    public void sendTipsToMeiShi_1V1(@NonNull final String corpId,
                                     @NonNull final String botUserId,
                                     @NonNull final DifyApiInfoBo.Item apiInfo,
                                     @Nullable final Result<ChatflowResponse> aiResult) {
        // RBucket<String> limitBucket = redisson.getBucket(
        //         String.format("DifyAiReplyError_%s_%s", corpId, botUserId),
        //         StringCodec.INSTANCE);
        // if (limitBucket.isExists()) {
        //     log.warn("当前DIFY Chatflow失败告警频繁触发已被忽略, corp id = {}, user id = {}", corpId, botUserId);
        //     return;
        // }

        // 按照工作流维度控制频率
        RBucket<String> limitBucket = redisson.getBucket(
                String.format("DifyAiReplyError_%s", apiInfo.getId()),
                StringCodec.INSTANCE);
        if (limitBucket.isExists()) {
            log.warn("当前DIFY Chatflow失败告警频繁触发已被忽略, chatflow id = {}, corp id = {}, user id = {}",
                    apiInfo.getId(), corpId, botUserId);
            return;
        }


        sendTipsToMeiShi(apiInfo, aiResult, limitBucket);
    }

    private void sendTipsToMeiShi(@NonNull final DifyApiInfoBo.Item apiInfo,
                                  @Nullable final Result<ChatflowResponse> aiResult,
                                  @NonNull final RBucket<String> limitBucket) {

        String errTips = null;
        if (ObjectUtils.notNull(aiResult)) {
            int errcode = aiResult.getCode();
            String errmsg = aiResult.getMsg();
            errTips = "errcode=" + errcode + ", errmsg=" + errmsg;
        }

        Map<String, String> contentMap = new LinkedHashMap<>();
        contentMap.put("负责人：", apiInfo.getOwner());
        contentMap.put("工作流ID：", apiInfo.getId());
        contentMap.put("失败原因：", errTips);

        alarm("企微DIFY应答失败通知", contentMap);

        limitBucket.set("1", getDifyAiChatflowExecErrorFreq(),
                TimeUnit.MINUTES);
    }

    private void alarm(@NonNull final String title,
                       @NonNull final Map<String, String> content) {

        MeishiMessageBo meishiBo = new MeishiMessageBo();
        meishiBo.setTitle(title);
        meishiBo.setContentMap(content);

        for (AbstractMeishiMessageHandler meishiMessageHandler : meishiMessageHandlers) {
            try {
                meishiMessageHandler.process(meishiBo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    private long getDifyAiChatflowExecErrorFreq() {

        RBucket<Long> bucket = redisson.getBucket(
                "DifyAiChatflowExecErrorFreq", LongCodec.INSTANCE);
        Long freq = bucket.get();
        if (ObjectUtils.isNull(freq)) {
            freq = 10L;
        }
        if (freq < 0) {
            freq = 10L;
        }

        return freq;
    }
}
