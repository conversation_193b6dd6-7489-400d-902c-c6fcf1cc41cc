package com.bj58.hy.wx.qywxbiz.service.jingxuan.auto_make_tags;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IExternalContactService;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.UpdateTagsOfThisExternalContactCorrespondingAllCorpUsersReq;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.AddExternalContactEventBo;
import com.bj58.hy.wx.qywxbiz.service.bo.BizLineEnum;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractAddExternalContactEventHandler;
import com.bj58.hy.wx.qywxbiz.service.jingxuan.auto_make_tags.bo.AutoMakeTagsStateValBo;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.google.common.collect.Sets;
import lombok.NonNull;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 售前咨询四级类
 * <AUTHOR>
 * @date 2024/12/19 11:20
 */
@Component
public class PreSalesSeekAdviceCate4ToTagEventHandler extends AbstractAddExternalContactEventHandler {


    @SCFClient(lookup = IExternalContactService.SCF_URL)
    private IExternalContactService externalContactService;


    @Override
    public void process(@NonNull AddExternalContactEventBo eventMsg) {
        // 获取渠道参数
        String stateMappedVal = eventMsg.getExt().getStateMappedVal();
        if (ObjectUtils.isEmpty(stateMappedVal)) {
            return;
        }

        String tagId = getAddTagId(stateMappedVal);
        if (ObjectUtils.isEmpty(tagId)) {
            return;
        }

        // 构建需要删除的标签
        RSet<String> allTagIds = redisson.getSet("售前咨询四级类_TAG_IDS", StringCodec.INSTANCE);
        if (allTagIds.isEmpty()) {
            return;
        }

        Set<String> deleteTagIds = new HashSet<>(allTagIds);
        deleteTagIds.remove(tagId);

        UpdateTagsOfThisExternalContactCorrespondingAllCorpUsersReq req =
                new UpdateTagsOfThisExternalContactCorrespondingAllCorpUsersReq();

        req.setCorpId(eventMsg.getCorpId());
        req.setExternalUserId(eventMsg.getExternalUserId());
        req.setAddTagIds(Sets.newHashSet(tagId));
        req.setRemoveTagIds(deleteTagIds);

        try {
            Result<Map<String, Boolean>> result = externalContactService
                    .updateTagsOfThisExternalContactCorrespondingAllCorpUsers(req);

            log.info("更新 售前咨询四级类 结果 : {}", JSONObject.toJSONString(result));

        } catch (Exception e) {

            log.error("更新 售前咨询四级类 异常，req = {}", JSONObject.toJSONString(req), e);
        }
    }

    private String getAddTagId(final String stateMappedVal) {
        try {
            if (ObjectUtils.isEmpty(stateMappedVal)) {
                return null;
            }

            JSONObject stateJsonObj = JSONObject.parseObject(stateMappedVal);
            if (ObjectUtils.isEmpty(stateJsonObj)) {
                return null;
            }

            Integer bizLine = stateJsonObj.getInteger("bizLine");
            Integer bizScene = stateJsonObj.getInteger("bizScene");

            if (ObjectUtils.isNull(bizLine) || ObjectUtils.isNull(bizScene)) {
                return null;
            }

            if (!Objects.equals(BizLineEnum.精选.getCode(), bizLine)
                    || !Sets.newHashSet(BizSceneEnum.精选_售前扫码加微.getSceneId()).contains(bizScene)) {
                return null;
            }

            String source = stateJsonObj.getString("source");
            if (ObjectUtils.isEmpty(source)) {
                return null;
            }

            AutoMakeTagsStateValBo mappedVal = JacksonUtils.parse(source, AutoMakeTagsStateValBo.class);
            Integer cateId = mappedVal.getCateId();

            if (ObjectUtils.isNull(cateId)) {
                return null;
            }

            // 获取类目映射的标签id
            RMap<String, String> cateId2TagIdMap =
                    redisson.getMap("CATE_2_售前咨询四级类_TAG_MAP", StringCodec.INSTANCE);
            if (cateId2TagIdMap.isEmpty()) {
                return null;
            }

            String tagId = cateId2TagIdMap.get(cateId + "");
            if (ObjectUtils.isEmpty(tagId)) {
                log.warn("not found tag by cate id, state mapped val = {}", stateMappedVal);
                return null;
            }

            return tagId;
        } catch (Exception e) {
            log.error("getAddTagId error, state mapped val = {}", stateMappedVal, e);
        }

        return null;
    }
}
