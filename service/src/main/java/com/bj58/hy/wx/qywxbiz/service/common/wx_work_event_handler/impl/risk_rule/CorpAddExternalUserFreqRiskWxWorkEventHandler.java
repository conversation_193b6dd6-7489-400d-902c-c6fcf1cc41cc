package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.impl.risk_rule;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.AbstractExternalContactEventBo;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.AddExternalContactEventBo;
import com.bj58.hy.wx.qywxbiz.service.bo.RateLimiterWrapper;
import lombok.NonNull;
import org.redisson.api.RBucket;
import org.redisson.api.RateIntervalUnit;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 企业 添加 外部联系人的 频率 风控规则校验
 *
 * <AUTHOR>
 */
@Component
public class CorpAddExternalUserFreqRiskWxWorkEventHandler extends AbstractCorpAddExternalUserRiskWxWorkEventHandler {

    @Override
    public void process(final @NonNull AddExternalContactEventBo event) {
        AbstractExternalContactEventBo.Ext ext = event.getExt();
        if (ObjectUtils.isNull(ext)) {
            return;
        }

        String operUserId = ext.getOperUserId();
        if (ObjectUtils.isEmpty(operUserId)) {
            return;
        }

        // 过滤掉 在职、离职 继承的数据
        if (Objects.equals(202, ext.getAddWay())) {
            return;
        }

        checkHasBeenTriggeredCorpLimit(event);
        checkHasBeenTriggeredCorpUserLimit(event);
    }

    private void checkHasBeenTriggeredCorpLimit(final @NonNull AddExternalContactEventBo event) {
        // 判断当前企业成员 是否超过了 添加的频率限制
        RateLimiterWrapper rateLimiter = getCorpAddRateLimiter(event);
        if (rateLimiter.couldExecute()) {
            return; // 没有超过频率限制
        }

        long rate = rateLimiter.getRate();
        long rateInterval = rateLimiter.getRateInterval();
        RateIntervalUnit rateIntervalUnit = rateLimiter.getRateIntervalUnit();

        log.info("[{}]当前企业添加客户频率过高, rate = {}, rate interval = {}, rate interval unit = {}", event.getCorpId(),
                rate, rateInterval, rateIntervalUnit.name());

        RBucket<String> bucket = redisson.getBucket(
                String.format("CorpAddExternalUserFreqRisk:%s:SEND_MEISHI_FLAG", event.getCorpId()), StringCodec.INSTANCE);
        if (bucket.isExists()) {
            return;
        }

        lockSupport.executeWithoutResult(
                bucket.getName() + "_LOCK_",
                () -> {
                    if (bucket.isExists()) { // 已经发送过告警，不再重复发送
                        return;
                    }

                    Map<String, String> content = new LinkedHashMap<>();
                    content.put("通知内容：", "当前企业添加客户频率过高，请尽快确认情况");
                    content.put("企业主体：", event.getCorpId());
                    content.put("告警频率：", String.format("每 %s %s 添加 %s个",
                            rateInterval, rateIntervalUnit.name().toLowerCase(), rate));
                    riskAlarmComponent.alarm("企业微信风控通知", content);

                    bucket.set("1", riskRuleComponent.getAlarmTimeInterval(), TimeUnit.SECONDS);
                }
        );
    }

    private void checkHasBeenTriggeredCorpUserLimit(final @NonNull AddExternalContactEventBo event) {
        // 判断当前企业成员 是否超过了 添加的频率限制
        RateLimiterWrapper rateLimiter = getCorpUserAddRateLimiter(event);
        if (rateLimiter.couldExecute()) {
            return; // 没有超过频率限制
        }

        long rate = rateLimiter.getRate();
        long rateInterval = rateLimiter.getRateInterval();
        RateIntervalUnit rateIntervalUnit = rateLimiter.getRateIntervalUnit();

        log.info("[{},{}]当前企业成员添加客户频率过高, rate = {}, rate interval = {}, rate interval unit = {}",
                event.getCorpId(), event.getUserId(),
                rate, rateInterval, rateIntervalUnit.name());

        RBucket<String> bucket = redisson.getBucket(
                String.format("CorpUserAddExternalUserFreqRisk:%s:%s:SEND_MEISHI_FLAG", event.getCorpId(), event.getUserId()), StringCodec.INSTANCE);
        if (bucket.isExists()) {
            return;
        }

        lockSupport.executeWithoutResult(
                bucket.getName() + "_LOCK_",
                () -> {
                    if (bucket.isExists()) { // 已经发送过告警，不再重复发送
                        return;
                    }

                    Map<String, String> content = new LinkedHashMap<>();
                    content.put("通知内容：", "当前企业成员添加客户频率过高，请尽快确认情况");
                    content.put("企业主体：", event.getCorpId());
                    content.put("企业成员：", event.getUserId());
                    content.put("告警频率：", String.format("每 %s %s 添加 %s个",
                            rateInterval, rateIntervalUnit.name().toLowerCase(), rate));
                    riskAlarmComponent.alarm("企业微信风控通知", content);

                    bucket.set("1", riskRuleComponent.getAlarmTimeInterval(), TimeUnit.SECONDS);
                }
        );
    }

}
