# 企微客服系统监控使用说明

## 概述

本监控系统为企微客服系统提供全面的监控能力，包括欢迎语、AI聊天、消息处理、用户满意度和外部服务等各个方面的监控指标。监控数据存储在Redis中，支持3天数据保留，并提供Web页面进行可视化展示。

## 设计原则

1. **异步执行**：所有监控逻辑都通过异步方式执行，避免影响主业务流程
2. **异常隔离**：监控异常不会影响主业务逻辑
3. **数据持久化**：监控数据存储在Redis中，支持3天数据保留
4. **可视化展示**：提供Web页面进行监控数据的可视化展示
5. **实时更新**：支持实时数据更新和自动刷新

## 核心组件

### 1. AsyncMonitorService
异步监控服务，将监控数据存储到Redis中，支持以下功能：
- 计数器数据存储（按日期分组）
- 详细记录数据存储（时间序列数据）
- 自动过期设置（3天）

### 2. MonitorDataService
监控数据查询服务，从Redis中查询和统计监控数据：
- 计算成功率、平均值等统计指标
- 获取时间序列数据
- 支持多天数据聚合

### 3. Web界面组件
- `MonitorViewController`：页面控制器
- `MonitorController`：API接口控制器
- Thymeleaf模板：监控页面模板

### 4. MonitorThreadPoolConfig
监控专用线程池配置，确保监控任务不影响主业务。

## 监控指标

### 欢迎语监控
- 欢迎语生成成功率 (corpId, bizScene, < 95%)
- 欢迎语发送成功率 (corpId, userId, < 90%)
- 重试次数分布 (corpId, 平均重试 > 2次)
- 发送耗时 (corpId, retryCount, P99 > 15s)
- Redis缓存命中率 (< 80%)

### AI聊天监控
- AI调用成功率 (corpId, callType, < 90%)
- AI响应时间 (corpId, sync/async, P99 > 20s)
- AI结果类型分布 (corpId, resultType, 转人工率 > 30%)
- 异步任务积压 (> 50)

### 消息处理监控
- 消息处理总量 (corpId, messageType)
- AI承接率 (corpId, userId, < 70%)
- 消息处理延迟 (corpId, stage, P95 > 25s)

### 用户满意度监控
- 用户满意度 (corpId, userId, < 60%)
- 首次咨询承接率 (corpId, < 85%)
- 人工介入率 (corpId, userId, > 40%)

### 外部服务监控
- Dify API调用成功率 (corpId, < 95%)
- 订单查询成功率 (orderType, < 98%)
- 消息队列积压 (> 1000)

## 数据存储结构

### Redis Key设计

#### 计数器数据
- 格式：`{category}:{action}:{dimension}:{date}`
- 示例：`welcome:generate:success:ww5:default:2024-01-01`
- 数据类型：RAtomicLong（原子性计数器）
- 过期时间：3天

#### 详细记录数据
- 格式：`{category}:{action}:detail`
- 示例：`welcome:generate:success:detail`
- 数据格式：`timestamp|data1|data2|...`
- 过期时间：3天，最多保留1000条记录

## 使用方式

### 1. 在业务代码中添加监控

```java
@Autowired
private AsyncMonitorService asyncMonitorService;

// 记录欢迎语生成成功
asyncMonitorService.recordWelcomeGenerateSuccess(corpId, bizScene);

// 记录AI调用失败
asyncMonitorService.recordAiCallFailure(corpId, "sync", "网络超时");

// 记录消息处理延迟
asyncMonitorService.recordMessageProcessLatency(corpId, "total", 1500);
```

### 2. 访问监控页面

访问以下URL查看监控页面：

```bash
# 监控首页
http://localhost:8080/monitor/view/

# 监控概览
http://localhost:8080/monitor/view/overview?corpId=ww5&days=3

# 欢迎语监控
http://localhost:8080/monitor/view/welcome?corpId=ww5&days=3

# AI聊天监控
http://localhost:8080/monitor/view/ai-chat?corpId=ww5&days=3
```

### 3. 使用API接口

通过REST API查询监控数据：

```bash
# 获取欢迎语监控指标
GET /monitor/api/welcome?corpId=ww5&bizScene=default&days=3

# 获取AI聊天监控指标
GET /monitor/api/ai-chat?corpId=ww5&days=3

# 获取监控概览
GET /monitor/api/overview?corpId=ww5&days=3
```

## 页面功能

### 监控首页
- 系统介绍和功能概览
- 快速导航到各个监控页面
- 企业和时间范围选择

### 监控概览页面
- 关键指标卡片展示
- 各类监控指标的趋势图表
- 快速导航链接

### 专项监控页面
- 欢迎语监控：生成成功率、缓存命中率、重试次数、发送耗时
- AI聊天监控：调用成功率、响应时间、结果类型分布、承接率
- 消息处理监控：处理总量、延迟、首次咨询承接率、人工介入
- 用户满意度监控：满意度调查、反馈统计
- 外部服务监控：Dify API、订单查询成功率

## 配置说明

### 线程池配置
```java
// 核心线程数：2
// 最大线程数：8
// 队列容量：1000
// 拒绝策略：DiscardPolicy（直接丢弃）
```

### 数据保留策略
- Redis数据保留时间：3天
- 详细记录最大条数：1000条
- 自动过期清理

## 最佳实践

### 1. 监控埋点
- 在关键业务节点添加监控埋点
- 使用异步方式记录监控数据
- 确保监控异常不影响主业务

### 2. 性能考虑
- 监控数据记录要轻量化
- 避免在监控逻辑中进行复杂计算
- 合理设置线程池参数

### 3. 告警策略
- 设置合理的告警阈值
- 避免告警风暴
- 重要指标设置多级告警

## 扩展指南

### 1. 新增监控指标
1. 在 `WMonitorEnum` 中添加新的监控指标
2. 在对应的监控组件中添加记录方法
3. 在 `AsyncMonitorService` 中添加异步方法
4. 在业务代码中添加监控埋点

### 2. 新增告警规则
1. 在 `MonitorAlarmService` 中添加检查方法
2. 设置合理的告警阈值
3. 在定时任务中调用检查方法

## 注意事项

1. **异步执行**：所有监控逻辑都是异步执行，不会阻塞主业务
2. **异常处理**：监控异常会被捕获并记录日志，不会影响主业务
3. **资源消耗**：监控系统会消耗一定的CPU和内存资源，但已经过优化
4. **数据一致性**：由于是异步执行，监控数据可能存在轻微延迟

## 监控数据查看

### 1. 通过Web页面查看
- 访问监控首页：`/monitor/view/`
- 选择企业和时间范围
- 查看各类监控指标的图表和详细数据

### 2. 通过API接口查看
- 使用REST API获取JSON格式的监控数据
- 支持程序化访问和集成

### 3. 数据特点
- 实时数据更新
- 支持历史数据查询（最多3天）
- 自动计算成功率、平均值等统计指标
- 支持多维度数据分析

## 告警系统

### 1. 告警配置
告警阈值可通过配置文件进行调整：

```yaml
# application-monitor.yml
monitor:
  alarm:
    welcome:
      generate-success-rate-threshold: 95.0
      cache-hit-rate-threshold: 80.0
    ai-chat:
      call-success-rate-threshold: 90.0
      takeover-success-rate-threshold: 70.0
    # ... 其他配置
```

### 2. 告警触发
- 定时任务自动检查：`MonitorAlarmJobHandler`
- 手动触发检查：`/monitor/alarm/check-all`
- 分类检查：`/monitor/alarm/check-welcome`等

### 3. 告警通知
通过现有的`RiskAlarmComponent`发送告警通知到美事群。

## API接口说明

### 监控数据查询
```bash
# 获取监控概览
GET /monitor/api/overview?corpId=ww5&days=3

# 获取欢迎语监控
GET /monitor/api/welcome?corpId=ww5&bizScene=default&days=3

# 获取AI聊天监控
GET /monitor/api/ai-chat?corpId=ww5&days=3
```

### 告警管理
```bash
# 手动触发告警检查
POST /monitor/alarm/check-all?corpId=ww5

# 获取告警配置
GET /monitor/alarm/config

# 获取告警阈值说明
GET /monitor/alarm/thresholds
```

## 技术特点

### 1. 高性能
- 异步数据记录，不影响主业务性能
- Redis高速存储，查询响应快
- 专用线程池，资源隔离

### 2. 高可用
- 监控异常不影响主业务
- 数据自动过期清理
- 优雅的错误处理
- 配置化告警阈值

### 3. 易用性
- 直观的Web界面
- 丰富的图表展示
- 自动刷新功能
- 响应式设计，支持移动端访问
- RESTful API接口

### 4. 可维护性
- 模块化设计
- 配置化管理
- 完善的日志记录
- 异常处理机制

### 5. Redis操作优化
- 使用RAtomicLong实现原子性计数器操作
- 支持高并发场景下的计数器增加
- 自动过期机制，避免内存泄漏
- 异常安全的Redis操作

## 部署说明

### 1. 配置文件
确保在`application.yml`中包含监控配置：
```yaml
spring:
  profiles:
    include: monitor
```

### 2. 定时任务配置
在调度平台配置`monitorAlarmJobHandler`定时任务，建议每5-10分钟执行一次。

### 3. 告警通知配置
确保`RiskAlarmComponent`已正确配置美事群通知。
