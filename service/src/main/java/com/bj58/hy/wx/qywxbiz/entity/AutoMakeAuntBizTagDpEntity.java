package com.bj58.hy.wx.qywxbiz.entity;

import com.bj58.hy.lib.spring.support.jpa.superclass.Identifiable;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * Description:
 *
 * <AUTHOR>
 */
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "t_qywx_make_aunt_biz_tag_dp", indexes = {
        @Index(name = "idx_58_id", columnList = "user_58_id, import_date"),
})
@Getter
public class AutoMakeAuntBizTagDpEntity extends Identifiable {

    /**
     * 外部联系人 58 id
     */
    @Column(name = "user_58_id", nullable = false)
    private Long externalUser58Id;

    /**
     * 阿姨服务城市
     */
    @Column(name = "service_city1_name", nullable = true)
    private String serviceCity1Name;

    /**
     * 阿姨技能四级类归属类目id
     */
    @Column(name = "service_cate4_id_skill", nullable = true, length = 1024)
    private String serviceCate4Ids;

    /**
     * 阿姨自动接单状态
     */
    @Column(name = "direct_dispatch_status", nullable = true)
    private String receivingStatus;

    /**
     * 阿姨入驻状态
     */
    @Column(name = "auth_state", nullable = true)
    private String joinInStatus;

    /**
     *阿姨绑定商家身份
     */
    @Column(name = "bind_buser_identity", nullable = true)
    private String bindingMerchantType;

    /**
     *累计众包完单数量
     */
    @Column(name = "zhongbao_service_finish_order_num", nullable = true)
    private String crowdsourcingFinishNum;

    /**
     * 累计平台完单数量
     */
    @Column(name = "service_finish_order_num", nullable = true)
    private String finishNum;

    /**
     * 上次众包完单距今间隔天数
     */
    @Column(name = "last_zhongbao_finish_interval_days", nullable = true)
    private String crowdsourcingLastFinishIntervalDays;

    /**
     * 阿姨服务等级
     */
    @Column(name = "aunt_service_level", nullable = true)
    private String aunt_service_level;

    /**
     * 末单前7天众包服务完成订单量
     */
    @Column(name = "last_order_pre7day_zhongbao_finish_order_num", nullable = true)
    private String last_order_pre7day_zhongbao_finish_order_num;

    /**
     * 末单前7天平台服务完成订单量
     */
    @Column(name = "last_order_pre7day_finish_order_num", nullable = true)
    private String last_order_pre7day_finish_order_num;

    /**
     * 近10单服务率【保洁清洗类目】
     */
    @Column(name = "last_10_baojie_order_service_rate", nullable = true)
    private String last_10_baojie_order_service_rate;

    /**
     * 近10单投诉率【保洁清洗类目】
     */
    @Column(name = "last_10_baojie_order_complaint_rate", nullable = true)
    private String last_10_baojie_order_complaint_rate;

    /**
     * 是否报名钟点工阿姨（1：是，0：否）
     */
    @Column(name = "is_zhongdiangong_aunt", nullable = true)
    private Integer zhongdiangong_aunt;

    /**
     * 是否精选众包无完单钟点工阿姨（1：是，0：否）
     */
    @Column(name = "is_new_zhongdiangong_aunt", nullable = true)
    private Integer new_zhongdiangong_aunt;

    /**
     * 累计钟点工完单量
     */
    @Column(name = "zhongdiangong_finish_order_num", nullable = true)
    private Integer zhongdiangong_finish_order_num;

    /**
     * 性别（男、女）
     */
    @Column(name = "gender", nullable = true)
    private String gender;

    /**
     * 家服背调状态
     */
    @Column(name = "background_check_status", nullable = true)
    private String background_check_status;

    /**
     * 家服体检状态
     */
    @Column(name = "medical_status", nullable = true)
    private String medical_status;

    /**
     * 年龄
     */
    @Column(name = "age", nullable = true)
    private Integer age;

    /**
     * 数据导入时间，eg：20241001
     */
    @Column(name = "import_date", nullable = false)
    private String importDate;

}
