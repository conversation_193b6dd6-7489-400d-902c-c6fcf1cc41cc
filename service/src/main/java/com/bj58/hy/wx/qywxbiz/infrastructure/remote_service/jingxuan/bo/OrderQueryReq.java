package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo;

import com.bj58.hy.lib.core.support.pojo.PagedQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/5/12 14:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderQueryReq extends PagedQuery {

    /**
     * 58id
     */
    private Long wbUserId;

    /**
     * 服务类目
     */
    private Integer cateId;

    /**
     * 订单创建时间开始
     */
    private Long createStartTime;

    /**
     * 订单创建时间结束
     */
    private Long createEndTime;

}
