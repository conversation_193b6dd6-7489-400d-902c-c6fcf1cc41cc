package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class LbgAiExternalContactStateBo {

    /**
     * 58id或者imei等
     */
    @JSONField(name = "uid") // fastjson
    @JsonProperty("uid")// jackson
    private Long uid;

    /**
     * 四级类目id
     */
    @JSONField(name = "cateId") // fastjson
    @JsonProperty("cateId")// jackson
    private Integer cateId;

    /**
     * 订单id
     */
    @JSONField(name = "orderId") // fastjson
    @JsonProperty("orderId")// jackson
    private Long orderId;

    /**
     * 场景分类 , reWork: 不满意重做
     */
    @JSONField(name = "type") // fastjson
    @JsonProperty("type")// jackson
    private String type;

}
