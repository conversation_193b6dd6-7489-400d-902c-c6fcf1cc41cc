package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan;

import com.bj58.hy.fx.bcore.contract.IFxReWorkService;
import com.bj58.hy.fx.bcore.entity.CheckCanReworkOutputDto;
import com.bj58.hy.fx.bcore.entity.FxCoreResponseT;
import com.bj58.hy.fx.bcore.entity.ReworkOrderOutputDto;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywx.contract.IExternalContactService;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.ExternalContactBasicInfoResp;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.ExternalContactFollowInfoResp;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.ExternalContactInfoResp;
import com.bj58.hy.wx.qywxbiz.contract.dto.rework.ReWorkPushReq;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.enums.ReWorkPushEnum;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiSendMessageComp;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/31 11:19
 */
@Slf4j
@Component
public class JingXuanReWorkService {


    @SCFClient(lookup = IFxReWorkService.SCF_URL)
    private IFxReWorkService reWorkService;

    @SCFClient(lookup = IExternalContactService.SCF_URL)
    private IExternalContactService externalContactService;

    @Autowired
    private LbgAiSendMessageComp sendMessageComp;


    /**
     * 查询订单是否达到不满意重做要求
     * @param orderId
     * @return
     */
    public CheckCanReworkOutputDto checkCanRework(Long orderId) {

        try {
            FxCoreResponseT<CheckCanReworkOutputDto> checkCanReworkResult = reWorkService.checkCanRework(orderId);
            if (checkCanReworkResult.isSuccess()){
                return checkCanReworkResult.getData();
            }
        } catch (Exception e) {
            log.error("reWorkService.checkCanRework error orderId : " + orderId,e);
        }

        return null;
    }


    /**
     * 根据用户id获取满足不满足重做要求的订单列表
     * @param userId
     * @return
     */
    public List<ReworkOrderOutputDto> getCanReworkOrderByUid(Long userId){
        try {
            FxCoreResponseT<List<ReworkOrderOutputDto>> canReworkOrderResult = reWorkService.getCanReworkOrderByUid(userId);
            if (canReworkOrderResult.isSuccess()){
                return canReworkOrderResult.getData();
            }
        } catch (Exception e) {
            log.error("reWorkService.getCanReworkOrderByUid error userId : " + userId,e);
        }

        return null;
    }


    public void reWorkPush(ReWorkPushReq reWorkPushReq){

        log.info("reWorkPushReq:" + JacksonUtils.format(reWorkPushReq));

        ReWorkPushEnum reWorkPushEnum = ReWorkPushEnum.strictOf(reWorkPushReq.getScene());
        if (reWorkPushEnum == null){
            return;
        }

        // 获取企业微信账号
        try {
            Result<ExternalContactInfoResp> externalContactInfoRespResult = externalContactService.getExternalContactInfoBy58Id("ww5cfa32107e9a1f20", reWorkPushReq.getUserId());
            if (externalContactInfoRespResult.notSuccess()){
                log.error("获取对应外部联系人信息失败");
                return;
            }

            ExternalContactInfoResp externalContactInfoResp = externalContactInfoRespResult.getData();
            if (ObjectUtils.isEmpty(externalContactInfoResp)){
                log.error("externalContactInfoResp is null ");
                return;
            }

            String externalUserId = externalContactInfoResp.getExternalUserId();

            ExternalContactBasicInfoResp basicInfo = externalContactInfoResp.getBasicInfo();
            if (ObjectUtils.isEmpty(basicInfo)){
                log.error("basicInfo is null ");
                return;
            }

            List<ExternalContactFollowInfoResp> followInfosList = externalContactInfoResp.getFollowInfos();
            if (CollectionUtils.isEmpty(followInfosList)){
                log.error("followInfosList is null ");
                return;
            }

            List<ExternalContactFollowInfoResp> reversedList = followInfosList.stream()
                    .filter(externalContactFollowInfoResp -> StringUtils.isNotEmpty(externalContactFollowInfoResp.getState()))
                    .sorted(Comparator.comparing(ExternalContactFollowInfoResp::getCreateTime).reversed())
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(reversedList)){
                log.error("reversedList is null ");
                return;
            }

            // 获取最近添加的企微账号
            String wxUserId = reversedList.get(0).getUserId();
            String name = basicInfo.getName();

            log.info("获取最近添加的企微账号:" + wxUserId);

            if (StringUtils.isNotEmpty(reWorkPushEnum.getMessageToUser())){
                // 给企微人员发送应用消息
                sendMessageComp.sendTextToUser("ww5cfa32107e9a1f20", wxUserId, String.format(reWorkPushEnum.getMessageToUser(),name,reWorkPushReq.getOrderSkuName(),reWorkPushReq.getOrderId()));
            }

            if (StringUtils.isNotEmpty(reWorkPushEnum.getMessageToExternalUserId())){
                String message = "";
                if (reWorkPushEnum.getScene() == ReWorkPushEnum.MODIFY_TIME_MESSAGE_TO_EXTERNALUSER.getScene()){
                    message = String.format(reWorkPushEnum.getMessageToExternalUserId(),reWorkPushReq.getOrderId(),reWorkPushReq.getOrderServiceTime());
                }

                if (reWorkPushEnum.getScene() == ReWorkPushEnum.ORDER_CREATE_FAIL_MESSAGE_TO_USER.getScene()
                        || reWorkPushEnum.getScene() == ReWorkPushEnum.SERVICE_FAIL_MESSAGE_TO_EXTERNALUSER.getScene()){
                    message = String.format(reWorkPushEnum.getMessageToExternalUserId(),reWorkPushReq.getOrderServiceTime(),reWorkPushReq.getOrderId());
                }

                if (reWorkPushEnum.getScene() == ReWorkPushEnum.BAD_MESSAGE_TO_EXTERNALUSER.getScene()){
                    message = String.format(reWorkPushEnum.getMessageToExternalUserId(),reWorkPushReq.getOrderSkuName());
                }

                if (StringUtils.isNotEmpty(message)){
                    sendMessageComp.sendTextToExternalUser("ww5cfa32107e9a1f20", wxUserId,
                            externalUserId, message, "");
                }

            }


        } catch (Exception e) {
            log.error("reWorkPush error ,reWorkPushReq " + JacksonUtils.format(reWorkPushReq),e);
        }
    }
}
