package com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.comp;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.service.common.dify.DifyComponent;
import com.bj58.hy.wx.qywxbiz.service.common.dify.bo.DifyApiInfoBo;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DifyChatInterventionConditionsComponent {

    @Autowired
    private DifyComponent difyComponent;

    public boolean isAiUser(final String corpId,
                            final String botUserId) {
        if (ObjectUtils.isEmpty(botUserId)) {
            return false;
        }

        DifyApiInfoBo.Item chatflowApiInfo = difyComponent.getChatflowApiInfo(corpId, botUserId);
        return ObjectUtils.notEmpty(chatflowApiInfo);
    }

}
