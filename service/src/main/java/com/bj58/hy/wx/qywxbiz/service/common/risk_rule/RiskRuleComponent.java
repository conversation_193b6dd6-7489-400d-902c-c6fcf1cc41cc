package com.bj58.hy.wx.qywxbiz.service.common.risk_rule;

import com.bj58.hy.lib.core.util.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RiskRuleComponent {

    @Autowired
    protected RedissonClient redisson;

    public long getAlarmTimeInterval() {
        RBucket<Long> bucket = redisson.getBucket("RISK_ALARM_TIME_INTERVAL", LongCodec.INSTANCE);

        Long interval = bucket.get();
        if (ObjectUtils.isEmpty(interval) || interval <= 0) {
            interval = 600L;
        }

        return interval;
    }

}
