package com.bj58.hy.wx.qywxbiz.service.common.welcome.global;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ChatContentRemoteService;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.enums.MessageMarkEnum;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziEventHandler;
import lombok.NonNull;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class JuziChatEventThenMarkWelcomeTagHandler extends AbstractJuziEventHandler {

    @Autowired
    private ChatContentRemoteService chatContentRemoteService;

    @Autowired
    protected RedissonClient redisson;

    @Override
    public void process(@NonNull JSONObject event) {
        if (!StringUtils.equalsIgnoreCase(event.getString("Event"), "聊天消息回调")) {
            return;
        }

        ChatMessagesEventBo.Body callbackBo = event.getObject("Body", ChatMessagesEventBo.Body.class);
        if (ObjectUtils.isNull(callbackBo)) {
            log.error("not support convert to standard AiRelayTask, event body = {}", JacksonUtils.format(event));
            return;
        }

        Date timestamp = callbackBo.getStandardTimestamp();
        if (ObjectUtils.isNull(timestamp)) {
            log.error("not found standard timestamp, event body = {}", JacksonUtils.format(callbackBo));
            return;
        }

        // 托管账号掉线后重新登录，句子互动会推送近期的聊天记录（真垃呀~）
        if (System.currentTimeMillis() - timestamp.getTime() > 20 * 60 * 1000) {
            log.error("found historical event, event body = {}", JacksonUtils.format(callbackBo));
            return;
        }

        if (ObjectUtils.notEmpty(callbackBo.getRoomWecomChatId())) { // 群聊
            return;
        }

        final String messageId = callbackBo.getMessageId();

        // 是否标准化了聊天记录?
        JuziSingleChatContentRecord singleChatContentRecord = chatContentRemoteService.getSingleChatContentRecord(messageId);
        if (ObjectUtils.isNull(singleChatContentRecord)) {
            log.error("not found standard 1v1 chat content record, event body = {}", JacksonUtils.format(callbackBo));
            return;
        }

        if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            // 假定当前是客服发送的欢迎语消息，尝试分析数据并且记录
            tryMarkWelcomeMessage(singleChatContentRecord);
        }
    }


    @Override
    public boolean matched(@NonNull JSONObject event) {
        return true;
    }

    private void tryMarkWelcomeMessage(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        try {
            if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                return;
            }

            if (!Objects.equals(singleChatContentRecord.getMessageType(), 7)) {
                return;
            }


            String payload = singleChatContentRecord.getPayload();
            if (ObjectUtils.isEmpty(payload)) {
                log.error("curr chat content is text but not found content, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            JSONObject jsonObj = JSON.parseObject(payload);
            String text = jsonObj.getString("text");
            if (ObjectUtils.isEmpty(text)) {
                log.error("curr chat content is text but not found content, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            String key = String.format("WELCOME_MESSAGE:%s:%s:%s",
                    singleChatContentRecord.getCorpId(),
                    singleChatContentRecord.getUserId(),
                    singleChatContentRecord.getExternalUserId());
            RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);

            String welcomeMessage = bucket.get();
            if (ObjectUtils.isEmpty(welcomeMessage)) {
                return;
            }

            // 校验是否和欢迎语匹配
            if (!Objects.equals(welcomeMessage, text)) {
                return;
            }

            // 记录欢迎语标记
            chatContentRemoteService.recordSingleChatBizInfo(
                    singleChatContentRecord.getMessageId(),
                    "WELCOME_MESSAGE_MARK",
                    MessageMarkEnum.WELCOME.getName());

        } catch (Exception e) {
            log.error("tryMarkWelcomeMessage error messageId : " + singleChatContentRecord.getMessageId(), e);
        }
    }
}
