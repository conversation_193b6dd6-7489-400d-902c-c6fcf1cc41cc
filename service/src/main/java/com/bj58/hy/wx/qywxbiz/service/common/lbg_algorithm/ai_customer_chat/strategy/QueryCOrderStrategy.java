package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.strategy;

import com.bj58.hy.fx.bcore.contract.IFxStaffCoreService;
import com.bj58.hy.fx.bcore.entity.FxCoreResponseT;
import com.bj58.hy.fx.bcore.entity.staff.StaffInfoEntity;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.support.pojo.PagedResult;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.ai_customer.config.AiCustomerProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.cmcpc.CMCPC;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo.OrderQueryBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.CommonOrderQueryService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.bo.OrderInfoBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.AbstractQueryDataStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.bo.OrderInfo;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.bj58.lbg.business.plaform.oms.api.dto.query.SearchQuerySortField;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 查询C端用户订单列表
 * <AUTHOR>
 * @date 2025/5/12 11:36
 */
@Slf4j
@Component
public class QueryCOrderStrategy extends AbstractQueryDataStrategy {

    @Autowired
    private ExternalContactRemoteService externalContactService;

    @Autowired
    private AiCustomerProperties aiCustomerProperties;

    @Autowired
    private CommonOrderQueryService commonOrderQueryService;

    @SCFClient(lookup = IFxStaffCoreService.SCF_URL)
    private IFxStaffCoreService fxStaffCoreService;

    @Override
    public boolean matched(@NonNull Object biztypeObj) {

        if (StringUtils.equalsIgnoreCase(biztypeObj.toString(), "queryCUserOrder")) {
            return true;
        }

        return false;
    }

    @Override
    public Result<Object> process(@NonNull Map<String, Object> queryParam) {

        Object externalUserIdObj = queryParam.get("externalUserId");
        if (ObjectUtils.isEmpty(externalUserIdObj)) {
            return Result.failure("externalUserId is empty");
        }

        String externalUserId = externalUserIdObj.toString();

        // 外部联系人转userId
        Long externalUser58Id = externalContactService.get58IdByExternalUserId(aiCustomerProperties.getCorpId(), externalUserId);
        if (ObjectUtils.isEmpty(externalUser58Id)) {
            return Result.failure("externalUser58Id is empty");
        }

        OrderQueryBo orderQueryBo = new OrderQueryBo();
        orderQueryBo.setWbUserId(externalUser58Id);

        if (ObjectUtils.isNotEmpty(queryParam.get("cateId"))) {
            orderQueryBo.setCateId(Integer.parseInt(queryParam.get("cateId").toString()));
        }

        if (ObjectUtils.isNotEmpty(queryParam.get("createStartTime"))) {
            orderQueryBo.setOrderCreateTimeStart(new Date(Long.parseLong(queryParam.get("createStartTime").toString())));
        }

        if (ObjectUtils.isNotEmpty(queryParam.get("createEndTime"))) {
            orderQueryBo.setOrderCreateTimeEnd(new Date(Long.parseLong(queryParam.get("createEndTime").toString())));
        }

        if (ObjectUtils.isNotEmpty(queryParam.get("payStartTime"))) {
            orderQueryBo.setPayTimeStart(new Date(Long.parseLong(queryParam.get("payStartTime").toString())));
        }

        if (ObjectUtils.isNotEmpty(queryParam.get("payEndTime"))) {
            orderQueryBo.setPayTimeEnd(new Date(Long.parseLong(queryParam.get("payEndTime").toString())));
        }

        if (ObjectUtils.isNotEmpty(queryParam.get("serviceCompleteStartTime"))) {
            orderQueryBo.setServiceCompletionTimeStart(new Date(Long.parseLong(queryParam.get("serviceCompleteStartTime").toString())));
        }

        if (ObjectUtils.isNotEmpty(queryParam.get("serviceCompleteEndTime"))) {
            orderQueryBo.setServiceCompletionTimeEnd(new Date(Long.parseLong(queryParam.get("serviceCompleteEndTime").toString())));
        }

        // 新增订单状态查询参数
        if (ObjectUtils.isNotEmpty(queryParam.get("orderStatus"))) {
            Object orderStatusObj = queryParam.get("orderStatus");
            orderQueryBo.setOrderStatus(Integer.parseInt(orderStatusObj.toString()));
        }

        orderQueryBo.setQuerySearchSortEntityList(Collections
                .singletonList(SearchQuerySortField
                        .builder()
                        .orderType("desc")
                        .sortField("create_time").build()));
        orderQueryBo.setPage(1);
        orderQueryBo.setSize(100);

        PagedResult<OrderInfoBo> orderInfoBoPagedResult = commonOrderQueryService.pageOrder(orderQueryBo);
        log.info("QueryCOrderStrategy pageOrder req:{},result:{}", JacksonUtils.format(orderQueryBo), JacksonUtils.format(orderInfoBoPagedResult));
        if (ObjectUtils.isEmpty(orderInfoBoPagedResult) || ObjectUtils.isEmpty(orderInfoBoPagedResult.getResults())) {
            return Result.success(null);
        }

        List<OrderInfo> collect = orderInfoBoPagedResult.stream().map(orderInfoBo -> {
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setCateId(orderInfoBo.getCateId());
            orderInfo.setOrderCreateTime(DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, orderInfoBo.getOrderCreateTime()));
            orderInfo.setOrderId(String.valueOf(orderInfoBo.getOrderId()));
            orderInfo.setCateName(CMCPC.getCategoryNameById(orderInfoBo.getCateId()));
            orderInfo.setServiceStartTime(DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, orderInfoBo.getServiceStartTime()));
            orderInfo.setServiceCompleteTime(DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, orderInfoBo.getServiceCompletionTime()));
            orderInfo.setServiceName(orderInfoBo.getProductName());
            orderInfo.setOrderStatus(orderInfoBo.getOrderStatusName());

            // 获取阿姨姓名
            try {
                Long orderId = orderInfoBo.getOrderId();
                // 获取服务人员信息
                FxCoreResponseT<StaffInfoEntity> staffInfo = fxStaffCoreService.getStaffToC(orderId);
                if (ObjectUtils.isNotEmpty(staffInfo) && staffInfo.isSuccess() && ObjectUtils.isNotEmpty(staffInfo.getData())) {
                    StaffInfoEntity staffInfoEntity = staffInfo.getData();
                    orderInfo.setStaffName(staffInfoEntity.getName());
                }
            } catch (Exception e) {
                log.error("Failed to get additional order info for orderId: {}, error:", orderInfoBo.getOrderId(), e);
            }

            return orderInfo;
        }).collect(Collectors.toList());

        return Result.success(JacksonUtils.format(collect));
    }
}
