package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.BufferedSource;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class DifyRemoteService {

    private static final OkHttpClient STREAM_HTTP_CLIENT = new OkHttpClient.Builder()
            .readTimeout(5, TimeUnit.MINUTES)
            .connectionPool(
                    new ConnectionPool(100, 5, TimeUnit.MINUTES)
            )
            .build();

    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json");

    private static final String DIFY_BASE_URL = "http://dify.58corp.com/v1";

    public Result<ChatflowResponse> chatflow(@NonNull final String apikey,
                                             @NonNull final ChatflowRequest chatflowRequest) {
        Map<String, Object> body = new LinkedHashMap<>();

        // 用户输入/提问内容
        body.put("query", chatflowRequest.getQuestion());

        // 允许传入 App 定义的各变量值。 inputs 参数包含了多组键值对（Key/Value pairs），每组的键对应一个特定变量，每组的值则是该变量的具体值。 默认{}
        body.put("inputs", ObjectUtils.notEmpty(chatflowRequest.getInputs()) ?
                chatflowRequest.getInputs() : Collections.emptyMap());

        // streaming 流式模式（推荐）。基于 SSE（Server-Sent Events）实现类似打字机输出方式的流式返回。
        // blocking 阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）。 由于 Cloudflare 限制，请求会在 100 秒超时无返回后中断。 注：Agent模式下不允许blocking。
        body.put("response_mode", "streaming");

        // 用户标识，用于定义终端用户的身份，方便检索、统计。
        // 由开发者定义规则，需保证用户标识在应用内唯一。
        body.put("user", chatflowRequest.getUser());

        // 会话 ID，需要基于之前的聊天记录继续对话，必须传之前消息的 conversation_id。
        body.put("conversation_id", chatflowRequest.getConversationId());

        body.put("files", Collections.emptyList());
        body.put("auto_generate_name", true);

        String bodyStr = JacksonUtils.format(body);

        RequestBody requestBody = RequestBody.create(JSON_MEDIA_TYPE, bodyStr);

        final Request request = new Request.Builder()
                .url(DIFY_BASE_URL + "/chat-messages")
                .header("Authorization", String.format("Bearer %s", apikey))
                .post(requestBody)
                .build();

        final CountDownLatch latch = new CountDownLatch(1);
        final Throwable[] errors = new Throwable[1];
        final List<JSONObject> streams = new ArrayList<>();

        final Call call = STREAM_HTTP_CLIENT.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                // 重试逻辑
                errors[0] = e;
                latch.countDown();
            }

            @Override
            public void onResponse(final Call call, final Response response) {
                if (!response.isSuccessful()) {
                    final String responseBody = getResponseBodyString(response);
                    String tips = Objects.nonNull(responseBody) ?
                            responseBody :
                            "Unexpected code: " + response.code();
                    errors[0] = new IOException(tips);
                    latch.countDown();
                    return;
                }

                try (BufferedSource source = response.body().source()) {
                    while (!source.exhausted()) {
                        String line = source.readUtf8Line();
                        if (log.isDebugEnabled()) {
                            log.debug("receive SSE response, line = {}", line);
                        }

                        if (ObjectUtils.notEmpty(line) && line.startsWith("data: ")) {
                            streams.add(JSON.parseObject(line.replaceFirst("data: ", "")));
                        }
                    }
                } catch (java.io.EOFException e) {
                    errors[0] = new RuntimeException("Unexpected EOF", e);
                } catch (Exception e) {
                    errors[0] = e;
                } finally {
                    latch.countDown();
                }
            }
        });

        // 等待最长30秒？
        boolean completed;
        try {
            completed = latch.await(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("latch wait interrupted", e);
            return Result.failure("latch wait interrupted");
        }

        if (!completed) { // 请求超时
            return Result.failure("Timeout waiting for SSE response");
        }

        if (errors[0] != null) {
            String errMsg = "execute failed, err msg = " + errors[0].getMessage();
            log.error(errMsg, errors[0]);
            return Result.failure(errors[0].getMessage());
        }

        log.info("received stream response success, req = {}, resp = {}",
                bodyStr, JacksonUtils.format(streams));

        // 首先判断是否有 event: error
        for (JSONObject item : streams) {
            if (Objects.equals(item.getString("event"), "error")) {
                // 证明流式输出过程中出现了异常
                return Result.failure(
                        String.format("event error, code = %s, message = %s",
                                item.getString("code"), item.getString("message"))
                );
            }
        }

        // 判断是否有截止？
        boolean b = false;
        for (JSONObject item : streams) {
            if (Objects.equals(item.getString("event"), "message_end")) {
                b = true;
                break;
            }
        }
        if (!b) {
            return Result.failure("not found end event");
        }

        ChatflowResponse response = new ChatflowResponse();
        StringBuilder answer = new StringBuilder();

        // 解析event = message | agent_message 的消息
        for (JSONObject item : streams) {
            // 所有的这几个字段，都一致
            String message_id = item.getString("message_id");
            String conversation_id = item.getString("conversation_id");
            int created_at = item.getIntValue("created_at");

            response.setMessageId(message_id);
            response.setConversationId(conversation_id);
            response.setCreatedAt(created_at);

            if (!Objects.equals(item.getString("event"), "message") &&
                    !Objects.equals(item.getString("event"), "agent_message")) {
                continue;
            }

            answer.append(item.getString("answer"));
        }
        response.setAnswer(answer.toString());

        // 解析结果
        return Result.success(response);
    }

    public Result<WorkflowResponse> workflow(@NonNull final String apiKey,
                                             @NonNull final WorkflowRequest workflowRequest) {
        Map<String, Object> body = new LinkedHashMap<>();

        // 允许传入 App 定义的各变量值。 inputs 参数包含了多组键值对（Key/Value pairs），每组的键对应一个特定变量，每组的值则是该变量的具体值。 默认{}
        body.put("inputs", ObjectUtils.notEmpty(workflowRequest.getInputs()) ?
                workflowRequest.getInputs() : Collections.emptyMap());

        // streaming 流式模式（推荐）。基于 SSE（Server-Sent Events）实现类似打字机输出方式的流式返回。
        // blocking 阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）。 由于 Cloudflare 限制，请求会在 100 秒超时无返回后中断。 注：Agent模式下不允许blocking。
        body.put("response_mode", "streaming");

        // 用户标识，用于定义终端用户的身份，方便检索、统计。
        // 由开发者定义规则，需保证用户标识在应用内唯一。
        body.put("user", workflowRequest.getUser());

        body.put("files", Collections.emptyList());

        String bodyStr = JacksonUtils.format(body);

        RequestBody requestBody = RequestBody.create(JSON_MEDIA_TYPE, bodyStr);

        final Request request = new Request.Builder()
                .url(DIFY_BASE_URL + "/workflows/run")
                .header("Authorization", String.format("Bearer %s", apiKey))
                .post(requestBody)
                .build();

        final CountDownLatch latch = new CountDownLatch(1);
        final Throwable[] errors = new Throwable[1];
        final List<JSONObject> streams = new ArrayList<>();

        final Call call = STREAM_HTTP_CLIENT.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                // 重试逻辑
                errors[0] = e;
                latch.countDown();
            }

            @Override
            public void onResponse(final Call call, final Response response) {
                if (!response.isSuccessful()) {
                    final String responseBody = getResponseBodyString(response);
                    String tips = Objects.nonNull(responseBody) ?
                            responseBody :
                            "Unexpected code: " + response.code();
                    errors[0] = new IOException(tips);
                    latch.countDown();
                    return;
                }

                try (BufferedSource source = response.body().source()) {
                    while (!source.exhausted()) {
                        String line = source.readUtf8Line();
                        if (log.isDebugEnabled()) {
                            log.debug("receive SSE response, line = {}", line);
                        }

                        if (ObjectUtils.notEmpty(line) && line.startsWith("data: ")) {
                            streams.add(JSON.parseObject(line.replaceFirst("data: ", "")));
                        }
                    }
                } catch (java.io.EOFException e) {
                    errors[0] = new RuntimeException("Unexpected EOF", e);
                } catch (Exception e) {
                    errors[0] = e;
                } finally {
                    latch.countDown();
                }
            }
        });

        // 等待最长30秒？
        boolean completed;
        try {
            completed = latch.await(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("latch wait interrupted", e);
            return Result.failure("latch wait interrupted");
        }

        if (!completed) { // 请求超时
            return Result.failure("Timeout waiting for SSE response");
        }

        if (errors[0] != null) {
            String errMsg = "execute failed, err msg = " + errors[0].getMessage();
            log.error(errMsg, errors[0]);
            return Result.failure(errors[0].getMessage());
        }

        log.info("received stream response success, req = {}, resp = {}",
                bodyStr, JacksonUtils.format(streams));

        WorkflowResponse response = new WorkflowResponse();

        for (JSONObject item : streams) {
            // 所有的这几个字段，都一致
            response.setWorkflowRunId(item.getString("workflow_run_id"));
            response.setTaskId(item.getString("task_id"));
            if (Objects.equals("workflow_finished", item.getString("event"))) {
                response.setData(item.getJSONObject("data"));
            }
        }

        // 解析结果
        return Result.success(response);
    }

    private static String getResponseBodyString(@Nullable final Response response) {
        if (ObjectUtils.isNull(response)) {
            return null;
        }

        try (ResponseBody responseBody = response.body()) {
            if (ObjectUtils.isNull(responseBody)) {
                return null;
            }
            String string = responseBody.string();

            if (ObjectUtils.notEmpty(string)) {

                try (JSONValidator validator = JSONValidator.from(string)) {
                    if (validator.validate()) {
                        return JSON.parseObject(string).toJSONString();
                    } else {
                        return string;
                    }
                }
            }
        } catch (Exception e) {
            log.error("get response body error", e);
        }

        return null;
    }

}
