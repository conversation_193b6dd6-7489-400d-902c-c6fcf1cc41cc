package com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.comp;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.exception.ServiceException;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywx.contract.IApplicationMessageService;
import com.bj58.hy.wx.qywx.contract.IMessageService;
import com.bj58.hy.wx.qywx.contract.dto.application_message.PassThroughSendApplicationMessageReq;
import com.bj58.hy.wx.qywx.contract.dto.application_message.PassThroughSendApplicationMessageResp;
import com.bj58.hy.wx.qywx.contract.dto.message.MessageResp;
import com.bj58.hy.wx.qywx.contract.dto.message.SendMessageReq;
import com.bj58.hy.wx.qywxbiz.infrastructure.ai_rate_limiter.AiCustomerApiRateLimiter;
import com.bj58.hy.wx.qywxbiz.infrastructure.ai_rate_limiter.AiCustomerApiRateLimiterType;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.ai_customer.config.AiCustomerProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.constants.WMonitorEnum;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DifyChatSendMessageComp {

    @Autowired
    private AiCustomerProperties aiCustomerProperties;

    @SCFClient(lookup = IApplicationMessageService.SCF_URL)
    private IApplicationMessageService applicationMessageService;

    @SCFClient(lookup = IMessageService.SCF_URL)
    private IMessageService messageService;

    public void sendTextToUser(@NonNull final String corpId,
                               @NonNull final String botUserId,
                               @NonNull final String message) {

        if (StringUtils.isBlank(botUserId) || StringUtils.isBlank(message)) {
            return;
        }

        PassThroughSendApplicationMessageReq applicationMessageReq = new PassThroughSendApplicationMessageReq();
        applicationMessageReq.setCorpId(corpId);
        applicationMessageReq.setAgentId(aiCustomerProperties.getAgentId());
        HashMap<String, Object> applicationMessageMap = Maps.newHashMap();
        applicationMessageMap.put("touser", botUserId);
        applicationMessageMap.put("msgtype", "text");
        applicationMessageMap.put("agentid", aiCustomerProperties.getAgentId());
        applicationMessageMap.put("text", new HashMap<String, Object>() {{
            put("content", message);
        }});

        applicationMessageMap.put("safe", 0);
        applicationMessageMap.put("enable_id_trans", 0);
        applicationMessageMap.put("enable_duplicate_check", 0);
        applicationMessageMap.put("duplicate_check_interval", 1800);
        applicationMessageReq.setBody(JacksonUtils.format(applicationMessageMap));
        try {
            log.info("DifyAiSendMessageComp.sendMessageToUser request = {}", JacksonUtils.format(applicationMessageReq));
            Result<PassThroughSendApplicationMessageResp> result = applicationMessageService.send(applicationMessageReq);
            log.info("DifyAiSendMessageComp.sendMessageToUser response = {}", JacksonUtils.format(result));

        } catch (Exception e) {
            log.error("DifyAiSendMessageComp.sendMessageToUser error, request = {}", JacksonUtils.format(applicationMessageReq), e);
        }
    }

    public void sendTextToExternalUser(@NonNull String corpId,
                                       @NonNull String botUserId,
                                       @NonNull String externalUserId,
                                       @NonNull String message,
                                       @NonNull String tipCorpUserWhenError) {
        try {
            AiCustomerApiRateLimiter rateLimiter = AiCustomerApiRateLimiter.getRateLimiter(
                    corpId, AiCustomerApiRateLimiterType.SEND_MESSAGE);
            rateLimiter.tryAcquireOrThrowEx(WMonitorEnum.AI_SEND_MESSAGE_ERROR_BECAUSE_OUT_OF_LIMIT);

        } catch (ServiceException e) {
            // 超出限制，发送人工信息
            this.sendTextToUser(corpId, externalUserId, tipCorpUserWhenError);
            return;
        }

        SendMessageReq sendMessageReq = new SendMessageReq();
        sendMessageReq.setCorpId(corpId);
        sendMessageReq.setWecomUserId(botUserId);
        sendMessageReq.setExternalUserId(externalUserId);
        sendMessageReq.setMessageType(7);
        HashMap<String, Object> payload = Maps.newHashMap();
        payload.put("text", message);
        sendMessageReq.setPayload(JacksonUtils.format(payload));

        try {
            log.info("DifyAiSendMessageComp.sendMessageToExternalUser request = {}", JacksonUtils.format(sendMessageReq));

            Result<MessageResp> result = messageService.send(sendMessageReq);

            log.info("DifyAiSendMessageComp.sendMessageToExternalUser response = {}", JacksonUtils.format(result));

        } catch (Exception e) {
            log.error("DifyAiSendMessageComp.sendMessageToExternalUser error, req = {}, errMsg = {}",
                    JacksonUtils.format(sendMessageReq), e.getMessage(), e);
        }
    }

}
