package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 各个业务线枚举
 */
@Getter
@AllArgsConstructor
public enum OrderClientIdEnum {
    //    JFFW(1336576390518935552L, "家庭服务-服务订单", OrderTypeEnum.SERVICE_ORDER),
//    JFYY(1369839051402137600L, "家庭服务-预约订单", OrderTypeEnum.SERVICE_ORDER),
    DJBANJIA(1070978870867718144L, "到家精选-搬家订单"),
    DJBAOJIE(3305652503130996856L, "到家精选-保洁3.0订单"),
//    DJCZK(1503277809366392832L, "到家精选-储值卡"),
//    DJFWK(1316600609792614400L, "到家精选-服务卡订单"),
    ;

    private final Long clientId;
    private final String desc;

    public final static Map<Long, OrderClientIdEnum> MAP = Arrays.stream(OrderClientIdEnum.values()).collect(Collectors.toMap(OrderClientIdEnum::getClientId, Function.identity()));

    public static OrderClientIdEnum getEnumByCode(Long code) {
        return MAP.get(code);
    }

}
