package com.bj58.hy.wx.qywxbiz.interfaces.wconfig;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.SkuCategoryBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29 11:36
 */
@Slf4j
@Component
public class WConfigComp {

    @Resource
    private WConfigCache wConfigCache;


    public List<String> getPushSkuCateConfig() {
        String pushSkuCateConfigStr = wConfigCache.getConfigValue("push_sku_cate_config");
        if (StringUtils.isEmpty(pushSkuCateConfigStr)) {
            return null;
        }

        return JSONObject.parseArray(pushSkuCateConfigStr, String.class);
    }


    public SkuCategoryBo getSkuCategoryConfig(String name) {
        try {
            String pushSkuMappingConfigStr = wConfigCache.getConfigValue("push_sku_mapping_config");
            if (StringUtils.isEmpty(pushSkuMappingConfigStr)) {
                return null;
            }

            JSONObject skuMappingMap = JSONObject.parseObject(pushSkuMappingConfigStr);

            String obj = skuMappingMap.getString(name);
            if (ObjectUtils.isEmpty(obj)) {
                return null;
            }

            SkuCategoryBo skuCategoryBo = JSONObject.parseObject(obj, SkuCategoryBo.class);
            if (ObjectUtils.isEmpty(skuCategoryBo)) {
                return null;
            }

            return skuCategoryBo;
        } catch (Exception e) {
            log.error("getSkuCategoryConfig error", e);
        }
        return null;
    }
}
