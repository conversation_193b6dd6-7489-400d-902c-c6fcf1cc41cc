package com.bj58.hy.wx.qywxbiz.service.bo;

import com.bj58.hy.lib.core.util.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 对应 表：t_biz_dict
 */
@Getter
@AllArgsConstructor
public enum BizSceneEnum {

    精选_售前扫码加微(BizLineEnum.精选.getCode(), 1, "c端入口-ai客服用户售前添加企微"),
    精选_售后扫码加微(BizLineEnum.精选.getCode(), 2, "c端入口-ai客服用户售后添加企微"),
    精选_投诉加企微(BizLineEnum.精选.getCode(), 3, "精选投诉加企微"),
    精选_维修阿姨端入口加企微(BizLineEnum.精选.getCode(), 4, "维修阿姨端入口-用户添加企微"),
    精选_保洁阿姨端入口加企微(BizLineEnum.精选.getCode(), 5, "保洁阿姨端入口-用户添加企微"),
    精选_支付成功订单详情页引导加企微(BizLineEnum.精选.getCode(), 6, "支付成功订单详情页引导用户添加企微"),
    精选_其他(BizLineEnum.精选.getCode(), 7, "其他"),


    家服_直聘拉群加企微(BizLineEnum.家服.getCode(), 4, "家服直聘加企微(拉群)"),

    家服_阿姨接单自动建群(BizLineEnum.家服.getCode(), 5, "家服阿姨接单自动建群"),

    家服_阿姨拉群加企微(BizLineEnum.家服.getCode(), 10, "家服阿姨加企微(拉群)"),


    开发_精选_售前扫码加微(BizLineEnum.开发一键拉群专用.getCode(), 1, "c端入口-ai客服用户售前添加企微"),
    开发_精选_售后扫码加微(BizLineEnum.开发一键拉群专用.getCode(), 2, "c端入口-ai客服用户售后添加企微"),
    开发_精选_投诉加企微(BizLineEnum.开发一键拉群专用.getCode(), 3, "精选投诉加企微"),
    开发_精选_维修阿姨端入口加企微(BizLineEnum.开发一键拉群专用.getCode(), 4, "维修阿姨端入口-用户添加企微"),
    开发_精选_保洁阿姨端入口加企微(BizLineEnum.开发一键拉群专用.getCode(), 5, "保洁阿姨端入口-用户添加企微"),
    开发_精选_支付成功订单详情页引导加企微(BizLineEnum.开发一键拉群专用.getCode(), 6, "支付成功订单详情页引导用户添加企微"),
    开发_精选_其他(BizLineEnum.开发一键拉群专用.getCode(), 7, "其他"),



    主站_B端拉群(BizLineEnum.主站.getCode(), 1, "主站_B端拉群"),
    主站_C端拉群(BizLineEnum.主站.getCode(), 2, "主站_C端拉群"),

    ;

    private final int lineId;

    private final int sceneId;

    private final String sceneName;


    public static BizSceneEnum strictOf(Integer bizLine, Integer bizScene) {
        BizSceneEnum value = of(bizLine, bizScene);
        if (ObjectUtils.isNull(value)) {
            throw new UnsupportedOperationException(
                    String.format("unsupported enum, biz line = %s, biz scene = %s", bizLine, bizScene));
        }

        return value;
    }

    public static BizSceneEnum of(Integer bizLine, Integer bizScene) {
        for (BizSceneEnum item : BizSceneEnum.values()) {

            if (Objects.equals(item.getSceneId(), bizScene) &&
                    Objects.equals(item.getLineId(), bizLine)) {

                return item;
            }
        }
        return null;
    }
}
