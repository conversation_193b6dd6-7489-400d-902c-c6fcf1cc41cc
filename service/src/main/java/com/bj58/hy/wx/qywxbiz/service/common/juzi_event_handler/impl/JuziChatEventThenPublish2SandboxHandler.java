package com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziEventHandler;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.comp.JuziChatEventThenPublish2SandboxComponent;
import lombok.NonNull;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:43
 */
@Component
public class JuziChatEventThenPublish2SandboxHandler extends AbstractJuziEventHandler {

    @Autowired
    protected RedissonClient redisson;

    @Autowired
    private JuziChatEventThenPublish2SandboxComponent juziChatEventThenPublish2SandboxComponent;

    @Override
    public void process(@NonNull JSONObject event) {
        juziChatEventThenPublish2SandboxComponent.publish(JacksonUtils.format(event));
    }

    @Override
    public boolean matched(@NonNull JSONObject event) {
        if (!StringUtils.equalsIgnoreCase(event.getString("Event"), "聊天消息回调")) {
            return false;
        }

        ChatMessagesEventBo.Body callbackBo = event.getObject("Body", ChatMessagesEventBo.Body.class);
        if (ObjectUtils.isNull(callbackBo)) {
            return false;
        }

        if (juziChatEventThenPublish2SandboxComponent.isMatch(callbackBo, false)) {
            return true;
        }

        return false;
    }
}
