package com.bj58.hy.wx.qywxbiz.interfaces.scf.common;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.spring.support.aspect.GlobalExceptionWrapper;
import com.bj58.hy.lib.spring.support.aspect.VerifiedParams;
import com.bj58.hy.wx.qywxbiz.contract.IGroupChatService;
import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.GetRecommendedOwnerIdReq;
import com.bj58.hy.wx.qywxbiz.service.common.group_chat.AbstractGroupChatOwnerStrategy;
import com.bj58.spat.scf.server.contract.annotation.ServiceBehavior;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 *
 */
@Slf4j
@Component
@ServiceBehavior
public class GroupChatService implements IGroupChatService {

    @Autowired
    private ObjectProvider<AbstractGroupChatOwnerStrategy> groupChatOwnerStrategies;

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<String> getRecommendedOwnerId(@NotNull @Valid final GetRecommendedOwnerIdReq req) throws Exception {

        AbstractGroupChatOwnerStrategy handler = groupChatOwnerStrategies.stream()
                .filter(p -> p.matched(req))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(handler)) {
            String errMsg = String.format("not found group chat owner handler, biz line = %s, biz scene = %s",
                    req.getBizLine(), req.getBizScene());
            log.error(errMsg);
            return Result.failure(errMsg);
        }

        String availableUserId = handler.getUserId(req);
        if (ObjectUtils.isEmpty(availableUserId)) {
            return Result.failure("暂无可用账号，请联系相应负责人");
        }

        return Result.success(availableUserId);
    }
}
