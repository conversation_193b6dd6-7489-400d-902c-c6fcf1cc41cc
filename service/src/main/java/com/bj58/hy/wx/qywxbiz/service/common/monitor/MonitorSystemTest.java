package com.bj58.hy.wx.qywxbiz.service.common.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 监控系统测试类
 * 用于验证监控系统的基本功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class MonitorSystemTest {

    @Autowired
    private AsyncMonitorService asyncMonitorService;

    @Autowired
    private MonitorDataService monitorDataService;

    @Autowired
    private MonitorAlarmService monitorAlarmService;

    /**
     * 测试监控数据记录
     */
    public void testMonitorDataRecording() {
        try {
            String corpId = "test_corp";
            String bizScene = "test_scene";
            String userId = "test_user";
            
            log.info("开始测试监控数据记录...");
            
            // 测试欢迎语监控
            asyncMonitorService.recordWelcomeGenerateSuccess(corpId, bizScene);
            asyncMonitorService.recordWelcomeSendSuccess(corpId, userId);
            asyncMonitorService.recordWelcomeCacheHit();
            
            // 测试AI聊天监控
            asyncMonitorService.recordAiCallSuccess(corpId, "sync");
            asyncMonitorService.recordAiTakeoverSuccess(corpId, userId);
            asyncMonitorService.recordAiResponseTime(corpId, "sync", 1500L);
            
            // 测试消息处理监控
            asyncMonitorService.recordChatMessageTotal(corpId, "EXTERNAL_USER");
            asyncMonitorService.recordFirstConsultTakeoverSuccess(corpId);
            asyncMonitorService.recordMessageProcessLatency(corpId, "total", 2000L);
            
            // 测试用户满意度监控
            asyncMonitorService.recordSatisfactionSurveySent(corpId, userId);
            asyncMonitorService.recordUserSatisfactionPositive(corpId, userId, "satisfied");
            
            // 测试外部服务监控
            asyncMonitorService.recordDifyApiCallSuccess(corpId);
            asyncMonitorService.recordOrderQuerySuccess("jingxuan");
            
            log.info("监控数据记录测试完成");
            
        } catch (Exception e) {
            log.error("监控数据记录测试失败", e);
        }
    }

    /**
     * 测试监控数据查询
     */
    public void testMonitorDataQuery() {
        try {
            String corpId = "test_corp";
            String bizScene = "test_scene";
            int days = 1;
            
            log.info("开始测试监控数据查询...");
            
            // 等待一段时间让异步数据写入完成
            Thread.sleep(2000);
            
            // 测试欢迎语监控数据查询
            var welcomeData = monitorDataService.getWelcomeMonitorData(corpId, bizScene, days);
            log.info("欢迎语监控数据: {}", welcomeData);
            
            // 测试AI聊天监控数据查询
            var aiChatData = monitorDataService.getAiChatMonitorData(corpId, days);
            log.info("AI聊天监控数据: {}", aiChatData);
            
            // 测试监控概览数据查询
            var overviewData = monitorDataService.getMonitorOverview(corpId, days);
            log.info("监控概览数据: {}", overviewData);
            
            log.info("监控数据查询测试完成");
            
        } catch (Exception e) {
            log.error("监控数据查询测试失败", e);
        }
    }

    /**
     * 测试监控告警
     */
    public void testMonitorAlarm() {
        try {
            String corpId = "test_corp";
            
            log.info("开始测试监控告警...");
            
            // 测试各类监控告警检查
            monitorAlarmService.checkWelcomeMessageAlarms(corpId, "test_scene");
            monitorAlarmService.checkAiChatAlarms(corpId);
            monitorAlarmService.checkMessageProcessAlarms(corpId);
            monitorAlarmService.checkUserSatisfactionAlarms(corpId);
            monitorAlarmService.checkExternalServiceAlarms(corpId);
            
            // 测试全量告警检查
            monitorAlarmService.checkAllAlarms(corpId);
            
            log.info("监控告警测试完成");
            
        } catch (Exception e) {
            log.error("监控告警测试失败", e);
        }
    }

    /**
     * 运行完整的监控系统测试
     */
    public void runFullTest() {
        log.info("开始运行完整的监控系统测试...");
        
        testMonitorDataRecording();
        testMonitorDataQuery();
        testMonitorAlarm();
        
        log.info("完整的监控系统测试完成");
    }
}
