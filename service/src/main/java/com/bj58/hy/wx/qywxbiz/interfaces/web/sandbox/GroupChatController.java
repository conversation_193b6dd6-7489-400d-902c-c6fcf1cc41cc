package com.bj58.hy.wx.qywxbiz.interfaces.web.sandbox;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.GetRecommendedOwnerIdReq;
import com.bj58.hy.wx.qywxbiz.interfaces.scf.common.GroupChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
public class GroupChatController {

    @Autowired
    private GroupChatService service;

    @RequestMapping("/getRecommendedOwnerId")
    public Result<?> getRecommendedOwnerId(@Valid @RequestBody GetRecommendedOwnerIdReq req) throws Exception {
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            return Result.failure("Unsupported");
        }

        return service.getRecommendedOwnerId(req);
    }

}
