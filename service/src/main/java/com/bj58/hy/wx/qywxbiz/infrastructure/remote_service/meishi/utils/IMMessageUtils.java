package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.utils;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.IMMessageBo;
import com.bj58.meishi.openapi.client.MeishiOpenapiMsgClient;
import com.bj58.meishi.openapi.entity.card.*;
import com.bj58.meishi.openapi.entity.common.TransJsonData;
import com.bj58.meishi.openapi.entity.msg.CardMsg;
import com.bj58.meishi.openapi.entity.msg.SendMsgVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class IMMessageUtils {

    private static final String SENDER_ID = "MIS_ROBOT_hyqywx";

    public static Result<String> sendSimpleMessageToOas(IMMessageBo messageBo) {
        if (ObjectUtils.isEmpty(messageBo)
                || ObjectUtils.isEmpty(messageBo.getContent())
                || ObjectUtils.isEmpty(messageBo.getReceives())) {
            return Result.failure("messageBo is not valid");
        }

        List<SendMsgVo> msgs = Lists.newArrayList();
        // 构建消息体
        for (String receiveOa : messageBo.getReceives()) {
            SendMsgVo msg = buildSimpleMessage(messageBo);
            msg.setToOa(receiveOa);
            msgs.add(msg);
        }

        if (ObjectUtils.isEmpty(msgs)) {
            return Result.failure("messageBo is not valid");
        }

        log.info("IMMessageUtils.sendSimpleMessageToOas, msgs = {}", msgs);
        TransJsonData<String> sentResult = MeishiOpenapiMsgClient.sendMsgBatch(UUID.randomUUID().toString(), msgs);
        log.info("IMMessageUtils.sendSimpleMessageToOas, result = {}", sentResult);
        if (ObjectUtils.isEmpty(sentResult)) {
            return Result.failure("send message failed");
        }
        if (!Objects.equals(sentResult.getCode(), "0")) {
            return Result.failure(sentResult.getMsg());
        }
        return Result.success(sentResult.getMsg());
    }

    private static SendMsgVo buildSimpleMessage(IMMessageBo messageBo) {
        InteractiveCard card = new InteractiveCard();
        card.setStateless(false);

        // 设置标题
        CardTitle cardTitle = new CardTitle();
        cardTitle.setText(messageBo.getTitle());
        cardTitle.setColor(messageBo.getLevel().getColor());
        card.setTitle(cardTitle);
        // 推送的消息标题
        card.setPushTitle(messageBo.getTitle());

        // 设置操作文案及按钮
        if (ObjectUtils.isNotEmpty(messageBo.getButton()) && ObjectUtils.isNotEmpty(messageBo.getButton().getMap())) {
            List<ActionField> actionFields = Lists.newArrayList();
            for (Map.Entry<String, String> button : messageBo.getButton().getMap().entrySet()) {
                ActionField actionField = new ActionField();
                actionField.setActionType("open");
                actionField.setText(button.getKey());
                actionField.setIsSelected(false);
                MultiUrl multiUrl = new MultiUrl();
                multiUrl.setPcUrl(button.getValue());
                multiUrl.setMobileUrl(button.getValue());
                actionField.setOpenUrl(multiUrl);
                actionFields.add(actionField);
            }
            Map<String, List<ActionField>> actionFieldMap = Maps.newHashMap();
            actionFieldMap.put("default", actionFields);

            Section<ActionField> actionSect = new Section<>();
            actionSect.setMutableElems(actionFieldMap);
            actionSect.setLayout("oneByOne");
            actionSect.setDefaultState("default");
            card.setActionSect(actionSect);
        }

        // 设置文案体
        Section<TextField> textSect = new Section<>();
        Map<String, List<TextField>> mutableElems = Maps.newHashMap();
        textSect.setMutableElems(mutableElems);
        textSect.setLayout("oneByOne");
        textSect.setDefaultState("default");

        List<TextField> textFields = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(messageBo.getContent()) && ObjectUtils.isNotEmpty(messageBo.getContent().getMap())) {
            for (Map.Entry<String, String> content : messageBo.getContent().getMap().entrySet()) {
                TextField textField = new TextField();
                textField.setContent(content.getValue());
                textField.setSecondContent(content.getKey());
                textFields.add(textField);
            }
            mutableElems.put("default", textFields);
        }
        card.setTextSect(textSect);
        card.setReplaceText(1);
        card.setCanForward(1);

        CardMsg cardMsg = new CardMsg(card);

        SendMsgVo msg = new SendMsgVo();
        msg.setSenderId(SENDER_ID);
        msg.setShowType(cardMsg.getType());
        msg.setContent(cardMsg.getContent());
        return msg;
    }

    public static void sendSimpleMessageToGroup(IMMessageBo messageBo) {
        if (ObjectUtils.isEmpty(messageBo)
                || ObjectUtils.isEmpty(messageBo.getContent())
                || ObjectUtils.isEmpty(messageBo.getReceives())) {
            return;
        }

        for (String receiveOa : messageBo.getReceives()) {
            SendMsgVo msg = buildSimpleMessage(messageBo);
            msg.setToId(receiveOa);
            log.info("IMMessageUtils.sendSimpleMessageToGroup, msg = {}", msg);
            TransJsonData<String> sendResult = MeishiOpenapiMsgClient.sengGroupMsg(UUID.randomUUID().toString(), msg);
            log.info("IMMessageUtils.sendSimpleMessageToGroup, result = {}", sendResult);
        }

    }


}
