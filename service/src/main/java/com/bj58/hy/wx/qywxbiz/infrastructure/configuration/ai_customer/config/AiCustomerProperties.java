package com.bj58.hy.wx.qywxbiz.infrastructure.configuration.ai_customer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "customer-ai")
public class AiCustomerProperties {

    // 企业id
    private String corpId;

    // 通知人工 应用消息配置
    private String agentId;
}
