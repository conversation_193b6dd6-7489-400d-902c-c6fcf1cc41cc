package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.impl.wx_work_state;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IExternalContactMappingService;
import com.bj58.hy.wx.qywx.contract.dto.external_contact_mapping.SaveExternalUserLinkedWuBaIdReq;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractWxWorkEventHandler;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Component
public class ExtractPassThroughWuBaWxWorkEventHandler extends AbstractWxWorkEventHandler {

    @SCFClient(lookup = IExternalContactMappingService.SCF_URL)
    private IExternalContactMappingService externalContactMappingService;

    @Override
    public void process(final @NonNull JSONObject event) {
        try {
            String corpId = event.getString("ToUserName");
            String externalUserID = event.getString("ExternalUserID");
            if (StringUtils.isBlank(corpId) || StringUtils.isBlank(externalUserID)) {
                return;
            }

            JSONObject ext = event.getJSONObject("Ext");
            if (ObjectUtils.isEmpty(ext)) {
                return;
            }

            JSONObject stateMappedVal = ext.getJSONObject("StateMappedVal");
            if (ObjectUtils.isEmpty(stateMappedVal)) {
                return;
            }

            JSONObject bindWuBa = stateMappedVal.getJSONObject("_BindWuBa_");
            if (ObjectUtils.isNull(bindWuBa)) {
                return;
            }

            String reason = bindWuBa.getString("reason");
            Long wuBaId = bindWuBa.getLong("id");
            if (StringUtils.isBlank(reason) || ObjectUtils.isNull(wuBaId)) {
                return;
            }

            log.info("found external user linked wuba, info = {}", event.toJSONString());

            SaveExternalUserLinkedWuBaIdReq saveExternalUserLinkedWuBaReq = new SaveExternalUserLinkedWuBaIdReq();
            saveExternalUserLinkedWuBaReq.setCorpId(corpId);
            saveExternalUserLinkedWuBaReq.setExternalUserId(externalUserID);
            saveExternalUserLinkedWuBaReq.setWubaId(wuBaId);
            saveExternalUserLinkedWuBaReq.setReason(reason);
            externalContactMappingService.saveExternalUserLinkedWuBaId(saveExternalUserLinkedWuBaReq);

            log.info("save external user linked wuba success, info = {}",
                    JacksonUtils.format(saveExternalUserLinkedWuBaReq));
        } catch (Exception e) {
            log.error("ExtractPassThroughWuBaWxWorkEventHandler process error , event:{}", JSONObject.toJSONString(event), e);
        }
    }

    @Override
    public boolean matched(final @NonNull JSONObject event) {
        return true;
    }
}
