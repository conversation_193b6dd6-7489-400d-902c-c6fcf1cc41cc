package com.bj58.hy.wx.qywxbiz.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @date 2025/5/20 17:57
 */
@Slf4j
public class IpUtils {

    /**
     * 获取当前服务ip
     */
    public static String getIp() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();

        } catch (UnknownHostException e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }
}
