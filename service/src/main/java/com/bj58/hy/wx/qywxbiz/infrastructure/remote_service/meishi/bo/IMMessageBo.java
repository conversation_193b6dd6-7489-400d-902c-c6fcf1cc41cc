package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo;

import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.enums.IMMessageLevelEnum;
import lombok.Data;

import java.util.*;

@Data
public class IMMessageBo {

    // 标题
    private String title;

    // 消息等级
    private IMMessageLevelEnum level = IMMessageLevelEnum.Warning;

    // 接收人
    private List<String> receives;

    // 消息内容
    private TitleTextMap content;

    // 按钮
    private ButtonMap button;

    @Data
    public static class TitleTextMap {
        // 获取整个映射
        private final Map<String, String> map;

        // 构造方法
        public TitleTextMap() {
            this.map = new LinkedHashMap<>();
        }

        // 添加键值对
        public void put(String title, String text) {
            map.put(title, text);
        }

    }

    @Data
    public static class ButtonMap {

        // 获取整个映射
        private final Map<String, String> map;

        // 构造方法
        public ButtonMap() {
            this.map = new LinkedHashMap<>();
        }

        // 添加键值对
        public void put(String text, String url) {
            map.put(text, url);
        }

    }

}