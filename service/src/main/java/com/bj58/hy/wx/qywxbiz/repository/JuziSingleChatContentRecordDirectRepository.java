package com.bj58.hy.wx.qywxbiz.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywx.contract.dto.message.MessagePayload;
import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.bj58.hy.wx.qywxbiz.entity.JuziSingleChatContentRecordEntity;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.google.common.collect.Maps;
import com.bj58.hy.wx.qywxbiz.repository.jpa.JuziSingleChatContentRecordJpaRepository;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 句子聊天记录Repository - 直接查询数据库版本
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JuziSingleChatContentRecordDirectRepository {

    private final JuziSingleChatContentRecordJpaRepository jpaRepository;

    /**
     * 查询指定客服当天的聊天记录（按external_user_id分组）- 直接查询数据库
     */
    public Map<String, List<LbgAiMessageChatRecordBo>> getChatRecordsByExternalUser(@NonNull final String corpId,
                                                                                    @NonNull final String userId,
                                                                                    @NonNull final String analysisDate) {

        Map<String, List<LbgAiMessageChatRecordBo>> result = Maps.newHashMap();

        try {
            // 解析分析日期
            Date startDate = DateUtils.strToDate(analysisDate, DateUtils.yyyyMMdd);
            if (startDate == null) {
                log.error("无效的分析日期格式：{}", analysisDate);
                return result;
            }

            Date endDate = DateUtils.addDay(startDate, 1);

            log.info("直接查询数据库聊天记录，corpId={}, userId={}, startTime={}, endTime={}",
                    corpId, userId, startDate, endDate);

            List<JuziSingleChatContentRecordEntity> allRecords = jpaRepository.findChatRecords(corpId, userId, startDate, endDate);

            log.info("查询到聊天记录总数：{}，corpId={}, userId={}", allRecords.size(), corpId, userId);

            // 按对话分组处理聊天记录
            result = groupChatRecordsByConversation(allRecords, userId);

            // 对每个外部用户的聊天记录按时间排序
            result.forEach((externalUserId, chatRecords) ->
                    chatRecords.sort(Comparator.comparingLong(LbgAiMessageChatRecordBo::getDateStamp)));

            log.info("按外部用户分组后的聊天记录：{}个外部用户，corpId={}, userId={}",
                    result.size(), corpId, userId);

        } catch (Exception e) {
            log.error("查询聊天记录失败，corpId={}, userId={}, analysisDate={}", corpId, userId, analysisDate, e);
        }

        return result;
    }

    /**
     * 按对话分组聊天记录
     * 简化版：直接使用实体中的externalUserId字段进行分组
     */
    private Map<String, List<LbgAiMessageChatRecordBo>> groupChatRecordsByConversation(
            List<JuziSingleChatContentRecordEntity> allRecords, String corpUserId) {

        // 按时间排序
        allRecords.sort(Comparator.comparing(JuziSingleChatContentRecordEntity::getCreateTime));

        // 直接按externalUserId分组并转换
        Map<String, List<LbgAiMessageChatRecordBo>> result = allRecords.stream()
                .map(record -> convertToLbgAiMessageChatRecordBo(record, corpUserId))
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        chatRecord -> chatRecord.getIsUserSend() ? chatRecord.getSender() : chatRecord.getReceiver(),
                        Collectors.toList()
                ));

        // 过滤掉客户没有发送过消息的聊天记录
        result = result.entrySet().stream()
                .filter(entry -> entry.getValue().stream()
                        .anyMatch(LbgAiMessageChatRecordBo::getIsUserSend))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue));

        log.info("按外部用户分组后的聊天记录：{}个外部用户", result.size());
        return result;
    }


    /**
     * 将JuziSingleChatContentRecordEntity转换为LbgAiMessageChatRecordBo
     */
    private LbgAiMessageChatRecordBo convertToLbgAiMessageChatRecordBo(JuziSingleChatContentRecordEntity record,
                                                                       String corpUserId) {
        try {
            if (record == null || StringUtils.isEmpty(record.getPayload())) {
                return null;
            }

            String messageContent = parseMessageContent(record);
            if (StringUtils.isEmpty(messageContent)) {
                return null;
            }

            LbgAiMessageChatRecordBo chatRecord = new LbgAiMessageChatRecordBo();
            chatRecord.setDateStamp(record.getCreateTime().getTime());
            chatRecord.setMessage(messageContent);
            chatRecord.setMessageId(record.getMessageId());

            // 根据发送方设置sender和receiver
            if (record.getSendBy() == SingleChatSendBy.EXTERNAL_USER) {
                // 外部用户发送的消息
                chatRecord.setSender(record.getExternalUserId());
                chatRecord.setReceiver(corpUserId);
                chatRecord.setIsUserSend(true);
            } else {
                // 企业用户发送的消息
                chatRecord.setSender(corpUserId);
                chatRecord.setReceiver(record.getExternalUserId());
                chatRecord.setIsUserSend(false);
            }

            return chatRecord;

        } catch (Exception e) {
            log.error("转换聊天记录失败，messageId={}", record.getMessageId(), e);
            return null;
        }
    }

    /**
     * 解析消息内容
     */
    private String parseMessageContent(JuziSingleChatContentRecordEntity record) {
        try {
            String payload = record.getPayload();
            Integer messageType = record.getMessageType();

            if (messageType == null || StringUtils.isEmpty(payload)) {
                return null;
            }

            // 根据消息类型解析内容
            switch (messageType) {
                case 7: // 文本消息
                    MessagePayload.TextPayload textPayload = JacksonUtils.parse(payload, MessagePayload.TextPayload.class);
                    return textPayload != null ? textPayload.getText() : null;

                case 2: // 语音消息
                    JSONObject voiceJson = JSON.parseObject(payload);
                    return voiceJson.getString("text");

                default:
                    // 其他类型消息，直接返回payload
                    return payload;
            }
        } catch (Exception e) {
            log.error("解析消息内容失败，messageId={}, payload={}", record.getMessageId(), record.getPayload(), e);
            return record.getPayload(); // 解析失败时返回原始payload
        }
    }

}
