package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.AddExternalContactEventBo;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.ExternalContactEventCallbackType;
import lombok.NonNull;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractAddExternalContactEventHandler extends AbstractWxWorkEventHandler {

    @Autowired
    protected RedissonClient redisson;

    @Autowired
    protected RedisLockSupport lockSupport;

    @Override
    public void process(@NonNull JSONObject event) {
        ExternalContactEventCallbackType callbackType =
                ExternalContactEventCallbackType.getEnumByJson(event);

        if (!ExternalContactEventCallbackType.ADD_EXTERNAL_CONTACT.equals(callbackType)) {
            return;
        }

        // 校验是否是加好友的消息
        AddExternalContactEventBo eventMsg = JSON.parseObject(event.toJSONString(), AddExternalContactEventBo.class);
        if (ObjectUtils.isEmpty(eventMsg) || ObjectUtils.isEmpty(eventMsg.getExt())) {
            return;
        }

        process(eventMsg);
    }

    protected abstract void process(@NonNull AddExternalContactEventBo event);

    @Override
    public boolean matched(final @NonNull JSONObject event) {
        return true;
    }
}
