package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.exception.ServiceException;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywx.contract.IApplicationMessageService;
import com.bj58.hy.wx.qywx.contract.IMessageService;
import com.bj58.hy.wx.qywx.contract.dto.application_message.PassThroughSendApplicationMessageReq;
import com.bj58.hy.wx.qywx.contract.dto.application_message.PassThroughSendApplicationMessageResp;
import com.bj58.hy.wx.qywx.contract.dto.message.MessagePayload;
import com.bj58.hy.wx.qywx.contract.dto.message.MessageResp;
import com.bj58.hy.wx.qywx.contract.dto.message.SendMessageReq;
import com.bj58.hy.wx.qywxbiz.infrastructure.ai_rate_limiter.AiCustomerApiRateLimiter;
import com.bj58.hy.wx.qywxbiz.infrastructure.ai_rate_limiter.AiCustomerApiRateLimiterType;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.ai_customer.config.AiCustomerProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.constants.WMonitorEnum;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.SkuCategoryBo;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LbgAiSendMessageComp {

    @Autowired
    private AiCustomerProperties aiCustomerProperties;

    @SCFClient(lookup = IApplicationMessageService.SCF_URL)
    private IApplicationMessageService applicationMessageService;

    @SCFClient(lookup = IMessageService.SCF_URL)
    private IMessageService messageService;

    public void sendTextToUser(@NonNull final String corpId,
                               @NonNull final String botUserId,
                               @NonNull final String message) {

        if (StringUtils.isBlank(botUserId) || StringUtils.isBlank(message)) {
            return;
        }

        PassThroughSendApplicationMessageReq applicationMessageReq = new PassThroughSendApplicationMessageReq();
        applicationMessageReq.setCorpId(corpId);
        applicationMessageReq.setAgentId(aiCustomerProperties.getAgentId());
        HashMap<String, Object> applicationMessageMap = Maps.newHashMap();
        applicationMessageMap.put("touser", botUserId);
        applicationMessageMap.put("msgtype", "text");
        applicationMessageMap.put("agentid", aiCustomerProperties.getAgentId());
        applicationMessageMap.put("text", new HashMap<String, Object>() {{
            put("content", message);
        }});

        applicationMessageMap.put("safe", 0);
        applicationMessageMap.put("enable_id_trans", 0);
        applicationMessageMap.put("enable_duplicate_check", 0);
        applicationMessageMap.put("duplicate_check_interval", 1800);
        applicationMessageReq.setBody(JacksonUtils.format(applicationMessageMap));
        try {
            log.info("AiCustomerChatComponent.sendMessageToUser request = {}", JacksonUtils.format(applicationMessageReq));
            Result<PassThroughSendApplicationMessageResp> result = applicationMessageService.send(applicationMessageReq);
            log.info("AiCustomerChatComponent.sendMessageToUser response = {}", JacksonUtils.format(result));

        } catch (Exception e) {
            log.error("AiCustomerChatComponent.sendMessageToUser error, request = {}", JacksonUtils.format(applicationMessageReq), e);
        }
    }

    public Result<MessageResp> sendTextToExternalUser(@NonNull String corpId,
                                       @NonNull String botUserId,
                                       @NonNull String externalUserId,
                                       @NonNull String message,
                                       @NonNull String tipCorpUserWhenError) {
        try {
            AiCustomerApiRateLimiter rateLimiter = AiCustomerApiRateLimiter.getRateLimiter(
                    corpId, AiCustomerApiRateLimiterType.SEND_MESSAGE);
            rateLimiter.tryAcquireOrThrowEx(WMonitorEnum.AI_SEND_MESSAGE_ERROR_BECAUSE_OUT_OF_LIMIT);

        } catch (ServiceException e) {
            // 超出限制，发送人工信息
            this.sendTextToUser(corpId, externalUserId, tipCorpUserWhenError);
            return null;
        }

        SendMessageReq sendMessageReq = new SendMessageReq();
        sendMessageReq.setCorpId(corpId);
        sendMessageReq.setWecomUserId(botUserId);
        sendMessageReq.setExternalUserId(externalUserId);
        sendMessageReq.setMessageType(7);
        HashMap<String, Object> payload = Maps.newHashMap();
        payload.put("text", message);
        sendMessageReq.setPayload(JacksonUtils.format(payload));

        try {
            log.info("AiCustomerChatComponent.sendMessageToExternalUser request = {}", JacksonUtils.format(sendMessageReq));

            Result<MessageResp> result = messageService.send(sendMessageReq);

            log.info("AiCustomerChatComponent.sendMessageToExternalUser response = {}", JacksonUtils.format(result));

            return result;
        } catch (Exception e) {
            log.error("AiCustomerChatComponent.sendMessageToExternalUser error, req = {}, errMsg = {}",
                    JacksonUtils.format(sendMessageReq), e.getMessage(), e);
        }

        return null;
    }


    public void sendAppletMessageToExternalUser(@NonNull String corpId,
                                                @NonNull String botUserId,
                                                @NonNull String externalUserId,
                                                @NonNull String url,
                                                @NonNull SkuCategoryBo skuCategoryConfig) {
        try {
            AiCustomerApiRateLimiter rateLimiter = AiCustomerApiRateLimiter.getRateLimiter(
                    corpId, AiCustomerApiRateLimiterType.SEND_MESSAGE);
            rateLimiter.tryAcquireOrThrowEx(WMonitorEnum.AI_SEND_MESSAGE_ERROR_BECAUSE_OUT_OF_LIMIT);

        } catch (ServiceException e) {
            return;
        }

        SendMessageReq sendMessageReq = new SendMessageReq();
        sendMessageReq.setCorpId(corpId);
        sendMessageReq.setWecomUserId(botUserId);
        sendMessageReq.setExternalUserId(externalUserId);
        sendMessageReq.setMessageType(9);
        MessagePayload.MiniProgramPayload payload = new MessagePayload.MiniProgramPayload();
        payload.setAppid("wx3f3fa95456b856c3");
        payload.setUsername("gh_ccf8d737fb68");
        payload.setDescription(skuCategoryConfig.getDescription());
        payload.setPagePath(url);
        payload.setThumbUrl(skuCategoryConfig.getThumbUrl());
        payload.setTitle(skuCategoryConfig.getTitle());
        payload.setIconUrl(skuCategoryConfig.getIconUrl());
        sendMessageReq.setPayload(JacksonUtils.format(payload));

        try {
            log.info("AiCustomerChatComponent.sendAppletMessageToExternalUser request = {}", JacksonUtils.format(sendMessageReq));

            Result<MessageResp> result = messageService.send(sendMessageReq);

            log.info("AiCustomerChatComponent.sendAppletMessageToExternalUser response = {}", JacksonUtils.format(result));

        } catch (Exception e) {
            log.error("AiCustomerChatComponent.sendAppletMessageToExternalUser error, req = {}, errMsg = {}",
                    JacksonUtils.format(sendMessageReq), e.getMessage(), e);
        }
    }


    /**
     * 不满意重做发送小程序卡片
     * @param corpId
     * @param botUserId
     * @param externalUserId
     * @param url
     */
    public void sendReWorkAppletMessage(@NonNull String corpId,
                                         @NonNull String botUserId,
                                         @NonNull String externalUserId,
                                         @NonNull String url) {
        try {
            AiCustomerApiRateLimiter rateLimiter = AiCustomerApiRateLimiter.getRateLimiter(
                    corpId, AiCustomerApiRateLimiterType.SEND_MESSAGE);
            rateLimiter.tryAcquireOrThrowEx(WMonitorEnum.AI_SEND_MESSAGE_ERROR_BECAUSE_OUT_OF_LIMIT);

        } catch (ServiceException e) {
            return;
        }

        SendMessageReq sendMessageReq = new SendMessageReq();
        sendMessageReq.setCorpId(corpId);
        sendMessageReq.setWecomUserId(botUserId);
        sendMessageReq.setExternalUserId(externalUserId);
        sendMessageReq.setMessageType(9);
        MessagePayload.MiniProgramPayload payload = new MessagePayload.MiniProgramPayload();
        payload.setAppid("wx56f4459e89f63e7a");
        payload.setUsername("gh_40a9834b6611");
        payload.setDescription("服务不满意，平台保障重新服务！");
        payload.setPagePath(url);
        payload.setThumbUrl("https://pic4.58cdn.com.cn/nowater/lbgfe/image/n_v368efb00ea20546c68eea4ce80af67b21.png");
        payload.setTitle("58到家不满意重做");
        payload.setIconUrl("https://xiaoju-resource-bucket.s3.cn-northwest-1.amazonaws.com.cn/64be2bd4e89ef9dbf4a445dc_640%3Fwx_fmt%3Dpng%26wxfrom%3D200");
        sendMessageReq.setPayload(JacksonUtils.format(payload));

        try {
            Result<MessageResp> result = messageService.send(sendMessageReq);
            log.info("sendReWorkAppletMessage request = {},response = {}", JacksonUtils.format(sendMessageReq), JacksonUtils.format(result));

        } catch (Exception e) {
            log.error("sendReWorkAppletMessage error, req = {}, errMsg = {}",
                    JacksonUtils.format(sendMessageReq), e.getMessage(), e);
        }
    }

}
