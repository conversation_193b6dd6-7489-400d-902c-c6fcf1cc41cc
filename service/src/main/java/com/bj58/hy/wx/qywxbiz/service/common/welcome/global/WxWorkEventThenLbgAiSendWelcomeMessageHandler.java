package com.bj58.hy.wx.qywxbiz.service.common.welcome.global;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.fx.bcore.entity.neworder.OrderEntity;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.message.MessageResp;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.AddExternalContactEventBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.banjia.BanJiaOrderQueryService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.cmcpc.CMCPC;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanOrderQueryService;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.LbgAiReplyComponent;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiExternalContactStateBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiInterventionConditionsComponent;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiSendMessageComp;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatIdRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiWelcomeLatestSendTimeRepository;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractAddExternalContactEventHandler;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.bj58.lbg.daojia.fxbanjia.pojo.vo.order.OrderCsCDetailVO;
import com.google.common.collect.Lists;
import lombok.NonNull;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.client.codec.IntegerCodec;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Component
public class WxWorkEventThenLbgAiSendWelcomeMessageHandler extends AbstractAddExternalContactEventHandler {

    @Autowired
    private JingXuanOrderQueryService jingXuanOrderQueryService;

    @Autowired
    private BanJiaOrderQueryService banJiaOrderQueryService;

    @Autowired
    private LbgAiSendMessageComp sendMessageComp;

    @Autowired
    private LbgAiWelcomeLatestSendTimeRepository aiWelcomeLatestSendTimeRepository;

    @Autowired
    private LbgAiReplyComponent lbgAiReplyComponent;

    @Autowired
    private LbgAiInterventionConditionsComponent lbgAiInterventionConditionsComponent;

    @Autowired
    private LbgAiChatIdRepository lbgAiChatIdRepository;

    @Override
    public void process(@NonNull AddExternalContactEventBo eventMsg) {
        // 只有非DIFY欢迎语的，才走这个逻辑
    }

    public void processMessage(@NotNull AddExternalContactEventBo eventMsg) {
        try {
            String stateMappedVal = eventMsg.getExt().getStateMappedVal();
            if (ObjectUtils.isEmpty(stateMappedVal)) {
                return;
            }

            JSONObject stateJsonObj = JSONObject.parseObject(stateMappedVal);
            if (ObjectUtils.isEmpty(stateJsonObj)) {
                return;
            }

            BizSceneEnum bizScene = BizSceneEnum.of(
                    stateJsonObj.getInteger("bizLine"),
                    stateJsonObj.getInteger("bizScene")
            );

            if (ObjectUtils.isNull(bizScene)) {
                return;
            }

            // 1.获取欢迎语
            String welcomeStr = this.analysisWelcomeStr(eventMsg, bizScene, stateJsonObj);
            if (ObjectUtils.isEmpty(welcomeStr)) {
                log.info("welcome str is empty, event msg = {}", JacksonUtils.format(eventMsg));
                return;
            }

            // 1.1 记录发送的欢迎语
            String key = String.format("WELCOME_MESSAGE:%s:%s:%s",
                    eventMsg.getCorpId(), eventMsg.getUserId(), eventMsg.getExternalUserId());
            RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);
            bucket.set(welcomeStr, 1, TimeUnit.MINUTES); // 1分钟内应该能收到欢迎语的回调

            // 2. 发送句子消息
            sendWelcomeMessageIncludeRetry(eventMsg.getCorpId(),
                    eventMsg.getUserId(),
                    eventMsg.getExternalUserId(),
                    welcomeStr,
                    "[AI客服]欢迎语发送失败,已达到频次限制");

            // 3. 记录最近一次AI欢迎语发送时间
            aiWelcomeLatestSendTimeRepository.save(eventMsg.getCorpId(),
                    bizScene.getLineId(),
                    bizScene.getSceneId(),
                    eventMsg.getUserId(),
                    eventMsg.getExternalUserId());

            if (lbgAiInterventionConditionsComponent.isAiUser(eventMsg.getCorpId(), eventMsg.getUserId())) {
                // 调用LBG 的AI，提供AI标记
                LbgAiExternalContactStateBo customerAiStateMappedVal =
                        JacksonUtils.parse(stateJsonObj.getString("source"), LbgAiExternalContactStateBo.class);
                triggerLbgAi(eventMsg, customerAiStateMappedVal, welcomeStr);
            }

        } catch (Exception e) {
            log.error("AiWelcomeMessageEventHandler process error", e);
        }
    }

    private void triggerLbgAi(@NonNull AddExternalContactEventBo eventMsg,
                              @Nullable final LbgAiExternalContactStateBo customerAiStateMappedVal,
                              @NonNull final String welcomeStr) {
        try {
            if (ObjectUtils.isNull(customerAiStateMappedVal)) {
                return;
            }

            // 现在打招呼语只有不满意重做需求需要调算法
            if (StringUtils.isEmpty(customerAiStateMappedVal.getType())
                    || !customerAiStateMappedVal.getType().equals("reWork")) {
                return;
            }

            final String chatId = lbgAiChatIdRepository.getChatId(
                    eventMsg.getCorpId(), eventMsg.getUserId(), eventMsg.getExternalUserId());

            LbgAiMessageChatRecordBo item = new LbgAiMessageChatRecordBo();
            item.setDateStamp(0L);
            item.setMessage("");
            item.setMessageId(chatId);

            lbgAiReplyComponent.getAIResult(chatId, eventMsg.getExternalUserId(), customerAiStateMappedVal,
                    Lists.newArrayList(item), null, welcomeStr);

        } catch (Exception e) {
            log.error("trigger lbg ai error, event body = {}, state mapped val = {}, welcome str = {}",
                    JacksonUtils.format(eventMsg), JacksonUtils.format(customerAiStateMappedVal), welcomeStr, e);
        }

    }

    private String analysisWelcomeStr(@NonNull final AddExternalContactEventBo eventBo,
                                      @NonNull final BizSceneEnum bizScene,
                                      @NonNull final JSONObject stateJsonObj) {

        if (ObjectUtils.isNull(bizScene) || ObjectUtils.isNull(stateJsonObj) || ObjectUtils.isNull(eventBo)) {
            return null;
        }

        // 处理精选售前售后扫码加微
        if (Arrays.asList(BizSceneEnum.精选_售前扫码加微, BizSceneEnum.精选_售后扫码加微).contains(bizScene)) {
            return getPreSalesOrAfterSalesWelcomeMessage(eventBo, stateJsonObj, bizScene);
        }

        // 处理维修阿姨端入口加企微
        if (BizSceneEnum.精选_维修阿姨端入口加企微.equals(bizScene)) {
            return getMaintenanceAuntWelcomeMessage(eventBo, stateJsonObj);
        }

        // 处理保洁阿姨端入口加企微
        if (BizSceneEnum.精选_保洁阿姨端入口加企微.equals(bizScene)) {
            return getCleaningAuntWelcomeMessage();
        }

        return null;
    }

    @Nullable
    public String getPreSalesOrAfterSalesWelcomeMessage(final AddExternalContactEventBo eventBo,
                                                        final JSONObject stateJsonObj,
                                                        final BizSceneEnum bizScene) {
        String source = stateJsonObj.getString("source");
        if (ObjectUtils.isEmpty(source)) {
            return null;
        }

        LbgAiExternalContactStateBo customerAiStateMappedVal =
                JacksonUtils.parse(source, LbgAiExternalContactStateBo.class);

        Integer cateId = customerAiStateMappedVal.getCateId();
        Long orderId = customerAiStateMappedVal.getOrderId();

        String welcomeMessage = null;

        if (ObjectUtils.notNull(orderId) && orderId > 0) {
            String orderCreateTimeStr = null;
            String serviceName = null;

            OrderEntity djjxOrder = jingXuanOrderQueryService.query(orderId);

            if (ObjectUtils.notNull(djjxOrder)) {

                Date orderCreateTime = djjxOrder.getCreateTime();
                orderCreateTimeStr = DateUtils.dateToStr(DateUtils.yyyyMMddHH4mmss, orderCreateTime);
                serviceName = djjxOrder.getServiceName();

            } else {
                OrderCsCDetailVO fxbjOrder = banJiaOrderQueryService.query(orderId);
                if (ObjectUtils.notNull(fxbjOrder)) {

                    orderCreateTimeStr = fxbjOrder.getOrderInfo().getServiceTime();
                    serviceName = fxbjOrder.getOrderInfo().getSkuName();
                }

            }

            if (ObjectUtils.isEmpty(orderCreateTimeStr) ||
                    ObjectUtils.isEmpty(serviceName)) {
                return null;
            }

            welcomeMessage = String.format("您好，我是58到家专属客服，请问您是想咨询%s的%s这条订单的什么问题呢？",
                    orderCreateTimeStr, serviceName);

        } else if (ObjectUtils.notEmpty(cateId)) {

            String categoryName = CMCPC.getCategoryNameById(cateId);
            if (ObjectUtils.isEmpty(categoryName)) {
                return null;
            }


            if (BizSceneEnum.精选_售前扫码加微.equals(bizScene)) {
                RMap<String, String> mappingConfig =
                        redisson.getMap("WELCOME_MESSAGE_CATE_MAPPING_CONFIG", StringCodec.INSTANCE);

                if (ObjectUtils.notEmpty(mappingConfig)) {
                    String cateConfigName = mappingConfig.get(categoryName);
                    if (StringUtils.isNotEmpty(cateConfigName)) {
                        categoryName = cateConfigName;
                    }
                }
            }

            welcomeMessage = String.format("您好，我是58到家专属客服，请问您想咨询%s的什么问题呢？我能给您拿到企微专属优惠价", categoryName);
        }

        return welcomeMessage;
    }

    /**
     * 获取维修阿姨端入口加企微的欢迎语
     */
    @Nullable
    public String getMaintenanceAuntWelcomeMessage(final AddExternalContactEventBo eventBo,
                                                   final JSONObject stateJsonObj) {
        String source = stateJsonObj.getString("source");
        if (ObjectUtils.isEmpty(source)) {
            return getDefaultMaintenanceWelcomeMessage();
        }

        LbgAiExternalContactStateBo customerAiStateMappedVal =
                JacksonUtils.parse(source, LbgAiExternalContactStateBo.class);

        if (ObjectUtils.isNull(customerAiStateMappedVal)) {
            return getDefaultMaintenanceWelcomeMessage();
        }

        Integer cateId = customerAiStateMappedVal.getCateId();

        // 如果没有品类ID，返回默认欢迎语
        if (ObjectUtils.isEmpty(cateId)) {
            return getDefaultWelcomeMessage();
        }

        String categoryName = CMCPC.getCategoryNameById(cateId);

        // 如果品类名称为空，返回默认欢迎语
        if (ObjectUtils.isEmpty(categoryName)) {
            return getDefaultWelcomeMessage();
        }

        return String.format("您好，我是您的58专属服务管家，请问您想咨询%s的什么问题呢？", categoryName);
    }

    /**
     * 获取保洁阿姨端入口加企微的欢迎语
     */
    @Nullable
    public String getCleaningAuntWelcomeMessage() {
        return "感谢您选择58到家！我是您的专属客服，服务完成后期待您在app或小程序内对我们的服务做出评价哦~如对服务不满意，可以马上联系我帮您申请重做！";
    }

    /**
     * 获取默认的维修欢迎语（维修增粉场景）
     */
    private String getDefaultMaintenanceWelcomeMessage() {
        return "感谢您选择58到家！我是您的专属客服，服务完成后期待您在app或小程序内对我们的服务做出评价哦~如对服务不满意，可以马上联系我帮您申请重做！";
    }

    /**
     * 获取默认欢迎语（品类为空时）
     */
    private String getDefaultWelcomeMessage() {
        return "您好，我是你的专属管家，请问有什么可以帮您？";
    }


    /**
     * 发送欢迎语包含重试逻辑
     */
    public void sendWelcomeMessageIncludeRetry(@NonNull String corpId,
                                               @NonNull String botUserId,
                                               @NonNull String externalUserId,
                                               @NonNull String message,
                                               @NonNull String tipCorpUserWhenError) {

        // 重试开关  redis没值关闭重试 redis有值开启重试
        RBucket<String> bucket = redisson.getBucket("SEND_WELCOME_MESSAGE_SWITCH", StringCodec.INSTANCE);

        if (!bucket.isExists()) {
            sendMessageComp.sendTextToExternalUser(
                    corpId, botUserId, externalUserId,
                    message, tipCorpUserWhenError);
            return;
        }

        // 最大重试次数
        int welcomeMessageRetryMaxRetries = getWelcomeMessageRetryMaxRetries();

        // 重试间隔
        int welcomeMessageRetryInterval = getWelcomeMessageRetryInterval();

        int i = 0;
        while (true) {

            Result<MessageResp> messageRespResult = sendMessageComp.sendTextToExternalUser(
                    corpId, botUserId, externalUserId,
                    message, tipCorpUserWhenError);

            // 不成功的时候进行重试
            if (messageRespResult != null && messageRespResult.notSuccess()) {
                log.info("发送欢迎语失败重试 botUserId :" + botUserId
                        + ",externalUserId : " + externalUserId
                        + ",message : " + message
                        + ",messageRespResult:" + JSONObject.toJSONString(messageRespResult));
                ++i;

                if (i > welcomeMessageRetryMaxRetries) { // 超过了最大重试次数
                    break;
                }

                // 未达最大重试次数，尝试休眠后重试
                if (welcomeMessageRetryInterval > 0) {
                    try {
                        TimeUnit.SECONDS.sleep(welcomeMessageRetryInterval);
                    } catch (InterruptedException ignored) {
                    }
                }
                continue;
            }

            break;
        }

    }


    private int getWelcomeMessageRetryMaxRetries() {
        int maxRetries = 1;
        RBucket<Integer> bucket = redisson.getBucket("SEND_WELCOME_MESSAGE_MAX_RETRIES", IntegerCodec.INSTANCE);
        if (bucket.isExists()) {
            maxRetries = bucket.get();
        }

        if (maxRetries < 0) {
            maxRetries = 1;
        }

        return maxRetries;
    }

    private int getWelcomeMessageRetryInterval() {
        int interval = 10;

        RBucket<Integer> bucket = redisson.getBucket("SEND_WELCOME_MESSAGE_RETRY_INTERVAL", IntegerCodec.INSTANCE);
        if (bucket.isExists()) {
            interval = bucket.get();
        }

        if (interval < 0) {
            interval = 10;
        }

        return interval;
    }
}
