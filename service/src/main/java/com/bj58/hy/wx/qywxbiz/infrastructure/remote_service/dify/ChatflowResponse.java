
package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *
 */
@Data
public class ChatflowResponse {

    /**
     * 会话唯一标识
     */
    private String conversationId;

    /**
     * 消息唯一 ID
     */
    @JsonProperty("message_id")
    private String messageId;

    /**
     * 完整回复内容
     */
    private String answer;


    /**
     * 消息创建时间戳，如：1705395332
     */
    @JsonProperty("created_at")
    private int createdAt;

}
