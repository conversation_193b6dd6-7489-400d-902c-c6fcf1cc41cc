package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.bo;

import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/12 15:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class OrderInfo {

    private String orderId;

    private String orderCreateTime;

    private Integer cateId;

    private String cateName;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 预约服务时间
     */
    private String serviceStartTime;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 服务完成时间
     */
    private String serviceCompleteTime;

    /**
     * 阿姨姓名
     */
    private String staffName;

}
