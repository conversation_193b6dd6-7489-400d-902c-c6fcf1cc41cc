package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.cmcpc;

import com.bj58.spat.cmc.entity.CategoryEntity;
import com.bj58.spat.cmc.entity.DispCategoryEntity;
import com.bj58.spat.cmc.entity.DispLocalEntity;
import com.bj58.spat.cmc.entity.LocalEntity;
import com.bj58.spat.cmc.service.CategoryService;
import com.bj58.spat.cmc.service.DispCategoryService;
import com.bj58.spat.cmc.service.DispLocalService;
import com.bj58.spat.cmc.service.LocalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public class CMCPC {


    /* 归属分类相关的服务初始化 */
    private static final CategoryService categoryService = CategoryService.getInstance();

    /* 表现分类相关的服务初始化 */
    private static final DispCategoryService dispCategoryService = DispCategoryService.getInstance();

    /* 归属地域相关的服务初始化 */
    private static final LocalService localService = LocalService.getInstance();

    /* 表现地域相关的服务初始化 */
    private static final DispLocalService dispLocalService = DispLocalService.getInstance();

    /**
     * 根据归属类ID获得归属类
     */
    public static CategoryEntity getCategoryById(Integer id) {
        if (Objects.isNull(id) || id <= 0) {
            return null;
        }

        try {
            return categoryService.GetCategoryByID(id);
        } catch (Exception e) {
            log.error(String.format("get category by id error, id = %s", id), e);
            return null;
        }
    }

    public static String getCategoryNameById(Integer id) {
        CategoryEntity cate = getCategoryById(id);
        return cate == null ? "" : cate.getCateName();
    }

    /**
     * 根据表现类ID获得表现分类
     */
    public static DispCategoryEntity getDisplayCategoryById(Integer displayId) {
        if (Objects.isNull(displayId) || displayId <= 0) {
            return null;
        }

        try {
            return dispCategoryService.GetDispCategoryByID(displayId);
        } catch (Exception e) {
            log.error(String.format("get display category by display category id error, display id = %s", displayId), e);
            return null;
        }
    }

    /**
     * 根据表现类ID获得归属类ID
     */
    public static Integer getCategoryIdByDisplayCategoryId(Integer displayId) {
        return Optional.ofNullable(getDisplayCategoryById(displayId))
                .map(DispCategoryEntity::getCateID)
                .orElse(null);
    }

    /**
     * 根据父归属类ID获得归属类集合
     */
    public static List<CategoryEntity> getCategoriesByPid(Integer pid) {
        if (Objects.isNull(pid) || pid <= 0) {
            return new ArrayList<>();
        }

        try {
            return categoryService.GetCategoryByParentID(pid);
        } catch (Exception e) {
            log.error(String.format("get categories by pid error, pid = %s", pid), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据归属类ID获得默认的表现分类
     */
    public static DispCategoryEntity getDefaultDisplayCategoryById(Integer id) {
        if (Objects.isNull(id) || id <= 0) {
            return null;
        }

        try {
            return dispCategoryService.GetDefaultDispCateByCateID(id);
        } catch (Exception e) {
            log.error(String.format("get default display category by category id error, id = %s", id), e);
            return null;
        }
    }

    /**
     * 根据归属地域ID获得归属地域
     */
    public static LocalEntity getLocalById(Integer id) {
        if (Objects.isNull(id) || id <= 0) {
            return null;
        }
        try {
            return localService.GetLocalByID(id);
        } catch (Exception e) {
            log.error(String.format("get local by id error, id = %s", id), e);
            return null;
        }
    }

    /**
     * 根据dirname获取归属地域
     */
    public static LocalEntity getLocalByDirname(String dirname) {
        if (Objects.isNull(dirname)) {
            return null;
        }

        try {
            return localService.GetLocalAll()
                    .stream()
                    .filter(local -> local.getDirName().equals(dirname))
                    .findFirst()
                    .orElse(null);
        } catch (Exception e) {
            log.error(String.format("get local by dirname error, dirname = %s", dirname), e);
            return null;
        }
    }

    /**
     * 根据父归属地域ID获得子归属地域
     */
    public static List<LocalEntity> getLocalsByPid(Integer pid) {
        if (Objects.isNull(pid) || pid <= 0) {
            return new ArrayList<>();
        }
        try {
            List<LocalEntity> entities = localService.GetLocalByParentID(pid);

            return CollectionUtils.isNotEmpty(entities) ?
                    entities : new ArrayList<>();

        } catch (Exception e) {
            log.error(String.format("get locals by pid error, id = %s", pid), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据表现地域ID获得归属地域
     */
    public static LocalEntity getLocalByDisplayId(Integer displayId) {
        if (Objects.isNull(displayId) || displayId <= 0) {
            return null;
        }
        DispLocalEntity dispLocalEntity = getDisplayLocalByDisplayId(displayId);
        if (null == dispLocalEntity) {
            return null;
        }

        return getLocalById(dispLocalEntity.getLocalID());
    }

    /**
     * 根据表现地域ID获得归属地域
     */
    public static Integer getLocalIdByDisplayId(Integer displayId) {
        LocalEntity local = getLocalByDisplayId(displayId);

        if (Objects.isNull(local)) {
            return -1;
        }

        return local.getLocalID();
    }

    /**
     * 根据表现地域ID获得表现地域
     */
    public static DispLocalEntity getDisplayLocalByDisplayId(Integer displayId) {
        if (Objects.isNull(displayId) || displayId <= 0) {
            return null;
        }
        try {
            return dispLocalService.GetDispLocalByID(displayId);
        } catch (Exception e) {
            log.error(String.format("get display local by display id error, id = %s", displayId), e);
            return null;
        }
    }

    /**
     * 根据归属地域ID获取表现地域
     */
    public static List<DispLocalEntity> getDisplayLocalsById(Integer id) {
        if (Objects.isNull(id) || id <= 0) {
            return null;
        }

        try {
            List<DispLocalEntity> entities = dispLocalService.GetDispLocal(id);

            return CollectionUtils.isNotEmpty(entities) ?
                    entities : new ArrayList<>();
        } catch (Exception e) {
            log.error(String.format("get display locals by id error, id = %s", id), e);
            return new ArrayList<>();
        }
    }
}