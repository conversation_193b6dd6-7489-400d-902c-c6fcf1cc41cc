package com.bj58.hy.wx.qywxbiz.service.common.monitor;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * Redis计数器测试类
 * 用于验证Redis计数器操作是否正确
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisCounterTest {

    @Autowired
    private RedissonClient redisson;

    private static final int EXPIRE_DAYS = 3;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 测试计数器增加操作
     */
    public void testIncrementCounter() {
        try {
            String testKey = "test:counter:" + LocalDateTime.now().format(DATE_FORMATTER);
            
            log.info("开始测试计数器操作，key: {}", testKey);
            
            // 获取RAtomicLong实例
            RAtomicLong atomicLong = redisson.getAtomicLong(testKey);
            
            // 测试增加操作
            long value1 = atomicLong.incrementAndGet();
            log.info("第一次增加后的值: {}", value1);
            
            long value2 = atomicLong.incrementAndGet();
            log.info("第二次增加后的值: {}", value2);
            
            // 设置过期时间
            atomicLong.expire(EXPIRE_DAYS, TimeUnit.DAYS);
            log.info("设置过期时间: {} 天", EXPIRE_DAYS);
            
            // 获取当前值
            long currentValue = atomicLong.get();
            log.info("当前值: {}", currentValue);
            
            // 清理测试数据
            atomicLong.delete();
            log.info("清理测试数据完成");
            
            log.info("计数器操作测试成功");
            
        } catch (Exception e) {
            log.error("计数器操作测试失败", e);
        }
    }

    /**
     * 测试并发计数器操作
     */
    public void testConcurrentCounter() {
        try {
            String testKey = "test:concurrent:counter:" + LocalDateTime.now().format(DATE_FORMATTER);
            
            log.info("开始测试并发计数器操作，key: {}", testKey);
            
            RAtomicLong atomicLong = redisson.getAtomicLong(testKey);
            
            // 模拟并发操作
            int threadCount = 10;
            int incrementsPerThread = 100;
            
            Thread[] threads = new Thread[threadCount];
            
            for (int i = 0; i < threadCount; i++) {
                threads[i] = new Thread(() -> {
                    for (int j = 0; j < incrementsPerThread; j++) {
                        atomicLong.incrementAndGet();
                    }
                });
            }
            
            // 启动所有线程
            for (Thread thread : threads) {
                thread.start();
            }
            
            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join();
            }
            
            long finalValue = atomicLong.get();
            long expectedValue = threadCount * incrementsPerThread;
            
            log.info("并发测试结果 - 期望值: {}, 实际值: {}", expectedValue, finalValue);
            
            if (finalValue == expectedValue) {
                log.info("并发计数器操作测试成功");
            } else {
                log.error("并发计数器操作测试失败，值不匹配");
            }
            
            // 清理测试数据
            atomicLong.delete();
            
        } catch (Exception e) {
            log.error("并发计数器操作测试失败", e);
        }
    }

    /**
     * 运行所有测试
     */
    public void runAllTests() {
        log.info("开始运行Redis计数器测试...");
        
        testIncrementCounter();
        testConcurrentCounter();
        
        log.info("Redis计数器测试完成");
    }
}
