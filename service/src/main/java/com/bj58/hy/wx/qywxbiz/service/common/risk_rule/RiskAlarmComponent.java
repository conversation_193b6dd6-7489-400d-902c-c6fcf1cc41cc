package com.bj58.hy.wx.qywxbiz.service.common.risk_rule;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.AbstractMeishiMessageHandler;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.MeishiMessageBo;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RiskAlarmComponent {

    @Autowired
    protected RedissonClient redisson;

    @Autowired
    protected ObjectProvider<AbstractMeishiMessageHandler> meishiMessageHandlers;

    public void alarm(@NonNull final String title,
                      @NonNull final Map<String, String> content) {

        MeishiMessageBo meishiBo = new MeishiMessageBo();
        meishiBo.setTitle(title);
        meishiBo.setContentMap(content);

        // 判断是否需要 发送到特定的美事群里
        RMap<String, String> rMap;
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            rMap = redisson.getMap("ALARM_MEISHI_GROUP_ID", StringCodec.INSTANCE);
        } else {
            rMap = redisson.getMap("ALARM_MEISHI_GROUP_ID_SANDBOX", StringCodec.INSTANCE);
        }

        for (Map.Entry<String, String> entry : content.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            String ruleStr = key + value;
            String specialGroupId = rMap.get(ruleStr);

            if (ObjectUtils.notEmpty(specialGroupId)) {
                log.info("发现特定的告警群组，规则 = {}, 对应的群id = {}", ruleStr, specialGroupId);
                meishiBo.getSpecialGroupIds().add(specialGroupId); // set, 不需要手动去重
            }
        }

        for (AbstractMeishiMessageHandler meishiMessageHandler : meishiMessageHandlers) {
            try {
                meishiMessageHandler.process(meishiBo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

}
