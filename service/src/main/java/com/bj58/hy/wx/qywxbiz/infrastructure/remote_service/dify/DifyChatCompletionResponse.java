
package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 当 response_mode 为 blocking 时，返回 ChatCompletionResponse object。
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DifyChatCompletionResponse {


    /**
     * 消息唯一 ID
     */
    @JsonProperty("message_id")
    private String messageId;

    /**
     * 会话 ID
     */
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * App 模式，固定为 chat
     */
    private String mode = "chat";

    /**
     * 完整回复内容
     */
    private String answer;

    /**
     * 任务 ID，用于请求跟踪和下方的停止响应接口
     */
    @JsonProperty("task_id")
    private String taskId;

    /**
     * 消息创建时间戳，如：1705395332
     */
    @JsonProperty("created_at")
    private int createdAt;

}
