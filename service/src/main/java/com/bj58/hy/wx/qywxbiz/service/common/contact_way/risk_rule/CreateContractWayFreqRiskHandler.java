package com.bj58.hy.wx.qywxbiz.service.common.contact_way.risk_rule;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayReq;
import com.bj58.hy.wx.qywxbiz.entity.BizDictEntity;
import com.bj58.hy.wx.qywxbiz.repository.BizDictRepository;
import com.bj58.hy.wx.qywxbiz.service.bo.BizLineEnum;
import com.bj58.hy.wx.qywxbiz.service.bo.RateLimiterWrapper;
import com.bj58.hy.wx.qywxbiz.service.common.risk_rule.RiskAlarmComponent;
import com.bj58.hy.wx.qywxbiz.service.common.risk_rule.RiskRuleComponent;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 创建 【请联系我】二维码的 频率风控规则
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CreateContractWayFreqRiskHandler {

    @Autowired
    protected RedissonClient redisson;

    @Autowired
    protected RiskRuleComponent riskRuleComponent;

    @Autowired
    protected RiskAlarmComponent riskAlarmComponent;

    @Autowired
    protected BizDictRepository bizDictRepository;


    public void tryAlarm(@NonNull final ContactWayReq req,
                         @NonNull final List<String> userIds) {
        if (ObjectUtils.isEmpty(userIds)) {
            return;
        }

        for (String userId : userIds) {
            checkHasBeenTriggeredLimit(req, userId);
        }
    }

    private void checkHasBeenTriggeredLimit(@NonNull final ContactWayReq req,
                                            @NonNull final String userId) {

        // 判断当前企业成员 是否超过了 添加的频率限制
        RateLimiterWrapper rateLimiter = getRateLimiter(req.getCorpId(), userId);
        if (rateLimiter.couldExecute()) {
            return; // 没有超过频率限制
        }

        long rate = rateLimiter.getRate();
        long rateInterval = rateLimiter.getRateInterval();
        RateIntervalUnit rateIntervalUnit = rateLimiter.getRateIntervalUnit();

        log.info("[{},{}]企业成员二维码生成频率频率过高, rate = {}, rate interval = {}, rate interval unit = {}",
                req.getCorpId(), userId,
                rate, rateInterval, rateIntervalUnit.name());

        RBucket<String> bucket = redisson.getBucket(
                String.format("CreateContractWayFreqRisk:%s:%s:SEND_MEISHI_FLAG", req.getCorpId(), userId), StringCodec.INSTANCE);
        if (bucket.isExists()) {
            return;
        }

        String corpId = req.getCorpId();
        Integer bizLine = req.getBizLine();
        Integer bizScene = req.getBizScene();


        BizLineEnum bizLineEnum = BizLineEnum.of(bizLine);
        String bizLineDesc = Objects.nonNull(bizLineEnum) ? bizLineEnum.name() : String.format("未知业务线[%s]", bizLine);

        BizDictEntity bizDict = bizDictRepository.getOne(bizLine, bizScene);
        String bizSceneDesc = Objects.nonNull(bizDict) ? bizDict.getBizName() : String.format("未知场景[%s]", bizScene);


        Map<String, String> content = new LinkedHashMap<>();
        content.put("通知内容：", "当前企业成员生成二维码频率过高，请尽快确认情况");
        content.put("企业主体：", corpId);
        content.put("企业成员：", userId);
        content.put("业务线：", bizLineDesc);
        content.put("业务场景：", bizSceneDesc);
        content.put("告警频率：", String.format("每 %s %s 生成 %s个",
                rateInterval, rateIntervalUnit.name().toLowerCase(), rate));

        riskAlarmComponent.alarm("企业微信风控通知", content);

        bucket.set("1", riskRuleComponent.getAlarmTimeInterval(), TimeUnit.SECONDS);
    }


    private RateLimiterWrapper getRateLimiter(final @NonNull String corpId,
                                              final @NonNull String userId) {
        RAtomicLong rateValue = redisson.getAtomicLong(
                // CreateContractWayFreqRate:ww5cfa32107e9a1f20
                String.format("CreateContractWayFreqRate:%s", corpId)
        );
        if (!rateValue.isExists()) {
            rateValue = redisson.getAtomicLong(
                    String.format("CreateContractWayFreqRate:%s", "default")
            );
        }

        RAtomicLong rateIntervalValue = redisson.getAtomicLong(
                // CreateContractWayFreqRateInterval:ww5cfa32107e9a1f20
                String.format("CreateContractWayFreqRateInterval:%s", corpId)
        );
        if (!rateIntervalValue.isExists()) {
            rateIntervalValue = redisson.getAtomicLong(
                    String.format("CreateContractWayFreqRateInterval:%s", "default")
            );
        }

        RBucket<String> rateIntervalUnitValue = redisson.getBucket(
                // CreateContractWayFreqRateIntervalUnit:ww5cfa32107e9a1f20
                String.format("CreateContractWayFreqRateIntervalUnit:%s", corpId), StringCodec.INSTANCE
        );
        if (!rateIntervalUnitValue.isExists()) {
            rateIntervalUnitValue = redisson.getBucket(
                    String.format("CreateContractWayFreqRateIntervalUnit:%s", "default"), StringCodec.INSTANCE
            );
        }

        long rate = 2;
        if (rateValue.isExists()) {
            rate = rateValue.get();
        }

        long rateInterval = 1;
        if (rateIntervalValue.isExists()) {
            rateInterval = rateIntervalValue.get();
        }

        RateIntervalUnit rateIntervalUnit = RateIntervalUnit.SECONDS;
        if (rateIntervalUnitValue.isExists()) {
            RateIntervalUnit v = RateIntervalUnit.valueOf(rateIntervalUnitValue.get());
            if (ObjectUtils.notNull(v)) {
                rateIntervalUnit = v;
            }
        }

        RRateLimiter rateLimiter = redisson.getRateLimiter(
                String.format("CreateContractWayFreqRateLimiter:%s:%s", corpId, userId)
        );

        if (!rateLimiter.isExists()) {
            log.info("[{},{}]企业成员二维码生成频率风控规则暂未初始化, rate = {}, rate interval = {}, rate interval unit = {}",
                    corpId, userId,
                    rate, rateInterval, rateIntervalUnit.name());
            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        RateLimiterConfig config = rateLimiter.getConfig();
        long oldRate = config.getRate();
        long oldRateInterval = config.getRateInterval();

        long newRateInterval = rateIntervalUnit.toMillis(rateInterval);

        if (!Objects.equals(oldRate, rate) ||
                !Objects.equals(oldRateInterval, newRateInterval)) {

            log.info("[{},{}]当前企业成员二维码生成频率风控规则的设置发生变动, limiter = {}, old rate = {}, old rate interval = {}, new rate = {}, new rate interval = {}",
                    corpId, userId,
                    rateLimiter.getName(),
                    oldRate, oldRateInterval, rate, newRateInterval);

            redisson.getKeys().delete( // 删除key，这里的删除操作极端情况下可能会导致报错，影响不大暂时忽略
                    rateLimiter.getName(),
                    String.format("{%s}:permits", rateLimiter.getName()),
                    String.format("{%s}:value", rateLimiter.getName())
            );

            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        return new RateLimiterWrapper(rateLimiter, rate, rateInterval, rateIntervalUnit);
    }
}
