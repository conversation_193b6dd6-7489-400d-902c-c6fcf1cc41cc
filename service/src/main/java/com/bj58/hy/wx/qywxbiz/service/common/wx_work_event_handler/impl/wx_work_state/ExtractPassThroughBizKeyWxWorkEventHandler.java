package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.impl.wx_work_state;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IExternalContactMappingService;
import com.bj58.hy.wx.qywx.contract.dto.external_contact_mapping.SaveExternalUserLinkedBizKeyReq;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractWxWorkEventHandler;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Component
public class ExtractPassThroughBizKeyWxWorkEventHandler extends AbstractWxWorkEventHandler {

    @SCFClient(lookup = IExternalContactMappingService.SCF_URL)
    private IExternalContactMappingService externalContactMappingService;

    @Override
    public void process(final @NonNull JSONObject event) {
        try {
            String corpId = event.getString("ToUserName");
            String externalUserID = event.getString("ExternalUserID");
            if (StringUtils.isBlank(corpId) || StringUtils.isBlank(externalUserID)) {
                return;
            }

            JSONObject ext = event.getJSONObject("Ext");
            if (ObjectUtils.isEmpty(ext)) {
                return;
            }

            JSONObject stateMappedVal = ext.getJSONObject("StateMappedVal");
            if (ObjectUtils.isEmpty(stateMappedVal)) {
                return;
            }

            JSONObject bindBizKey = stateMappedVal.getJSONObject("_BindBizKey_");
            if (ObjectUtils.isNull(bindBizKey)) {
                return;
            }

            String reason = bindBizKey.getString("reason");
            String bizKey = bindBizKey.getString("id");
            if (StringUtils.isBlank(reason) || ObjectUtils.isNull(bizKey)) {
                return;
            }

            log.info("found external user linked biz key, info = {}", event.toJSONString());

            SaveExternalUserLinkedBizKeyReq saveExternalUserLinkedBizKeyReq = new SaveExternalUserLinkedBizKeyReq();
            saveExternalUserLinkedBizKeyReq.setCorpId(corpId);
            saveExternalUserLinkedBizKeyReq.setExternalUserId(externalUserID);
            saveExternalUserLinkedBizKeyReq.setBizKey(bizKey);
            saveExternalUserLinkedBizKeyReq.setReason(reason);
            externalContactMappingService.saveExternalUserLinkedBizKey(saveExternalUserLinkedBizKeyReq);

            log.info("save external user linked biz key success, info = {}",
                    JacksonUtils.format(saveExternalUserLinkedBizKeyReq));
        } catch (Exception e) {
            log.error("ExtractPassThroughBizKeyWxWorkEventHandler process error , event:{}", JSONObject.toJSONString(event), e);
        }
    }

    @Override
    public boolean matched(final @NonNull JSONObject event) {
        return true;
    }
}
