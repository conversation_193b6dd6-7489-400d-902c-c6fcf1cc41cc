package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.huangye.alg.qasystem.client.entity.AiCustomerDaojiaInput;
import com.bj58.huangye.alg.qasystem.client.entity.AiCustomerDaojiaResult;
import com.bj58.huangye.alg.qasystem.client.entity.AiCustomerInputItem;
import com.bj58.huangye.alg.qasystem.client.entity.enums.InputTypeEnum;
import com.bj58.hy.fx.bcore.entity.neworder.OrderEntity;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.support.pojo.KeyValue;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.bj58.hy.wx.qywxbiz.entity.JuziSingleChatContentRecordAiBizInfo;
import com.bj58.hy.wx.qywxbiz.entity.enums.AiType;
import com.bj58.hy.wx.qywxbiz.entity.enums.NotReqAiReason;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.aop.RedisLockWrapper;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ChatContentRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.banjia.BanJiaOrderQueryService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.cmcpc.CMCPC;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.DifyRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowRequest;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowResponse;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanOrderQueryService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo.ReworkOrderResp;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.qa_system.QaSystemChatRemoteService;
import com.bj58.hy.wx.qywxbiz.interfaces.wconfig.WConfigComp;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiExternalContactStateBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.SkuCategoryBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiHumanNotJoinComp;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiInterventionConditionsComponent;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiSendMessageComp;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.constants.LbgAiCustomerConstants;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.constants.LbgAiSkuConstants;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatIdRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatMessageRecordRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiExternalUserRecentMessageRecordRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiResultRepository;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.bj58.lbg.daojia.cscauth.contract.IComplainComp;
import com.bj58.lbg.daojia.cscauth.pojo.dto.ComplainCreateDTO;
import com.bj58.lbg.daojia.fxbanjia.pojo.vo.order.OrderCsCDetailVO;
import com.bj58.spat.cmc.entity.CategoryEntity;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RBucket;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class LbgAiReplyComponent {

    @Autowired
    private ExternalContactRemoteService externalContactRemoteService;

    @Autowired
    private LbgAiExternalUserRecentMessageRecordRepository externalUserRecentChatMessageRecordRepository;

    @Autowired
    private LbgAiChatIdRepository aiChatIdRepository;

    @Autowired
    private LbgAiResultRepository aiResultRepository;

    @Autowired
    private LbgAiInterventionConditionsComponent aiInterventionConditionsComponent;

    @Autowired
    private ChatContentRemoteService chatContentRemoteService;

    @Autowired
    private LbgAiSendMessageComp sendMessageComp;

    @Autowired
    private LbgAiHumanNotJoinComp humanNotJoinComp;

    @Autowired
    private JingXuanOrderQueryService jingXuanOrderQueryService;

    @Autowired
    private BanJiaOrderQueryService banJiaOrderQueryService;

    @Autowired
    private QaSystemChatRemoteService qaSystemChatRemoteService;

    @Autowired
    private LbgAiChatMessageRecordRepository lbgAiChatMessageRecordRepository;

    @Autowired
    private WConfigComp wconfigComp;

    @Autowired
    protected RedissonClient redisson;

    private static final String REWORK_PUSH_URL = "/pkH5Page/pages/index?source=4738&src=https://mfangxin.58.com/m/v1/reworkApplication.html?orderId=%s&cateId=%s";
    private static final String REWORK_PUSH_TEXT_URL = "非常抱歉给您带来不好的服务体验，我向平台专门为您申请了重新服务，点击填写申请即可～有问题随时联系我";

    @Autowired
    private DifyRemoteService difyRemoteService;

    @SCFClient(lookup = IComplainComp.SCF_URL)
    private IComplainComp complainComp;

    public void tryReply(@NonNull final String corpId,
                         @NonNull final String botUserId,
                         @NonNull final String externalUserId,
                         @NonNull final String contactName) {

        // 取出最近的聊天内容，并且从记录中删除
        List<LbgAiMessageChatRecordBo> reqAiMessages = externalUserRecentChatMessageRecordRepository
                .getRecentChatMessagesThenClear(corpId, botUserId, externalUserId);
        if (ObjectUtils.isEmpty(reqAiMessages)) {
            return;
        }

        // 可能因为某些原因，此时不能由AI介入
        // 例如：人工客服回复，需要 取消 AI 托管，直到30min再启用 AI 托管。
        // 例如：用户要求转人工，30分钟内不再承接用户问题
        // 例如：AI无法承接，转人工，10分钟内不再承接用户问题
        boolean permits = aiInterventionConditionsComponent.tryGetAiCanJoinPermits(
                corpId, botUserId, externalUserId, new Date());
        if (!permits) {
            NotReqAiReason reason = aiInterventionConditionsComponent.getAiCanNotJoinReason(
                    corpId, botUserId, externalUserId);
            recordNotReqAiBizInfo(reqAiMessages, reason);
            return;
        }

        GetExternalContactRelationshipResp contactRelationshipResult = externalContactRemoteService.getRelationship(
                corpId, botUserId, externalUserId);

        if (Objects.isNull(contactRelationshipResult)) {
            // 这里应该满足，但是可能存在30s聚合期间，好友关系删除的情况
            recordNotReqAiBizInfo(reqAiMessages, NotReqAiReason.NOT_FOUND_RELATIONSHIP);
            return;
        }

        String mappingVal = contactRelationshipResult.getStateMappedValue();
        if (StringUtils.isBlank(mappingVal)) { // 这里肯定满足，前一步已经判断了
            recordNotReqAiBizInfo(reqAiMessages, NotReqAiReason.MISMATCH_STATE_VALUE);
            return;
        }

        JSONObject stateJsonObj = JSONObject.parseObject(mappingVal);
        if (ObjectUtils.isEmpty(stateJsonObj)) { // 这里肯定满足，前一步已经判断了
            recordNotReqAiBizInfo(reqAiMessages, NotReqAiReason.MISMATCH_STATE_VALUE);
            return;
        }

        String source = stateJsonObj.getString("source");
        if (ObjectUtils.isEmpty(source)) { // 这里应该满足，和业务方约定好的
            recordNotReqAiBizInfo(reqAiMessages, NotReqAiReason.MISMATCH_STATE_VALUE);
            return;
        }

        LbgAiExternalContactStateBo customerAiStateMappedVal =
                JacksonUtils.parse(source, LbgAiExternalContactStateBo.class);
        if (Objects.isNull(customerAiStateMappedVal)) { // 这里应该满足，和业务方约定好的
            recordNotReqAiBizInfo(reqAiMessages, NotReqAiReason.MISMATCH_STATE_VALUE);
            return;
        }

        final String chatId = aiChatIdRepository.getChatId(
                corpId, botUserId, externalUserId);

        // 检查是否需要异步处理
        boolean isAsync = isAsyncProcessingEnabled();

        if (isAsync) {
            // 异步处理AI请求
            getAIResultAsync(chatId, externalUserId,
                    customerAiStateMappedVal, reqAiMessages, null, null, contactName);

            // 记录异步请求信息
            recordSuccessReqAiBizInfo(reqAiMessages, null);

            return;
        }

        // 调用AI客服（同步）
        AiCustomerDaojiaResult aiResult = getAIResult(
                chatId, externalUserId, customerAiStateMappedVal, reqAiMessages, null, null);

        // 再次判断是否在人工托管期间，如果在，则舍弃当前ai返回
        permits = aiInterventionConditionsComponent.tryGetAiCanJoinPermits(
                corpId, botUserId, externalUserId, new Date());
        if (!permits) {
            NotReqAiReason reason = aiInterventionConditionsComponent
                    .getAiCanNotJoinReason(corpId, botUserId, externalUserId);
            recordNotReqAiBizInfo(reqAiMessages, reason);
            return;
        }

        // 记录LBG ai返回信息
        recordSuccessReqAiBizInfo(reqAiMessages, aiResult);

        if (ObjectUtils.isNull(aiResult) || !Objects.equals(aiResult.getCode(), 0)) {

            replyIfAiBusy(corpId, botUserId, externalUserId, contactName
            );

        } else {
            Integer aiResultType = aiResult.getType();

            if (Objects.equals(aiResultType, 0)) { //  0-普通消息

                // 是否为反馈语句
                Map<String, String> extend = aiResult.getExtend();
                // 如果没有该字段或字段为空，则意味用户的回复非反馈类回复，此时取data中的内容对用户进行回复；
                // 如果存在该字段且字段非空，则为用户反馈，且值为「满意」或「不满意」，此时data值为空，不需要对用户进行回复
                if (!ObjectUtils.isEmpty(extend) && !ObjectUtils.isEmpty(extend.get("isSatisfied"))) {
                    this.recordSuccessFeedbackInfo(reqAiMessages, "isReplyAutoFeedback", JacksonUtils.format(aiResult));
                    return;
                }

                // 是否推送sku
                this.tryPushPossibleSku(chatId, reqAiMessages, aiResult, corpId, botUserId, externalUserId);

                String aiRespTxt = aiResult.getData();
                if (ObjectUtils.isEmpty(aiRespTxt)) { // not happened
                    log.error("ai result is empty, req messages = {}, ai result = {}",
                            JacksonUtils.format(reqAiMessages), JacksonUtils.format(aiResult));
                    return;
                }

                // 记录一下AI应答的文本结果，对应的是哪几条请求
                // 用于后续收到AI应答的文本会话内容回调时，存储对应的数据
                recordAiReplyTextMappedReqMessageIds(chatId, aiRespTxt, reqAiMessages);


                String tipUserMessage = String.format(LbgAiCustomerConstants.AI_TO_USER_MESSAGE, contactName);
                sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                        aiRespTxt, tipUserMessage);

            } else if (Objects.equals(aiResultType, 1)) { //  1-转人工, 话术见data字段
                // 是否推送sku
                this.tryPushPossibleSku(chatId, reqAiMessages, aiResult, corpId, botUserId, externalUserId);

                String aiRespTxt = aiResult.getData();
                if (ObjectUtils.isEmpty(aiRespTxt)) {
                    log.error("ai result is empty, req messages = {}, ai result = {}",
                            JacksonUtils.format(reqAiMessages), JacksonUtils.format(aiResult));
                    return;
                }

                // 记录一下AI应答的文本结果，对应的是哪几条请求
                // 用于后续收到AI应答的文本会话内容回调时，存储对应的数据
                recordAiReplyTextMappedReqMessageIds(chatId, aiRespTxt, reqAiMessages);

                // AI无法承接，转人工，10分钟内不再承接用户问题
                aiInterventionConditionsComponent.setNextAiCanJoinTimestamp(
                        corpId, botUserId, externalUserId,
                        DateUtils.addMinute(new Date(), 10),
                        "AI无法承接"
                );

                // 延迟校验人工是否介入
                humanNotJoinComp.sendDelayCheckHumanJoinTask(corpId, botUserId, externalUserId, contactName);

                RSet<String> afterSalesAccount = redisson.getSet("SEND_MESSAGE_AFTER_SALES_ACCOUNT_CONFIG", StringCodec.INSTANCE);
                if (ObjectUtils.notEmpty(afterSalesAccount) && afterSalesAccount.contains(botUserId)) {
                    aiRespTxt = LbgAiCustomerConstants.AFTER_SALES_ACCOUNT_TO_EXTERNAL_USER_MESSAGE;
                }

                String tipCorpUserWhenError = String.format(LbgAiCustomerConstants.AI_TO_USER_MESSAGE, contactName);
                sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                        aiRespTxt, tipCorpUserWhenError);
                sendMessageComp.sendTextToUser(corpId, botUserId, tipCorpUserWhenError);

            } else if (Objects.equals(aiResultType, 3)) { //  3-消息丢弃

            } else if (Objects.equals(aiResultType, 4)) { //  4-执行操作

                log.info("执行操作:" + JacksonUtils.format(aiResult) +
                        ",botUserId:" + botUserId +
                        ",externalUserId:" + externalUserId +
                        ",contactName:" + contactName);

                // 发消息推 url
                Map<String, String> extend = aiResult.getExtend();

                if (!ObjectUtils.isEmpty(extend)
                        && !ObjectUtils.isEmpty(extend.get("scene"))
                        && extend.get("scene").equals("返工单")) {
                    tryPushReWorkUrl(extend, corpId, botUserId, externalUserId, contactName);
                } else if (!ObjectUtils.isEmpty(extend)
                        && !ObjectUtils.isEmpty(extend.get("sceneState"))
                        && extend.get("sceneState").equals("创建客诉单")) {
                    tryCreateComplaintOrder(extend, corpId, botUserId, externalUserId, contactName);
                }
            } else if (Objects.equals(aiResultType, 5)) { //  5-用户要求转人工
                String aiRespTxt = aiResult.getData();
                if (ObjectUtils.isEmpty(aiRespTxt)) {
                    log.error("ai result is empty, req messages = {}, ai result = {}",
                            JacksonUtils.format(reqAiMessages), JacksonUtils.format(aiResult));
                    return;
                }

                // 记录一下AI应答的文本结果，对应的是哪几条请求
                // 用于后续收到AI应答的文本会话内容回调时，存储对应的数据
                recordAiReplyTextMappedReqMessageIds(chatId, aiRespTxt, reqAiMessages);

                // 30 分钟内不再承接用户问题
                aiInterventionConditionsComponent.setNextAiCanJoinTimestamp(
                        corpId, botUserId, externalUserId,
                        DateUtils.addMinute(new Date(), 30),
                        "用户要求转人工");

                // 延迟校验人工是否介入
                humanNotJoinComp.sendDelayCheckHumanJoinTask(corpId, botUserId, externalUserId, contactName);

                RSet<String> afterSalesAccount = redisson.getSet("SEND_MESSAGE_AFTER_SALES_ACCOUNT_CONFIG", StringCodec.INSTANCE);
                if (ObjectUtils.notEmpty(afterSalesAccount) && afterSalesAccount.contains(botUserId)) {
                    aiRespTxt = LbgAiCustomerConstants.AFTER_SALES_ACCOUNT_TO_EXTERNAL_USER_MESSAGE;
                }

                String tipCorpUserWhenError = String.format(LbgAiCustomerConstants.AI_TO_USER_MESSAGE, contactName);
                sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                        aiRespTxt, tipCorpUserWhenError);
                sendMessageComp.sendTextToUser(corpId, botUserId, tipCorpUserWhenError);
            }

        }
    }

    private void tryPushReWorkUrl(@NonNull final Map<String, String> extend,
                                  @NonNull final String corpId,
                                  @NonNull final String botUserId,
                                  @NonNull final String externalUserId,
                                  @NonNull final String contactName) {

        try {
            String orderData = extend.get("orderData");
            if (StringUtils.isEmpty(orderData)) {
                return;
            }

            List<ReworkOrderResp> reworkOrderList = JSONObject.parseArray(orderData, ReworkOrderResp.class);

            for (ReworkOrderResp reworkOrder : reworkOrderList) {

                String tipCorpUserWhenError = String.format(LbgAiCustomerConstants.AI_TO_USER_MESSAGE, contactName);
                sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                        REWORK_PUSH_TEXT_URL, tipCorpUserWhenError);

                TimeUnit.SECONDS.sleep(1);

                String reWorkUrl = String.format(REWORK_PUSH_URL, reworkOrder.getOrderId(), reworkOrder.getCateId());
                sendMessageComp.sendReWorkAppletMessage(corpId, botUserId, externalUserId, reWorkUrl);
            }
        } catch (Exception e) {
            log.error("tryPushReWorkUrl error", e);
        }
    }

    private void tryPushPossibleSku(@NonNull final String chatId,
                                    @NonNull final List<LbgAiMessageChatRecordBo> messageRecords,
                                    @NonNull final AiCustomerDaojiaResult aiResult,
                                    @NonNull final String corpId, @NonNull final String botUserId, @NonNull final String externalUserId) {
        // 参数校验
        if (!Objects.equals(aiResult.getCode(), 0)) {
            return;
        }

        Map<String, String> extend = aiResult.getExtend();

        if (ObjectUtils.isEmpty(extend) ||
                !StringUtils.equalsIgnoreCase(extend.get(LbgAiSkuConstants.IS_PUSH_SKU), "true")) {
            return;
        }

        log.info("tryPushPossibleSku botUserId:" + botUserId + ",externalUserId:" + externalUserId + ",extend:" + JacksonUtils.format(extend));

        String pushSkuCategory = extend.get(LbgAiSkuConstants.PUSH_SKU_CATEGORY);
        if (StringUtils.isEmpty(pushSkuCategory)) {
            return;
        }

        String pushSkuParam = extend.get(LbgAiSkuConstants.PUSH_SKU_PARAM);
        if (StringUtils.isBlank(pushSkuParam)) {
            return;
        }

        JSONObject pushSkuParamObj = JSONObject.parseObject(pushSkuParam);
        if (ObjectUtils.isEmpty(pushSkuParamObj)) {
            return;
        }

        List<String> pushSkuCateConfig = wconfigComp.getPushSkuCateConfig();
        if (CollectionUtils.isEmpty(pushSkuCateConfig)) {
            return;
        }

        SkuCategoryBo skuCategoryConfig = wconfigComp.getSkuCategoryConfig(pushSkuCategory);
        if (ObjectUtils.isEmpty(skuCategoryConfig)) {
            return;
        }

        // 处理condition
        List<String> conditions = skuCategoryConfig.getCondition();

        String key = conditions.stream()
                .map(pushSkuParamObj::getString)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining("_"));

        log.info("tryPushPossibleSku botUserId:" + botUserId + ",key : " + key);

        if (StringUtils.isEmpty(key)) {
            return;
        }

        String skuId = "";

        int type = skuCategoryConfig.getType();
        if (type == 1) {
            Map<String, String> mapping = skuCategoryConfig.getMapping();
            if (ObjectUtils.isEmpty(mapping)) {
                return;
            }

            skuId = mapping.get(key);

        } else if (type == 2) {

            skuId = skuCategoryConfig.getDefaultSku();

        } else {
            return;
        }

        log.info("tryPushPossibleSku botUserId:" + botUserId + ",skuId : " + skuId);

        if (StringUtils.isEmpty(skuId)) {
            return;
        }

        String url = String.format(skuCategoryConfig.getUrl(), skuId);

        // 记录一下AI应答的sku推荐结果，对应的是哪几条请求
        // 用于后续收到AI应答的小程序会话内容回调时，存储对应的数据
        recordAiReplyAppletMappedReqMessageIds(chatId, url, messageRecords);

        // 发送小程序消息
        sendMessageComp.sendAppletMessageToExternalUser(corpId, botUserId, externalUserId, url, skuCategoryConfig);
    }

    public AiCustomerDaojiaResult getAIResult(@NonNull final String chatId,
                                              @NonNull final String externalUserId,
                                              @NonNull final LbgAiExternalContactStateBo customerAiStateMappedVal,
                                              @NonNull final List<LbgAiMessageChatRecordBo> messageRecords,
                                              final String feedbackMsg,
                                              final String welcomeMessage) {

        final Long orderId = customerAiStateMappedVal.getOrderId();
        KeyValue<Integer, Integer> standardCateId = getStandardCateId(customerAiStateMappedVal);

        List<AiCustomerInputItem> inputList = messageRecords.stream()
                .sorted(Comparator.comparing(LbgAiMessageChatRecordBo::getDateStamp))
                .map(messageBo -> {
                    AiCustomerInputItem item = new AiCustomerInputItem();
                    item.setMsgId(messageBo.getMessageId());
                    item.setQ(messageBo.getMessage());
                    item.setInputType(InputTypeEnum.text);
                    return item;
                })
                .collect(Collectors.toList());

        AiCustomerDaojiaInput aiCustomerDaojiaInput = new AiCustomerDaojiaInput();
        aiCustomerDaojiaInput.setInputList(inputList);
        aiCustomerDaojiaInput.setChatId(chatId);
        aiCustomerDaojiaInput.setUserId(externalUserId);
        aiCustomerDaojiaInput.setCateId(String.valueOf(standardCateId.getKey()));
        if (ObjectUtils.notEmpty(standardCateId.getKey())) {
            aiCustomerDaojiaInput.setCateName(CMCPC.getCategoryNameById(standardCateId.getKey()));
        }

        if (ObjectUtils.notEmpty(standardCateId.getValue()) && standardCateId.getValue() > 0) {
            aiCustomerDaojiaInput.setFourCateId(String.valueOf(standardCateId.getValue()));
            aiCustomerDaojiaInput.setFourCateName(CMCPC.getCategoryNameById(standardCateId.getValue()));
        }

        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(welcomeMessage)) {
            aiCustomerDaojiaInput.setType("新加微不满意订单重做");
            aiCustomerDaojiaInput.setPushText(welcomeMessage);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(feedbackMsg)) {
            aiCustomerDaojiaInput.setType("收集用户反馈");
            aiCustomerDaojiaInput.setPushText(feedbackMsg);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(orderId)) {
            aiCustomerDaojiaInput.setOrderId(String.valueOf(orderId));
        }

        // 2.调用AI客服
        AiCustomerDaojiaResult aiCustomerDaojiaResult = qaSystemChatRemoteService.aiCustomerChat(aiCustomerDaojiaInput);
        log.info("调用AI客服 req:" + JacksonUtils.format(aiCustomerDaojiaInput) + ",result:" + JacksonUtils.format(aiCustomerDaojiaResult));
        return aiCustomerDaojiaResult;
    }

    /**
     * 异步调用AI客服
     *
     * @param chatId                   聊天ID
     * @param externalUserId           外部用户ID
     * @param customerAiStateMappedVal 客户AI状态映射值
     * @param messageRecords           消息记录
     * @param feedbackMsg              反馈消息
     * @param welcomeMessage           欢迎消息
     */
    public void getAIResultAsync(@NonNull final String chatId,
                                 @NonNull final String externalUserId,
                                 @NonNull final LbgAiExternalContactStateBo customerAiStateMappedVal,
                                 @NonNull final List<LbgAiMessageChatRecordBo> messageRecords,
                                 final String feedbackMsg,
                                 final String welcomeMessage,
                                 final String customerName) {

        final Long orderId = customerAiStateMappedVal.getOrderId();
        KeyValue<Integer, Integer> standardCateId = getStandardCateId(customerAiStateMappedVal);

        List<AiCustomerInputItem> inputList = messageRecords.stream()
                .sorted(Comparator.comparing(LbgAiMessageChatRecordBo::getDateStamp))
                .map(messageBo -> {
                    AiCustomerInputItem item = new AiCustomerInputItem();
                    item.setMsgId(messageBo.getMessageId());
                    item.setQ(messageBo.getMessage());
                    item.setInputType(InputTypeEnum.text);
                    return item;
                })
                .collect(Collectors.toList());

        AiCustomerDaojiaInput aiCustomerDaojiaInput = new AiCustomerDaojiaInput();
        aiCustomerDaojiaInput.setInputList(inputList);
        aiCustomerDaojiaInput.setChatId(chatId);
        aiCustomerDaojiaInput.setUserId(externalUserId);
        aiCustomerDaojiaInput.setCateId(String.valueOf(standardCateId.getKey()));
        if (ObjectUtils.notEmpty(standardCateId.getKey())) {
            aiCustomerDaojiaInput.setCateName(CMCPC.getCategoryNameById(standardCateId.getKey()));
        }

        if (ObjectUtils.notEmpty(standardCateId.getValue()) && standardCateId.getValue() > 0) {
            aiCustomerDaojiaInput.setFourCateId(String.valueOf(standardCateId.getValue()));
            aiCustomerDaojiaInput.setFourCateName(CMCPC.getCategoryNameById(standardCateId.getValue()));
        }

        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(welcomeMessage)) {
            aiCustomerDaojiaInput.setType("新加微不满意订单重做");
            aiCustomerDaojiaInput.setPushText(welcomeMessage);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(feedbackMsg)) {
            aiCustomerDaojiaInput.setType("收集用户反馈");
            aiCustomerDaojiaInput.setPushText(feedbackMsg);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(orderId)) {
            aiCustomerDaojiaInput.setOrderId(String.valueOf(orderId));
        }

        // 设置异步标识
        Map<String, Object> extend = new HashMap<>();
        extend.put("async", "1");
        extend.put("customerName", customerName);
        aiCustomerDaojiaInput.setExtend(extend);

        // 2.调用AI客服（异步）
        AiCustomerDaojiaResult aiCustomerDaojiaResult = qaSystemChatRemoteService.aiCustomerChat(aiCustomerDaojiaInput);
        log.info("调用异步AI客服 req:" + JacksonUtils.format(aiCustomerDaojiaInput) + ",result:" + JacksonUtils.format(aiCustomerDaojiaResult));
    }

    private KeyValue<Integer, Integer> getStandardCateId(@NonNull final LbgAiExternalContactStateBo customerAiStateMappedVal) {

        Integer cate3Id = null;

        Integer cate4Id = null;

        Integer cateId = customerAiStateMappedVal.getCateId();
        Long orderId = customerAiStateMappedVal.getOrderId();

        if (ObjectUtils.notEmpty(orderId) && orderId > 0 && ObjectUtils.isEmpty(cateId)) {
            OrderEntity order = jingXuanOrderQueryService.query(orderId);
            if (ObjectUtils.notEmpty(order)) {
                cateId = order.getCateId();
                cate4Id = order.getCateId();
            } else {
                OrderCsCDetailVO banjiaOrder = banJiaOrderQueryService.query(orderId);
                if (ObjectUtils.notEmpty(banjiaOrder)) {
                    cateId = 12036;
                }
            }
        }

        if (ObjectUtils.notEmpty(cateId)) {
            CategoryEntity category = CMCPC.getCategoryById(cateId);
            if (ObjectUtils.notEmpty(category)) {
                String fullPath = category.getFullPath();
                if (StringUtils.isNotBlank(fullPath)) {
                    String[] split = fullPath.split(",");
                    if (ObjectUtils.notEmpty(split) && split.length > 2) {
                        cate3Id = Integer.parseInt(fullPath.split(",")[2]);
                        if (cateId != 12036) {
                            cate4Id = Integer.parseInt(fullPath.split(",")[3]);
                        }
                    }
                }
            }
        }

        return new KeyValue<>(cate3Id, cate4Id);
    }


    private void replyIfAiBusy(@NonNull final @NotNull String corpId,
                               @NonNull final @NotNull String botUserId,
                               @NonNull final @NotNull String externalUserId,
                               @NonNull final @NotNull String contactName) {
        // AI处理失败，转人工


        // AI处理失败的，不应该认定为AI的应答结果
        // final String chatId = aiChatIdRepository.getChatId(
        //         corpId, botUserId, externalUserId);
        // // 记录一下AI应答的文本结果，对应的是哪几条请求
        // // 用于后续收到AI应答的文本会话内容回调时，存储对应的数据
        // recordAiReplyMappedReqMessageIds(chatId, AiCustomerConstants.AI_TO_EXTERNAL_USER_MESSAGE_WHEN_ERROR, reqMessages);


        String message = LbgAiCustomerConstants.AI_TO_EXTERNAL_USER_MESSAGE_WHEN_ERROR;
        RSet<String> afterSalesAccount = redisson.getSet("SEND_MESSAGE_AFTER_SALES_ACCOUNT_CONFIG", StringCodec.INSTANCE);
        if (ObjectUtils.notEmpty(afterSalesAccount) && afterSalesAccount.contains(botUserId)) {
            message = LbgAiCustomerConstants.AFTER_SALES_ACCOUNT_TO_EXTERNAL_USER_MESSAGE;
        }

        String tipCorpUserWhenError = String.format(LbgAiCustomerConstants.AI_TO_USER_MESSAGE, contactName);
        sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                message, tipCorpUserWhenError);
        sendMessageComp.sendTextToUser(corpId, botUserId, tipCorpUserWhenError);
    }

    private void recordAiReplyTextMappedReqMessageIds(@NonNull final String chatId,
                                                      @NonNull final String aiRespTxt,
                                                      @NonNull final List<LbgAiMessageChatRecordBo> messageRecords) {
        if (ObjectUtils.isEmpty(messageRecords)) {
            // not happened
            return;
        }

        List<String> togetherMessageIds = messageRecords.stream()
                .map(LbgAiMessageChatRecordBo::getMessageId)
                .collect(Collectors.toList());

        aiResultRepository.save(chatId, aiRespTxt, togetherMessageIds);
    }

    private void recordAiReplyAppletMappedReqMessageIds(@NonNull final String chatId,
                                                        @NonNull final String appletUrl,
                                                        @NonNull final List<LbgAiMessageChatRecordBo> messageRecords) {
        if (ObjectUtils.isEmpty(messageRecords)) {
            // not happened
            return;
        }

        List<String> togetherMessageIds = messageRecords.stream()
                .map(LbgAiMessageChatRecordBo::getMessageId)
                .collect(Collectors.toList());

        aiResultRepository.save(chatId, appletUrl, togetherMessageIds);
    }


    private void recordNotReqAiBizInfo(@NonNull final List<LbgAiMessageChatRecordBo> messageRecords,
                                       @NonNull final NotReqAiReason notReqAiReason) {
        if (ObjectUtils.isEmpty(messageRecords)) {
            return;
        }

        for (LbgAiMessageChatRecordBo messageRecord : messageRecords) {
            JuziSingleChatContentRecord singleChatContentRecord =
                    chatContentRemoteService.getSingleChatContentRecord(messageRecord.getMessageId());

            if (ObjectUtils.isNull(singleChatContentRecord)) { // 肯定存在
                log.error("not found standard 1v1 chat content record, message id = {}", messageRecord.getMessageId());
                continue;
            }

            final String messageId = singleChatContentRecord.getMessageId();

            // 当前聊天是肯定是外部联系人发送的
            if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                log.error("curr chat content not send by external user, message id = {}", messageRecord.getMessageId());
                continue;
            }

            chatContentRemoteService.recordSingleChatBizInfo(
                    messageId,
                    JuziSingleChatContentRecordAiBizInfo.builder()
                            .messageId(messageId)
                            .aiType(AiType.LBG_AI)
                            .sendBy(SingleChatSendBy.EXTERNAL_USER)
                            .reqToAiBizInfo(
                                    JuziSingleChatContentRecordAiBizInfo.ReqToAiBizInfo.builder()
                                            .flag(false)
                                            .notReqReason(notReqAiReason)
                                            .build()
                            )
                            .build()
            );
        }
    }

    private void recordSuccessReqAiBizInfo(@NonNull final List<LbgAiMessageChatRecordBo> messageRecords,
                                           @Nullable final AiCustomerDaojiaResult aiResult) {

        if (ObjectUtils.isEmpty(messageRecords)) {
            return;
        }

        List<String> messageIds = messageRecords.stream()
                .map(LbgAiMessageChatRecordBo::getMessageId)
                .collect(Collectors.toList());

        for (LbgAiMessageChatRecordBo messageRecord : messageRecords) {

            JuziSingleChatContentRecord singleChatContentRecord =
                    chatContentRemoteService.getSingleChatContentRecord(messageRecord.getMessageId());

            if (ObjectUtils.isNull(singleChatContentRecord)) { // 肯定存在
                log.error("not found standard 1v1 chat content record, message id = {}", messageRecord.getMessageId());
                continue;
            }

            final String messageId = singleChatContentRecord.getMessageId();

            // 当前聊天是肯定是外部联系人发送的
            if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                log.error("curr chat content not send by external user, message id = {}", messageRecord.getMessageId());
                continue;
            }

            chatContentRemoteService.recordSingleChatBizInfo(
                    messageId,
                    JuziSingleChatContentRecordAiBizInfo.builder()
                            .messageId(messageId)
                            .aiType(AiType.LBG_AI)
                            .sendBy(SingleChatSendBy.EXTERNAL_USER)
                            .reqToAiBizInfo(
                                    JuziSingleChatContentRecordAiBizInfo.ReqToAiBizInfo.builder()
                                            .flag(true)
                                            .togetherMessageIds(messageIds)
                                            .aiResult(aiResult)
                                            .build()
                            )
                            .build()
            );
        }
    }

    public void recordSuccessFeedbackInfo(@NonNull final List<LbgAiMessageChatRecordBo> messageRecords,
                                          @NonNull final String key,
                                          @NonNull final String value) {

        if (ObjectUtils.isEmpty(messageRecords)) {
            return;
        }

        for (LbgAiMessageChatRecordBo messageRecord : messageRecords) {

            JuziSingleChatContentRecord singleChatContentRecord =
                    chatContentRemoteService.getSingleChatContentRecord(messageRecord.getMessageId());

            if (ObjectUtils.isNull(singleChatContentRecord)) { // 肯定存在
                log.error("not found standard 1v1 chat content record, message id = {}", messageRecord.getMessageId());
                continue;
            }

            final String messageId = singleChatContentRecord.getMessageId();

            chatContentRemoteService.recordSingleChatBizInfo(messageId, key, value);
        }

    }

    /**
     * 检查并发送反馈回收消息
     */
    @RedisLockWrapper(
            value = "'CheckAndSendFeedbackRecovery' + #corpId + '_' + #userId + '_' + #externalUserId + ':LOCK'",
            spel = true)
    public void checkAndSendFeedbackRecovery(@NonNull final String corpId,
                                             @NonNull final String userId,
                                             @NonNull final String externalUserId,
                                             @NonNull final Long timeStamp) {

        // 1分钟内只执行一次
        String lockKey = String.format("FEEDBACK_RECOVERY_EXECUTE_LOCK:%s:%s:%s", corpId, userId, externalUserId);
        RBucket<String> lockBucket = redisson.getBucket(lockKey, StringCodec.INSTANCE);
        if (lockBucket.isExists()) {
            return;
        }
        lockBucket.set("1", 1, TimeUnit.MINUTES);

        // 构造 Redis key
        String redisKey = String.format("FEEDBACK_RECOVERY:%s:%s:%s", corpId, userId, externalUserId);

        // 检查是否在24小时内已经执行过
        RBucket<String> bucket = redisson.getBucket(redisKey, StringCodec.INSTANCE);
        if (bucket.get() != null) {
            // 已经在24小时内执行过，直接返回
            log.info("checkAndSendFeedbackRecovery already executed within 24 hours: corpId={}, userId={}, externalUserId={}",
                    corpId, userId, externalUserId);
            return;
        }

        String chatId = aiChatIdRepository.getChatId(corpId, userId, externalUserId);
        if (ObjectUtils.isEmpty(chatId)) {
            return;
        }

        List<LbgAiMessageChatRecordBo> chatMessages =
                lbgAiChatMessageRecordRepository.getChatMessages(chatId);
        if (ObjectUtils.isEmpty(chatMessages)) {
            return;
        }

        // 获取所有客服发送的消息
        List<LbgAiMessageChatRecordBo> userChatList = chatMessages.stream()
                .sorted(Comparator.comparing(LbgAiMessageChatRecordBo::getDateStamp))
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(userChatList)) {
            return;
        }

        // 获取最后一条消息
        LbgAiMessageChatRecordBo lastMessage = userChatList.get(userChatList.size() - 1);

        // 判断最后一条的聊天内容文本，是否是邀请用户反馈？
        if (Objects.equals(
                lastMessage.getMessage(),
                LbgAiCustomerConstants.FEEDBACK_RECOVERY_MESSAGE)
        ) {
            return;
        }

        // 当前消息应该比客服发送的最后一条消息要晚
        Long dateStamp = lastMessage.getDateStamp();
        if (ObjectUtils.isEmpty(dateStamp)) {
            return;
        }
        boolean flag = timeStamp < dateStamp;
        if (flag) {
            return;
        }

        GetExternalContactRelationshipResp contactRelationshipResult = externalContactRemoteService.getRelationship(
                corpId, userId, externalUserId);
        if (Objects.isNull(contactRelationshipResult)) {
            return;
        }

        String mappingVal = contactRelationshipResult.getStateMappedValue();
        if (StringUtils.isBlank(mappingVal)) {
            return;
        }

        JSONObject stateJsonObj = JSONObject.parseObject(mappingVal);
        if (ObjectUtils.isEmpty(stateJsonObj)) {
            return;
        }

        String source = stateJsonObj.getString("source");
        if (ObjectUtils.isEmpty(source)) {
            return;
        }

        LbgAiExternalContactStateBo customerAiStateMappedVal =
                JacksonUtils.parse(source, LbgAiExternalContactStateBo.class);
        if (Objects.isNull(customerAiStateMappedVal)) {
            return;
        }

        WorkflowRequest request = new WorkflowRequest();
        String user = String.format("%s:%s:%s", corpId, userId, externalUserId);
        request.setUser(user);
        request.getInputs().put("chatList", JacksonUtils.format(chatMessages));


        // 判断当前聊天是否结束 @anhua
        log.info("判断当前聊天是否结束, dify workflow, req = {}", JacksonUtils.format(request));
        Result<WorkflowResponse> workflow = difyRemoteService.workflow("app-zCMFRW5aXbuuERdtEqEVfkyw", request);
        log.info("判断当前聊天是否结束, dify workflow done, req = {}, resp = {}", JacksonUtils.format(request), JacksonUtils.format(workflow));

        if (workflow.isFailed() || ObjectUtils.isEmpty(workflow.getData()) || ObjectUtils.isEmpty(workflow.getData().getData())) {
            return;
        }

        JSONObject data = workflow.getData().getData();
        if (ObjectUtils.isEmpty(data.getJSONObject("outputs")) ||
                ObjectUtils.isEmpty(data.getJSONObject("outputs").getString("class_name"))) {
            return;
        }
        if (!Objects.equals(data.getJSONObject("outputs").getString("class_name"), "是")) {
            return;
        }

        LbgAiMessageChatRecordBo item = new LbgAiMessageChatRecordBo();
        item.setDateStamp(0L);
        item.setMessage("");
        item.setMessageId(chatId);

        // 发送反馈回收消息
        sendMessageComp.sendTextToExternalUser(corpId, userId, externalUserId,
                LbgAiCustomerConstants.FEEDBACK_RECOVERY_MESSAGE, "");

        // 调用AI
        getAIResult(chatId, externalUserId, customerAiStateMappedVal,
                Lists.newArrayList(item), LbgAiCustomerConstants.FEEDBACK_RECOVERY_MESSAGE, null);

        // 设置 Redis 标记，表示该用户对在24小时内已执行过反馈回收
        bucket.set("executed", 24, TimeUnit.HOURS);
    }

    /**
     * 检查是否启用异步处理
     * 通过Redis配置控制是否使用异步AI处理
     *
     * @return true表示启用异步处理，false表示使用同步处理
     */
    private boolean isAsyncProcessingEnabled() {
        try {
            RBucket<String> bucket = redisson.getBucket("LBG_AI_ASYNC_PROCESSING_ENABLED", StringCodec.INSTANCE);
            String value = bucket.get();

            // 如果Redis中没有配置，默认启用同步处理
            if (ObjectUtils.isEmpty(value)) {
                return false;
            }

            return "1".equals(value);
        } catch (Exception e) {
            log.error("Failed to get async processing config from Redis, using default: true", e);
            // 异常情况下默认启用同步处理
            return false;
        }
    }

    /**
     * 处理异步AI请求 - 与tryReply中的同步逻辑保持一致
     * 此方法由异步消息队列调用，处理来自AI服务的异步响应
     *
     * @param corpId         企业ID
     * @param botUserId      机器人用户ID
     * @param externalUserId 外部用户ID
     * @param contactName    联系人姓名
     * @param aiResult       AI处理结果
     * @param reqAiMessages  原始请求消息列表
     */
    public void handleAsyncAiRequest(@NonNull final String corpId,
                                     @NonNull final String botUserId,
                                     @NonNull final String externalUserId,
                                     @NonNull final String contactName,
                                     @NonNull final AiCustomerDaojiaResult aiResult,
                                     @NonNull final List<LbgAiMessageChatRecordBo> reqAiMessages) {

        log.info("开始处理异步AI请求: corpId={}, botUserId={}, externalUserId={}, contactName={}",
                corpId, botUserId, externalUserId, contactName);

        try {
            // 再次判断是否在人工托管期间，如果在，则舍弃当前ai返回
            boolean permits = aiInterventionConditionsComponent.tryGetAiCanJoinPermits(
                    corpId, botUserId, externalUserId, new Date());
            if (!permits) {
                NotReqAiReason reason = aiInterventionConditionsComponent
                        .getAiCanNotJoinReason(corpId, botUserId, externalUserId);
                recordNotReqAiBizInfo(reqAiMessages, reason);
                return;
            }

            // 记录异步AI返回信息
            recordSuccessReqAiBizInfo(reqAiMessages, aiResult);

            final String chatId = aiChatIdRepository.getChatId(corpId, botUserId, externalUserId);

            if (ObjectUtils.isNull(aiResult) || !Objects.equals(aiResult.getCode(), 0)) {
                // AI处理失败，转人工
                replyIfAiBusy(corpId, botUserId, externalUserId, contactName);
            } else {
                Integer aiResultType = aiResult.getType();

                if (Objects.equals(aiResultType, 0)) { //  0-普通消息
                    handleAsyncNormalMessage(corpId, botUserId, externalUserId, contactName, chatId, aiResult, reqAiMessages);

                } else if (Objects.equals(aiResultType, 1)) { //  1-转人工, 话术见data字段
                    handleAsyncTransferToHuman(corpId, botUserId, externalUserId, contactName, chatId, aiResult, reqAiMessages);

                } else if (Objects.equals(aiResultType, 3)) { //  3-消息丢弃
                    log.info("AI指示丢弃消息: corpId={}, botUserId={}, externalUserId={}",
                            corpId, botUserId, externalUserId);

                } else if (Objects.equals(aiResultType, 4)) { //  4-执行操作
                    handleAsyncExecuteOperation(corpId, botUserId, externalUserId, contactName, aiResult);

                } else if (Objects.equals(aiResultType, 5)) { //  5-用户要求转人工
                    handleAsyncUserRequestTransfer(corpId, botUserId, externalUserId, contactName, chatId, aiResult, reqAiMessages);
                }
            }

            log.info("异步AI请求处理完成: corpId={}, botUserId={}, externalUserId={}",
                    corpId, botUserId, externalUserId);

        } catch (Exception e) {
            log.error("处理异步AI请求时发生异常: corpId={}, botUserId={}, externalUserId={}, error={}",
                    corpId, botUserId, externalUserId, e.getMessage(), e);
        }
    }

    /**
     * 处理异步普通消息
     */
    private void handleAsyncNormalMessage(@NonNull final String corpId,
                                          @NonNull final String botUserId,
                                          @NonNull final String externalUserId,
                                          @NonNull final String contactName,
                                          @NonNull final String chatId,
                                          @NonNull final AiCustomerDaojiaResult aiResult,
                                          @NonNull final List<LbgAiMessageChatRecordBo> reqAiMessages) {

        // 是否为反馈语句
        Map<String, String> extend = aiResult.getExtend();
        // 如果没有该字段或字段为空，则意味用户的回复非反馈类回复，此时取data中的内容对用户进行回复；
        // 如果存在该字段且字段非空，则为用户反馈，且值为「满意」或「不满意」，此时data值为空，不需要对用户进行回复
        if (!ObjectUtils.isEmpty(extend) && !ObjectUtils.isEmpty(extend.get("isSatisfied"))) {
            this.recordSuccessFeedbackInfo(reqAiMessages, "isReplyAutoFeedback", JacksonUtils.format(aiResult));
            return;
        }

        // 是否推送sku
        this.tryPushPossibleSku(chatId, reqAiMessages, aiResult, corpId, botUserId, externalUserId);

        String aiRespTxt = aiResult.getData();
        if (ObjectUtils.isEmpty(aiRespTxt)) {
            log.error("异步AI回复文本为空, req messages = {}, ai result = {}",
                    JacksonUtils.format(reqAiMessages), JacksonUtils.format(aiResult));
            return;
        }

        // 记录一下AI应答的文本结果，对应的是哪几条请求
        // 用于后续收到AI应答的文本会话内容回调时，存储对应的数据
        recordAiReplyTextMappedReqMessageIds(chatId, aiRespTxt, reqAiMessages);

        String tipUserMessage = String.format(LbgAiCustomerConstants.AI_TO_USER_MESSAGE, contactName);
        sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                aiRespTxt, tipUserMessage);
    }

    /**
     * 处理异步转人工
     */
    private void handleAsyncTransferToHuman(@NonNull final String corpId,
                                            @NonNull final String botUserId,
                                            @NonNull final String externalUserId,
                                            @NonNull final String contactName,
                                            @NonNull final String chatId,
                                            @NonNull final AiCustomerDaojiaResult aiResult,
                                            @NonNull final List<LbgAiMessageChatRecordBo> reqAiMessages) {

        // 是否推送sku
        this.tryPushPossibleSku(chatId, reqAiMessages, aiResult, corpId, botUserId, externalUserId);

        String aiRespTxt = aiResult.getData();
        if (ObjectUtils.isEmpty(aiRespTxt)) {
            log.error("异步转人工AI回复文本为空, req messages = {}, ai result = {}",
                    JacksonUtils.format(reqAiMessages), JacksonUtils.format(aiResult));
            return;
        }

        // 记录一下AI应答的文本结果，对应的是哪几条请求
        // 用于后续收到AI应答的文本会话内容回调时，存储对应的数据
        recordAiReplyTextMappedReqMessageIds(chatId, aiRespTxt, reqAiMessages);

        // AI无法承接，转人工，10分钟内不再承接用户问题
        aiInterventionConditionsComponent.setNextAiCanJoinTimestamp(
                corpId, botUserId, externalUserId,
                DateUtils.addMinute(new Date(), 10),
                "AI无法承接"
        );

        // 延迟校验人工是否介入
        humanNotJoinComp.sendDelayCheckHumanJoinTask(corpId, botUserId, externalUserId, contactName);

        RSet<String> afterSalesAccount = redisson.getSet("SEND_MESSAGE_AFTER_SALES_ACCOUNT_CONFIG", StringCodec.INSTANCE);
        if (ObjectUtils.notEmpty(afterSalesAccount) && afterSalesAccount.contains(botUserId)) {
            aiRespTxt = LbgAiCustomerConstants.AFTER_SALES_ACCOUNT_TO_EXTERNAL_USER_MESSAGE;
        }

        String tipCorpUserWhenError = String.format(LbgAiCustomerConstants.AI_TO_USER_MESSAGE, contactName);
        sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                aiRespTxt, tipCorpUserWhenError);
        sendMessageComp.sendTextToUser(corpId, botUserId, tipCorpUserWhenError);
    }

    /**
     * 处理异步用户要求转人工
     */
    private void handleAsyncUserRequestTransfer(@NonNull final String corpId,
                                                @NonNull final String botUserId,
                                                @NonNull final String externalUserId,
                                                @NonNull final String contactName,
                                                @NonNull final String chatId,
                                                @NonNull final AiCustomerDaojiaResult aiResult,
                                                @NonNull final List<LbgAiMessageChatRecordBo> reqAiMessages) {

        String aiRespTxt = aiResult.getData();
        if (ObjectUtils.isEmpty(aiRespTxt)) {
            log.error("异步用户要求转人工AI回复文本为空, req messages = {}, ai result = {}",
                    JacksonUtils.format(reqAiMessages), JacksonUtils.format(aiResult));
            return;
        }

        // 记录一下AI应答的文本结果，对应的是哪几条请求
        // 用于后续收到AI应答的文本会话内容回调时，存储对应的数据
        recordAiReplyTextMappedReqMessageIds(chatId, aiRespTxt, reqAiMessages);

        // 30 分钟内不再承接用户问题
        aiInterventionConditionsComponent.setNextAiCanJoinTimestamp(
                corpId, botUserId, externalUserId,
                DateUtils.addMinute(new Date(), 30),
                "用户要求转人工");

        // 延迟校验人工是否介入
        humanNotJoinComp.sendDelayCheckHumanJoinTask(corpId, botUserId, externalUserId, contactName);

        RSet<String> afterSalesAccount = redisson.getSet("SEND_MESSAGE_AFTER_SALES_ACCOUNT_CONFIG", StringCodec.INSTANCE);
        if (ObjectUtils.notEmpty(afterSalesAccount) && afterSalesAccount.contains(botUserId)) {
            aiRespTxt = LbgAiCustomerConstants.AFTER_SALES_ACCOUNT_TO_EXTERNAL_USER_MESSAGE;
        }

        String tipCorpUserWhenError = String.format(LbgAiCustomerConstants.AI_TO_USER_MESSAGE, contactName);
        sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                aiRespTxt, tipCorpUserWhenError);
        sendMessageComp.sendTextToUser(corpId, botUserId, tipCorpUserWhenError);
    }

    /**
     * 处理异步执行操作
     */
    public void handleAsyncExecuteOperation(@NonNull final String corpId,
                                             @NonNull final String botUserId,
                                             @NonNull final String externalUserId,
                                             @NonNull final String contactName,
                                             @NonNull final AiCustomerDaojiaResult aiResult) {

        log.info("异步执行操作:" + JacksonUtils.format(aiResult) +
                ",botUserId:" + botUserId +
                ",externalUserId:" + externalUserId +
                ",contactName:" + contactName);

        Map<String, String> extend = aiResult.getExtend();

        if (ObjectUtils.isEmpty(extend)) {
            return;
        }

        String scene = extend.get("scene");
        String sceneState = extend.get("sceneState");

        if ("返工单".equals(scene)) {
            tryPushReWorkUrl(extend, corpId, botUserId, externalUserId, contactName);
        } else if ("创建客诉单".equals(sceneState)) {
            tryCreateComplaintOrder(extend, corpId, botUserId, externalUserId, contactName);
        }

    }

    /**
     * 尝试创建客诉单
     */
    private void tryCreateComplaintOrder(@NonNull final Map<String, String> extend,
                                         @NonNull final String corpId,
                                         @NonNull final String botUserId,
                                         @NonNull final String externalUserId,
                                         @NonNull final String contactName) {

        try {
            log.info("开始创建客诉单, extend: {}, corpId: {}, botUserId: {}, externalUserId: {}, contactName: {}",
                    JacksonUtils.format(extend), corpId, botUserId, externalUserId, contactName);

            String complainParamsStr = extend.get("complainParams");
            if (ObjectUtils.isEmpty(complainParamsStr)) {
                log.info("创建客诉单失败：complainParams参数为空");
                return;
            }

            JSONObject complainParams;
            try {
                complainParams = JSON.parseObject(complainParamsStr);
            } catch (Exception e) {
                log.error("创建客诉单失败：解析complainParams JSON失败, complainParams: {}", complainParamsStr, e);
                return;
            }

            // 提取参数
            Long orderId = complainParams.getLong("orderId");
            Boolean isChangePhone = complainParams.getBoolean("isChangePhone");
            String complainPhone = complainParams.getString("complainPhone");
            String beComplainIdentity = complainParams.getString("beComplainIdentity");
            Integer complainType = complainParams.getInteger("complainType");
            String complainReason = complainParams.getString("complainReason");
            String complainDescription = complainParams.getString("complainDescription");

            if (ObjectUtils.isEmpty(orderId)) {
                log.error("创建客诉单失败：orderId参数为空");
                return;
            }

            // 创建客诉单DTO
            ComplainCreateDTO createDTO = new ComplainCreateDTO();

            // 设置被投诉人身份：1客户，2经纪人，3商家，4劳动者
            if ("劳动者".equals(beComplainIdentity)) {
                createDTO.setBeComplainIdentity(4);
            } else if ("商家".equals(beComplainIdentity)) {
                createDTO.setBeComplainIdentity(3);
            } else {
                createDTO.setBeComplainIdentity(3); // 默认为商家
            }

            createDTO.setComplainIdentity(2);    // 投诉人身份：1阿姨，2客户
            createDTO.setComplainSource(102);      // 客诉来源：1客服、2微博、3其他  - 102是企微
            createDTO.setSubmitFrom(8);          // 表单来源：1客服、2客户提交、3发起换人
            createDTO.setBusinessLine(2);        // 业务线：1主站担保支付，2到家精选，3生活服务卡，4放心搬家，5租车，7家庭服务

            // 设置订单ID
            createDTO.setOrderId(orderId);

            // 设置投诉描述
            if (ObjectUtils.notEmpty(complainDescription)) {
                createDTO.setComplainDescription(complainDescription);
            }

            // 设置投诉原因
            if (ObjectUtils.notEmpty(complainReason)) {
                createDTO.setComplainReason(complainReason);
            }

            // 设置投诉者姓名
            createDTO.setComplainName(contactName);

            // 查询订单信息获取联系方式、城市ID和类目ID
            String orderPhone = "";
            Integer cityId = 0;
            String orderCateId = null;
            try {
                // 尝试查询精选订单
                OrderEntity orderEntity = jingXuanOrderQueryService.query(orderId);
                if (ObjectUtils.notEmpty(orderEntity)) {
                    orderPhone = orderEntity.getCphone();
                    cityId = orderEntity.getCityId();
                    orderCateId = orderEntity.getCateFullPath();
                } else {
                    // 尝试查询搬家订单
                    OrderCsCDetailVO banjiaOrder = banJiaOrderQueryService.query(orderId);
                    if (ObjectUtils.notEmpty(banjiaOrder)
                            && ObjectUtils.notEmpty(banjiaOrder.getUserInfo())
                            && ObjectUtils.notEmpty(banjiaOrder.getOrderInfo())) {
                        orderPhone = banjiaOrder.getUserInfo().getCPhone();
                        cityId = banjiaOrder.getOrderInfo().getCityId();
                        orderCateId = "12036";
                    }
                }
            } catch (Exception e) {
                log.warn("查询订单信息失败，orderId: {}", orderId, e);
            }

            // 设置投诉者联系方式
            if (Boolean.TRUE.equals(isChangePhone) && ObjectUtils.notEmpty(complainPhone)) {
                createDTO.setComplainPhone(complainPhone);
            } else {
                // 使用订单自带的联系方式
                createDTO.setComplainPhone(ObjectUtils.notEmpty(orderPhone) ? orderPhone : "");
            }

            // 设置城市ID
            createDTO.setCityId(cityId != null ? cityId : 0);

            // 设置ComplainType（从extend中获取）
            if (ObjectUtils.notEmpty(complainType)) {
                createDTO.setComplainType(complainType);
            }

            // 设置cateId（从订单获取三级类目）
            if (ObjectUtils.notEmpty(orderCateId)) {
                Integer cate3Id = getCate3IdFromOrderCateId(orderCateId);
                if (ObjectUtils.notEmpty(cate3Id)) {
                    createDTO.setCateId(cate3Id);
                }
            }

            // 调用客诉服务创建客诉单
            log.info("创建客诉单, createDTO: {}", JacksonUtils.format(createDTO));
            com.bj58.lbg.commons.vo.Result<Long> result = complainComp.create("企微AI客服自动创建", createDTO);

            // 根据创建结果发送消息给客户
            String messageToCustomer;
            if (result.isSuccess()) {
                log.info("创建客诉单成功，客诉单ID: {}, orderId: {}", result.getData(), orderId);
                messageToCustomer = "您的售后问题已反馈成功，2小时内专属客服将电话联系您，请注意接听平台客服电话～如果您有紧急如您有紧急问题，可拨打客服热线400-8583645。有其他问题随时联系我哈";
            } else {
                log.error("创建客诉单失败，错误信息: {}, orderId: {}", result.getMsg(), orderId);
                messageToCustomer = "您的售后问题已反馈成功，看到您历史存在售后问题反馈，已为您补充最新反馈，请注意接听平台客服电话～如果您有紧急如您有紧急问题，可拨打客服热线400-8583645。有其他问题随时联系我哈";
            }

            // 发送消息给客户
            try {
                sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId, messageToCustomer, "");
                log.info("客诉单创建后消息发送成功，orderId: {}, message: {}", orderId, messageToCustomer);
            } catch (Exception e) {
                log.error("客诉单创建后消息发送失败，orderId: {}, message: {}", orderId, messageToCustomer, e);
            }

        } catch (Exception e) {
            log.error("创建客诉单异常, extend: {}, corpId: {}, botUserId: {}, externalUserId: {}",
                    JacksonUtils.format(extend), corpId, botUserId, externalUserId, e);
        }

    }

    /**
     * 从订单类目ID获取三级类目ID
     *
     */
    private Integer getCate3IdFromOrderCateId(String fullCatePath) {
        try {
            if (ObjectUtils.isEmpty(fullCatePath)) {
                return null;
            }

            if (Objects.equals(fullCatePath,"12036")) {
                return 12036;
            }

            String[] split = fullCatePath.split(",");
            if (ObjectUtils.notEmpty(split) && split.length > 2) {
                // fullPath格式：一级类目,二级类目,三级类目,四级类目
                // 返回三级类目ID（索引为2）
                return Integer.parseInt(split[2]);
            }
        } catch (Exception e) {
            log.warn("获取三级类目ID失败，orderCateId: {}", fullCatePath, e);
        }

        return null;
    }

}
