package com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.impl;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.service.bo.RateLimiterWrapper;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziChatEventHandler;
import com.bj58.hy.wx.qywxbiz.service.common.risk_rule.RiskAlarmComponent;
import com.bj58.hy.wx.qywxbiz.service.common.risk_rule.RiskRuleComponent;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 消息发送频率风险控制处理器
 * 1、一个企业主体，主动发送私聊、群聊消息，800次/30s（需要强制限制）
 * 2、一个企业成员，主动给某外部联系人发送消息，控制在间隔10s 一次
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MessageFrequencyRiskHandler extends AbstractJuziChatEventHandler {

    @Autowired
    protected RedissonClient redisson;
    @Autowired
    protected RedisLockSupport lockSupport;

    @Autowired
    private RiskAlarmComponent riskAlarmComponent;

    @Autowired
    private RiskRuleComponent riskRuleComponent;

    @Override
    public void process(@NonNull final ChatMessagesEventBo.Body event) {
        // 获取标准化的聊天记录
        JuziSingleChatContentRecord singleChatContentRecord = getCurrentSingleChatContentRecord();
        if (ObjectUtils.isNull(singleChatContentRecord)) {
            return;
        }

        // 只处理企业成员发送的消息
        if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            return;
        }

        final String corpId = singleChatContentRecord.getCorpId();
        final String userId = singleChatContentRecord.getUserId();
        final String externalUserId = singleChatContentRecord.getExternalUserId();

        // 1. 检查企业主体发送消息频率限制（500次/30s）
        checkCorpMessageFrequencyLimit(corpId);
    }

    @Override
    public boolean matched(@NonNull final ChatMessagesEventBo.Body event) {
        // 群聊，不走AI
        return ObjectUtils.isEmpty(event.getRoomWecomChatId());
    }

    /**
     * 检查企业主体发送消息频率限制（800次/30s）
     */
    private void checkCorpMessageFrequencyLimit(@NonNull final String corpId) {
        // 获取或创建消息频率限制器
        RateLimiterWrapper rateLimiter = getCorpMessageFrequencyRateLimiter(corpId);

        // 尝试获取令牌
        if (rateLimiter.couldExecute()) {
            return; // 没有超过频率限制
        }

        long rate = rateLimiter.getRate();
        long rateInterval = rateLimiter.getRateInterval();
        RateIntervalUnit rateIntervalUnit = rateLimiter.getRateIntervalUnit();

        log.info("[{}]当前企业发送消息频率过高, rate = {}, rate interval = {}, rate interval unit = {}", corpId,
                rate, rateInterval, rateIntervalUnit.name());

        // 检查是否已经发送过告警
        RBucket<String> bucket = redisson.getBucket(
                String.format("CorpMessageFrequencyRisk:%s:SEND_MEISHI_FLAG", corpId), StringCodec.INSTANCE);
        if (bucket.isExists()) {
            return; // 已经发送过告警，不再重复发送
        }

        lockSupport.executeWithoutResult(
                bucket.getName() + "_LOCK_",
                () -> {
                    // 再次检查是否已经发送过告警
                    if (bucket.isExists()) {
                        return; // 已经发送过告警，不再重复发送
                    }

                    // 发送告警消息
                    Map<String, String> content = new LinkedHashMap<>();
                    content.put("通知内容：", "当前企业发送消息频率过高，请尽快确认情况");
                    content.put("企业主体：", corpId);
                    content.put("告警频率：", String.format("每 %s 秒最多发送 %s 条消息",
                            rateLimiter.getRateInterval(), rateLimiter.getRate()));

                    riskAlarmComponent.alarm("企业微信风控通知", content);

                    // 设置告警标记，避免频繁告警
                    bucket.set("1", riskRuleComponent.getAlarmTimeInterval(), TimeUnit.SECONDS);
                }
        );
    }

    /**
     * 获取企业发送消息频率限制器
     */
    protected RateLimiterWrapper getCorpMessageFrequencyRateLimiter(@NonNull final String corpId) {
        RAtomicLong rateValue = redisson.getAtomicLong(
                // CorpMessageFreqRate:ww5cfa32107e9a1f20
                String.format("CorpMessageFreqRate:%s", corpId)
        );
        if (!rateValue.isExists()) {
            rateValue = redisson.getAtomicLong(
                    String.format("CorpMessageFreqRate:%s", "default")
            );
        }

        RAtomicLong rateIntervalValue = redisson.getAtomicLong(
                // CorpMessageFreqRateInterval:ww5cfa32107e9a1f20
                String.format("CorpMessageFreqRateInterval:%s", corpId)
        );
        if (!rateIntervalValue.isExists()) {
            rateIntervalValue = redisson.getAtomicLong(
                    String.format("CorpMessageFreqRateInterval:%s", "default")
            );
        }

        RBucket<String> rateIntervalUnitValue = redisson.getBucket(
                // CorpMessageFreqRateIntervalUnit:ww5cfa32107e9a1f20
                String.format("CorpMessageFreqRateIntervalUnit:%s", corpId), StringCodec.INSTANCE
        );
        if (!rateIntervalUnitValue.isExists()) {
            rateIntervalUnitValue = redisson.getBucket(
                    String.format("CorpMessageFreqRateIntervalUnit:%s", "default"), StringCodec.INSTANCE
            );
        }

        long rate = 800;
        if (rateValue.isExists()) {
            rate = rateValue.get();
        }

        long rateInterval = 30;
        if (rateIntervalValue.isExists()) {
            rateInterval = rateIntervalValue.get();
        }

        RateIntervalUnit rateIntervalUnit = RateIntervalUnit.SECONDS;
        if (rateIntervalUnitValue.isExists()) {
            RateIntervalUnit v = RateIntervalUnit.valueOf(rateIntervalUnitValue.get());
            if (ObjectUtils.notNull(v)) {
                rateIntervalUnit = v;
            }
        }

        // 判断当前企业 是否超过了 频率限制
        RRateLimiter rateLimiter = redisson.getRateLimiter(
                String.format("CorpMessageFrequencyRateLimiter:%s", corpId)
        );

        if (!rateLimiter.isExists()) {
            log.info("[{}]当前企业发送消息的频率风控规则暂未初始化, rate = {}, rate interval = {}, rate interval unit = {}",
                    corpId, rate, rateInterval, rateIntervalUnit.name());
            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        RateLimiterConfig config = rateLimiter.getConfig();
        long oldRate = config.getRate();
        long oldRateInterval = config.getRateInterval();

        long newRateInterval = rateIntervalUnit.toMillis(rateInterval);

        if (!Objects.equals(oldRate, rate) ||
                !Objects.equals(oldRateInterval, newRateInterval)) {

            log.info("[{}]当前企业发送消息的频率风控规则发生变动, limiter = {}, old rate = {}, old rate interval = {}, new rate = {}, new rate interval = {}",
                    corpId, rateLimiter.getName(),
                    oldRate, oldRateInterval, rate, newRateInterval);

            redisson.getKeys().delete( // 删除key，这里的删除操作极端情况下可能会导致报错，影响不大暂时忽略
                    rateLimiter.getName(),
                    String.format("{%s}:permits", rateLimiter.getName()),
                    String.format("{%s}:value", rateLimiter.getName())
            );

            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        return new RateLimiterWrapper(rateLimiter, rate, rateInterval, rateIntervalUnit);
    }
}