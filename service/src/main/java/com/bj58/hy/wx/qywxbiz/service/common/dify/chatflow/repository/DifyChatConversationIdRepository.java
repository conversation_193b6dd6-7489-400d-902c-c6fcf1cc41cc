package com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.repository;

import com.bj58.hy.lib.core.util.ObjectUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DifyChatConversationIdRepository {

    private final RedissonClient redisson;

    private static final String CONVERSATION_ID_KEY_TEMPLATE = "DIFY_AI_CONVERSATION_ID:%s:%s:%s";

    public String getConversationId(@NonNull final String corpId,
                                    @NonNull final String botUserId,
                                    @NonNull final String externalUserId) {
        String key = String.format(CONVERSATION_ID_KEY_TEMPLATE, corpId, botUserId, externalUserId);

        RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);
        String conversationId = bucket.get();

        if (ObjectUtils.notEmpty(conversationId)) {
            bucket.expire(8, TimeUnit.HOURS);
        }

        return conversationId;
    }

    public void saveConversationId(@NonNull final String corpId,
                                   @NonNull final String botUserId,
                                   @NonNull final String externalUserId,
                                   @NonNull final String conversationId) {
        String key = String.format(CONVERSATION_ID_KEY_TEMPLATE, corpId, botUserId, externalUserId);

        RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);
        bucket.set(conversationId, 8, TimeUnit.HOURS);
    }

}
