package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.strategy;

import com.bj58.hy.fx.bcore.entity.neworder.OrderEntity;
import com.bj58.hy.fx.bcore.entity.order.COrderWashInfoEntity;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.ai_customer.config.AiCustomerProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanOrderQueryService;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.AbstractQueryDataStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.bo.WashProgressInfo;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 查询洗衣洗鞋进度
 * <AUTHOR>
 * @date 2025/5/9 14:25
 */
@Slf4j
@Component
public class GetWashProgressStrategy extends AbstractQueryDataStrategy {

    @Autowired
    private ExternalContactRemoteService externalContactService;

    @Autowired
    private AiCustomerProperties aiCustomerProperties;

    @Autowired
    private JingXuanOrderQueryService jingXuanOrderQueryService;

    // 洗衣洗鞋类目
    private final int CATE_ID = 12142;


    @Override
    public boolean matched(@NonNull Object biztypeObj) {

        if (StringUtils.equalsIgnoreCase(biztypeObj.toString(), "orderProgress")) {
            return true;
        }

        return false;
    }

    @Override
    public Result<Object> process(@NonNull Map<String, Object> queryParam) {

        log.info("orderProgress参数 : " + JacksonUtils.format(queryParam));

        WashProgressInfo washProgressInfo = new WashProgressInfo();


        Object orderIdObj = queryParam.get("orderId");
        String orderId = orderIdObj.toString();

        //校验订单号长度是否是19位
        if (orderId.length() != 19){
            washProgressInfo.setResult(false);
            washProgressInfo.setFailReason("无效的订单号");
            return Result.success(JacksonUtils.format(washProgressInfo));
        }

        OrderEntity orderEntity = jingXuanOrderQueryService.query(Long.parseLong(orderId));
        log.info("GetWashProgressStrategy 查询订单信息 orderId:" + orderId + ",result:" + JacksonUtils.format(orderEntity));

        if (ObjectUtils.isEmpty(orderEntity)){

            washProgressInfo.setResult(false);
            washProgressInfo.setFailReason("没有查到订单信息");
            return Result.success(JacksonUtils.format(washProgressInfo));
        }


        // 校验是否是洗衣洗鞋类目 12142
        String cateFullPath = orderEntity.getCateFullPath();
        if (StringUtils.isEmpty(cateFullPath)){

            washProgressInfo.setResult(false);
            washProgressInfo.setFailReason("没有cateFullPath无法确定是否是洗衣洗鞋类目");
            return Result.success(JacksonUtils.format(washProgressInfo));
        }

        String[] parts = cateFullPath.split(",");
        if (parts.length <= 2){

            washProgressInfo.setResult(false);
            washProgressInfo.setFailReason("根据cateFullPath无法确定是否是洗衣洗鞋类目");
            return Result.success(JacksonUtils.format(washProgressInfo));
        }

        int cateId = Integer.parseInt(parts[2]);
        if (!Objects.equals(cateId, CATE_ID)){
            washProgressInfo.setResult(false);
            washProgressInfo.setFailReason("不是洗衣洗鞋类目");
            return Result.success(JacksonUtils.format(washProgressInfo));
        }

        // 订单状态
        Integer subOrderState = orderEntity.getSubOrderState();
        washProgressInfo.setOrderState(subOrderState);

        // 进行中
        if (Objects.equals(subOrderState,2001)){

            Object externalUserIdObj = queryParam.get("externalUserId");
            String externalUserId = externalUserIdObj.toString();

            // 外部联系人转userId
            Long externalUser58Id = externalContactService.get58IdByExternalUserId(aiCustomerProperties.getCorpId(), externalUserId);
            if (ObjectUtils.isEmpty(externalUser58Id)){
                return Result.failure("externalUser58Id is empty");
            }

            COrderWashInfoEntity washInfo = jingXuanOrderQueryService.getWashInfo(Long.parseLong(orderId), externalUser58Id);
            if (ObjectUtils.isEmpty(washInfo) || CollectionUtils.isEmpty(washInfo.getItems())){
                washProgressInfo.setResult(false);
                washProgressInfo.setFailReason("没有查询到洗衣洗鞋进度");
                return Result.success(JacksonUtils.format(washProgressInfo));
            }

            washProgressInfo.setResult(true);
            washProgressInfo.setItems(washInfo.getItems());

        } else if (Objects.equals(subOrderState,9001)){

            // 服务完成
            washProgressInfo.setServiceCompleteTime(orderEntity.getServiceEndtime() != null ?
                    DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,orderEntity.getServiceEndtime()) : null);
            washProgressInfo.setResult(true);

        }else if (Objects.equals(subOrderState,3002) || Objects.equals(subOrderState,9004) || Objects.equals(subOrderState,9012)
                || Objects.equals(subOrderState,9005) || Objects.equals(subOrderState,9003) || Objects.equals(subOrderState,9002)
                || Objects.equals(subOrderState,9013) || Objects.equals(subOrderState,9009) || Objects.equals(subOrderState,6001)
                || Objects.equals(subOrderState,1001)){

            // 其余产品提供的状态
            washProgressInfo.setOrderCreateTime(orderEntity.getCreateTime() != null ?
                    DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,orderEntity.getCreateTime()) : null);
            washProgressInfo.setResult(true);

        }else{
            washProgressInfo.setResult(false);
            washProgressInfo.setFailReason("未知订单类型");
        }

        return Result.success(JacksonUtils.format(washProgressInfo));
    }
}
