package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/29 11:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SkuCategoryBo {

    private List<String> condition;

    private Map<String,String> mapping;

    private String defaultSku;

    private String url;

    private String title;

    private String description;

    private String thumbUrl;

    private String iconUrl;

    // type = 1 根据mapping匹配  type = 2 返回的字段非空匹配
    private int type;
}
