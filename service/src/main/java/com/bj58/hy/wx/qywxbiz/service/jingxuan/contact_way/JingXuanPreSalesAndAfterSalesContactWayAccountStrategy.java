package com.bj58.hy.wx.qywxbiz.service.jingxuan.contact_way;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayReq;
import com.bj58.hy.wx.qywxbiz.service.bo.BizLineEnum;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.contact_way.AbstractContactWayAccountStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.contact_way.strategy.PollingGetContactWayBizAccountStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.contact_way.strategy.TryGetPrevByWuBaIdContactWayBizAccountStrategy;
import lombok.NonNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/11/19 17:06
 */
@Order(value = Ordered.HIGHEST_PRECEDENCE)
@Component
public class JingXuanPreSalesAndAfterSalesContactWayAccountStrategy extends AbstractContactWayAccountStrategy {

    @Autowired
    private TryGetPrevByWuBaIdContactWayBizAccountStrategy tryGetPrevByWuBaIdContactWayBizAccountStrategy;

    @Autowired
    protected PollingGetContactWayBizAccountStrategy pollingGetContactWayBizAccountStrategy;

    @Autowired
    private RedissonClient redisson;

    private static final Set<Integer> ALLOW_BIZ_LINES = new HashSet<>(Arrays.asList(
            BizLineEnum.精选.getCode()
    ));

    private static final Set<Integer> ALLOW_BIZ_SCENES = new HashSet<>(Arrays.asList(
            BizSceneEnum.精选_售前扫码加微.getSceneId(),
            BizSceneEnum.精选_售后扫码加微.getSceneId()
    ));

    @Override
    public boolean matched(@NonNull ContactWayReq contactWayReq) {
        return ALLOW_BIZ_LINES.contains(contactWayReq.getBizLine()) &&
                ALLOW_BIZ_SCENES.contains(contactWayReq.getBizScene());
    }

    @Override
    @Nullable
    public List<String> getUserIds(@NonNull ContactWayReq contactWayReq) {
        JSONObject stateMappedValueObj = getStateMappedValueObj(contactWayReq);
        if (ObjectUtils.isEmpty(stateMappedValueObj)) {
            return null;
        }

        int bizLine = stateMappedValueObj.getIntValue("bizLine");
        int bizScene = stateMappedValueObj.getIntValue("bizScene");
        if (!ALLOW_BIZ_LINES.contains(bizLine) || !ALLOW_BIZ_SCENES.contains(bizScene)) {
            return null;
        }

        JSONObject bindWuBa = stateMappedValueObj.getJSONObject("_BindWuBa_");
        if (ObjectUtils.notEmpty(bindWuBa)) {
            Long wubaId = bindWuBa.getLong("id");
            if (ObjectUtils.notNull(wubaId)) {
                // 如果有58id且在配置中 则走测试企微账号
                RSet<String> testUser = redisson.getSet("QYWX_WUBA_TEST_USER", StringCodec.INSTANCE);
                if (testUser.contains(String.valueOf(wubaId))) {
                    RSet<String> testAccount = redisson.getSet("QYWX_USER_TEST_ACCOUNT", StringCodec.INSTANCE);
                    return new ArrayList<>(testAccount);
                }

                return tryGetPrevByWuBaIdContactWayBizAccountStrategy.getUser(contactWayReq, wubaId);
            }
        }

        return pollingGetContactWayBizAccountStrategy.getUser(contactWayReq);
    }
}
