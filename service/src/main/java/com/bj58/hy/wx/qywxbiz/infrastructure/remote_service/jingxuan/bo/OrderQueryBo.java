package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo;

import com.bj58.hy.lib.core.support.pojo.PagedQuery;
import com.bj58.lbg.business.plaform.oms.api.dto.query.SearchQuerySortField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrderQueryBo extends PagedQuery {

    /**
     * 58id
     */
    private Long wbUserId;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 服务类目
     */
    private Integer cateId;

    /**
     * 查询排序列表条件
     */
    private List<SearchQuerySortField> querySearchSortEntityList;


    /**
     * 订单创建时间
     */
    private Date OrderCreateTimeStart;
    private Date OrderCreateTimeEnd;

    /**
     * 支付时间
     */
    private Date payTimeStart;
    private Date payTimeEnd;

    /**
     * 服务完成时间
     */
    private Date serviceCompletionTimeStart;
    private Date serviceCompletionTimeEnd;

}