package com.bj58.hy.wx.qywxbiz.repository;

import com.bj58.hy.wx.qywxbiz.entity.DifyChatAnalysisResultEntity;
import com.bj58.hy.wx.qywxbiz.entity.QDifyChatAnalysisResultEntity;
import com.bj58.hy.wx.qywxbiz.repository.jpa.DifyChatAnalysisResultJpaRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.Date;

/**
 * Dify聊天分析结果Repository
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DifyChatAnalysisResultRepository {

    private final DifyChatAnalysisResultJpaRepository repository;
    private final EntityManager entityManager;

    /**
     * 保存Dify分析成功结果
     */
    @Transactional
    public void save(@NonNull final String userId,
                     @NonNull final String externalUserId,
                     @NonNull final String analysisDate,
                     @NonNull final String difyResult,
                     @NonNull final Integer chatMessageCount,
                     final String category4Id) {

        DifyChatAnalysisResultEntity entity = DifyChatAnalysisResultEntity.builder()
                .userId(userId)
                .externalUserId(externalUserId)
                .analysisDate(analysisDate)
                .difyResult(difyResult)
                .chatMessageCount(chatMessageCount)
                .category4Id(category4Id)
                .createTime(new Date())
                .updateTime(new Date())
                .status(1) // 成功状态
                .build();

        repository.save(entity);
        log.info("保存Dify分析结果成功，userId={}, externalUserId={}, analysisDate={}, category4Id={}",
                userId, externalUserId, analysisDate, category4Id);
    }

    /**
     * 保存Dify分析失败记录
     */
    @Transactional
    public void saveFailure(@NonNull final String userId,
                           @NonNull final String externalUserId,
                           @NonNull final String analysisDate,
                           @NonNull final String failureReason,
                           @NonNull final Integer chatMessageCount) {

        DifyChatAnalysisResultEntity entity = DifyChatAnalysisResultEntity.builder()
                .userId(userId)
                .externalUserId(externalUserId)
                .analysisDate(analysisDate)
                .difyResult("") // 失败时为空
                .chatMessageCount(chatMessageCount)
                .failureReason(failureReason)
                .createTime(new Date())
                .updateTime(new Date())
                .status(2) // 失败状态
                .build();

        repository.save(entity);
        log.warn("保存Dify分析失败记录，userId={}, externalUserId={}, analysisDate={}, failureReason={}",
                userId, externalUserId, analysisDate, failureReason);
    }

    /**
     * 检查指定日期的分析结果是否已存在（包括成功和失败的记录）
     */
    @Transactional(readOnly = true)
    public boolean existsByUserAndDate(@NonNull final String userId,
                                       @NonNull final String externalUserId,
                                       @NonNull final String analysisDate) {
        QDifyChatAnalysisResultEntity qEntity = QDifyChatAnalysisResultEntity.difyChatAnalysisResultEntity;

        Long count = new JPAQueryFactory(entityManager)
                .selectFrom(qEntity)
                .where(qEntity.userId.eq(userId))
                .where(qEntity.externalUserId.eq(externalUserId))
                .where(qEntity.analysisDate.eq(analysisDate))
                .fetchCount();

        return count > 0;
    }

    /**
     * 根据分析日期删除数据
     * @param analysisDate 分析日期（格式：yyyy-MM-dd）
     * @return 删除的记录数量
     */
    @Transactional
    public long deleteByAnalysisDate(@NonNull final String analysisDate) {
        QDifyChatAnalysisResultEntity qEntity = QDifyChatAnalysisResultEntity.difyChatAnalysisResultEntity;

        long deletedCount = new JPAQueryFactory(entityManager)
                .delete(qEntity)
                .where(qEntity.analysisDate.eq(analysisDate))
                .execute();

        log.info("根据分析日期删除Dify分析结果，analysisDate={}, 删除记录数={}", analysisDate, deletedCount);
        return deletedCount;
    }

}
