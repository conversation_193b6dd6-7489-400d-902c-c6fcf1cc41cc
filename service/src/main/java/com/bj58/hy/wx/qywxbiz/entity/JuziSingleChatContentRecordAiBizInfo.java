package com.bj58.hy.wx.qywxbiz.entity;

import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.bj58.hy.wx.qywxbiz.entity.enums.AiType;
import com.bj58.hy.wx.qywxbiz.entity.enums.NotReqAiReason;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: AI聊天的附属信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JuziSingleChatContentRecordAiBizInfo {

    /**
     * 消息的唯一标识
     */
    private String messageId;

    private AiType aiType;

    private SingleChatSendBy sendBy;

    /**
     * 仅当sendBy = {@link SingleChatSendBy#EXTERNAL_USER} 时存在
     */
    private ReqToAiBizInfo reqToAiBizInfo;

    /**
     * 仅当sendBy = {@link SingleChatSendBy#CORP_USER} 时存在
     */
    private RespByAiBizInfo respByAiBizInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReqToAiBizInfo {

        /**
         * 当前消息 是否尝试请求AI应答？
         */
        private boolean flag;

        /**
         * 没有请求AI的原因
         */
        private NotReqAiReason notReqReason;

        /**
         * 请求AI时，聚合的所有消息（因为30s聚合一次聊天去请求AI，所以是数组形式）
         */
        private List<String> togetherMessageIds;

        /**
         * 如果尝试请求了AI，此字段记录了其返回的结果
         */
        private Object aiResult;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RespByAiBizInfo {

        /**
         * 当前消息 是否是AI回复的？
         */
        private boolean flag;

        /**
         * AI回复的是哪些消息（因为30s聚合一次聊天去请求AI，所以是数组形式）
         */
        private List<String> replyMessageIds;
    }


}
