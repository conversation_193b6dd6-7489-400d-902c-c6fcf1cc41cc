package com.bj58.hy.wx.qywxbiz.service.jiafu.dify_ai_workflow;

import com.alibaba.fastjson.JSON;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.group_chat.ExternalContactGroupChatSnapshotResp;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziGroupChatContentRecord;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.GroupChatRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowRequest;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowResponse;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.dify.bo.DifyApiInfoBo;
import lombok.NonNull;
import org.redisson.api.RBucket;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
public class JuziChatEventThenDifyJiaFuGroupChatAiWorkflowHandler extends AbstractJiaFuAiWorkflowHandler {

    @Autowired
    private GroupChatRemoteService groupChatRemoteService;

    private static final String GROUP_CHAT_TYPE = "group_chat";
    private static final String HISTORY_KEY_PREFIX = "JiaFuGroupChatAutoAiReply:History";

    @Override
    public String getMessageType() {
        return GROUP_CHAT_TYPE;
    }

    @Override
    protected String getHistoryKeyPrefix() {
        return HISTORY_KEY_PREFIX;
    }

    @Override
    protected boolean shouldProcess(@NonNull ChatMessagesEventBo.Body callbackBo) {
        // 只处理群聊消息
        return ObjectUtils.notEmpty(callbackBo.getRoomWecomChatId());
    }

    @Override
    protected void commitDelayedTriggerTask(@NonNull final String messageId, @NonNull final String type, String roomWecomChatId) {
        // 检查特定群ID
        long delaySeconds = getDelaySecondsWithRoomWecomChatId(roomWecomChatId);
        
        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("messageId", messageId);
        bodyInfoObj.put("type", type); // 添加消息类型标识

        wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), delaySeconds);
    }

    @Override
    protected long getDelaySeconds() {
        return 5 * 60; // 其他时间使用5分钟延迟
    }
    
    /**
     * 根据消息ID获取延迟时间，检查特定群ID
     * 
     * @param roomWecomChatId 消息ID
     * @return 延迟秒数
     */
    private long getDelaySecondsWithRoomWecomChatId(String roomWecomChatId) {
        return getDelaySeconds();
    }

    @Override
    protected void execWorkflow(@NonNull final String messageId) {
        execGroupChatWorkflow(messageId);
    }

    /**
     * 执行群聊工作流处理，保留原有逻辑
     */
    public void execGroupChatWorkflow(@NonNull final String messageId) {
        // 是否标准化了聊天记录?
        JuziGroupChatContentRecord groupChatContentRecord = chatContentRemoteService.getGroupChatContentRecord(messageId);
        if (ObjectUtils.isNull(groupChatContentRecord)) {
            log.error("not found standard group chat content record, message id = {}", messageId);
            return;
        }

        final String corpId = groupChatContentRecord.getCorpId();
        final String chatId = groupChatContentRecord.getChatId();

        // 是否存在群信息？
        ExternalContactGroupChatSnapshotResp groupChatInfoFromSnapshot =
                groupChatRemoteService.getExternalContactGroupChatInfoFromSnapshot(corpId, chatId);
        if (ObjectUtils.isNull(groupChatInfoFromSnapshot)) {
            log.warn("not found group chat info, corp id = {}, chat id = {}", corpId, chatId);
            return;
        }

        // 是否是家服业务的群？
        ExternalContactGroupChatSnapshotResp.BizInfo bizInfo = groupChatInfoFromSnapshot.getBizInfo();
        if (ObjectUtils.isNull(bizInfo)) {
            return;
        }
        if (!Objects.equals(BizSceneEnum.家服_阿姨接单自动建群.getLineId(), bizInfo.getBizLine()) ||
                !Objects.equals(BizSceneEnum.家服_阿姨接单自动建群.getSceneId(), bizInfo.getBizScene())) {
            return;
        }

        // API key
        RBucket<String> bucket = redisson.getBucket("JiaFuGroupChatAutoAiReplyWorkFlow", StringCodec.INSTANCE);
        String apiKey = bucket.get();

        if (ObjectUtils.isEmpty(apiKey)) { // 这里不应该查不到 api key
            log.error("not found JiaFuGroupChatAutoAiReplyWorkFlow api key, corp id = {}, chat id = {}",
                    corpId, chatId);
            return;
        }

        @NonNull final DifyApiInfoBo.Item apiInfo =
                new DifyApiInfoBo.Item("闫风", "6519e9f1-db8b-40f2-80d3-c8ad387ad51f", apiKey);


        WorkflowRequest request = new WorkflowRequest();

        String user = String.format("%s:%s", corpId, chatId);
        request.setUser(user);

        request.getInputs().put("corpId", corpId);
        request.getInputs().put("chatId", chatId);
        request.getInputs().put("messageId", messageId);

        try {
            log.info("request JiaFuGroupChatAutoAiReplyWorkFlow, req = {}", JacksonUtils.format(request));

            Result<WorkflowResponse> result = difyRemoteService.workflow(
                    apiKey, request);

            log.info("request JiaFuGroupChatAutoAiReplyWorkFlow done, req = {}, resp = {}",
                    JacksonUtils.format(request), JacksonUtils.format(result));


            if (ObjectUtils.isNull(result) || result.isFailed() || ObjectUtils.isNull(result.getData())) {
                difyWorkflowAlarmComponent.sendTipsToMeiShi_GROUP(corpId, chatId, apiInfo, result);
            }

        } catch (Exception e) {
            log.error("trigger JiaFuGroupChatAutoAiReplyWorkFlow exception, workflow request = {}", JacksonUtils.format(request), e);
        }
    }
}
