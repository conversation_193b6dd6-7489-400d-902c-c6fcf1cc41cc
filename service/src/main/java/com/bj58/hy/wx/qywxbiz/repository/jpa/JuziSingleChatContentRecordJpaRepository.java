package com.bj58.hy.wx.qywxbiz.repository.jpa;

import com.bj58.hy.wx.qywxbiz.entity.JuziSingleChatContentRecordEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 句子聊天记录JPA Repository
 */
@Repository
public interface JuziSingleChatContentRecordJpaRepository
        extends JpaRepository<JuziSingleChatContentRecordEntity, Long> {

    /**
     * 查询指定企业、用户、时间范围内的聊天记录
     */
    @Query("SELECT r FROM JuziSingleChatContentRecordEntity r " +
            "WHERE r.corpId = :corpId " +
            "AND r.userId = :userId " +
            "AND r.createTime >= :startDate " +
            "AND r.createTime < :endDate " +
            "AND r.externalUserId IS NOT NULL " +
            "ORDER BY r.createTime ASC")
    List<JuziSingleChatContentRecordEntity> findChatRecords(
            @Param("corpId") String corpId,
            @Param("userId") String userId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

}
