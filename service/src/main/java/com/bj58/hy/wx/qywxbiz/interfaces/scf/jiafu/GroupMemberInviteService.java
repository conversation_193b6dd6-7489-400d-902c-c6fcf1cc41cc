package com.bj58.hy.wx.qywxbiz.interfaces.scf.jiafu;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.spring.support.aspect.GlobalExceptionWrapper;
import com.bj58.hy.lib.spring.support.aspect.VerifiedParams;
import com.bj58.hy.wx.qywxbiz.contract.IGroupMemberInviteService;
import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.InviteExternalUserToGroupReq;
import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.InviteExternalUserToGroupResp;
import com.bj58.spat.scf.server.contract.annotation.ServiceBehavior;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 群成员邀请服务实现
 */
@Slf4j
@Component
@ServiceBehavior
public class GroupMemberInviteService implements IGroupMemberInviteService {

    @Autowired
    private GroupMemberInviteTransactionService transactionService;

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<InviteExternalUserToGroupResp> inviteExternalUserToGroup(@NotNull @Valid final InviteExternalUserToGroupReq req) throws Exception {
        // 调用事务服务的方法
        return transactionService.inviteExternalUserToGroup(
                req.getCorpId(),
                req.getUserId(),
                req.getExternalUserId(),
                req.getCityName()
        );
    }
}