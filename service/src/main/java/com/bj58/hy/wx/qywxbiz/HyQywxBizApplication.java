package com.bj58.hy.wx.qywxbiz;


import com.alibaba.fastjson.parser.ParserConfig;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.meishi.openapi.util.MeishiApplication;
import com.bj58.spat.cmc.service.Initializer;
import com.fasterxml.jackson.databind.DeserializationFeature;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.text.SimpleDateFormat;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableJpaRepositories(basePackages = {
        "com.bj58.hy.wx.qywxbiz.repository.jpa",
})
public class HyQywxBizApplication {

    public static void main(String[] args) throws Exception {
        ParserConfig.getGlobalInstance().setSafeMode(true);

        JacksonUtils.getInstance()
                .setTimeZone(TimeZone.getTimeZone("GMT+8"));
        JacksonUtils.getInstance()
                .setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        JacksonUtils.getInstance()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        final SpringApplication application = new SpringApplication(HyQywxBizApplication.class);
        application.run(args);

        MeishiApplication.initByValue("hyqywx", "360664acf9c7077e2c52a5c8dabecec5");
        Initializer.initCache(); // CMCPC
    }


}
