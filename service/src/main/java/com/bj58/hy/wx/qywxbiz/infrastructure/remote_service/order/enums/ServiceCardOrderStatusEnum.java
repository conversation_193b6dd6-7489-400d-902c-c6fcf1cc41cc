package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ServiceCardOrderStatusEnum {

    //    ERROR("兑换失败/组合下单失败", -1),
    INIT("兑换处理中", 1000),
    UNPAY("待支付", 3002),
    PAY("已支付", 3003),
    CANCEL("已取消", 9002),
    COMPLETE("服务完成", 9001),
    EXPIRE("已过期", 6001),
    REFUND("已退款", 3007),
    ;

    private final String name;
    private final Integer code;

    public final static Map<Integer, ServiceCardOrderStatusEnum> MAP = Arrays.stream(ServiceCardOrderStatusEnum.values()).collect(Collectors.toMap(ServiceCardOrderStatusEnum::getCode, Function.identity()));

    public static ServiceCardOrderStatusEnum getEnumByCode(Integer code) {
        return MAP.get(code);
    }

    public static String getNameByCode(Integer code) {
        ServiceCardOrderStatusEnum djjxOrderStatusEnum = MAP.get(code);
        if (ObjectUtils.isEmpty(djjxOrderStatusEnum)) {
            return null;
        }
        return djjxOrderStatusEnum.getName();
    }

}
