package com.bj58.hy.wx.qywxbiz.service.common.contact_way;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IWxWorkStateMappingService;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayReq;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * Description: 
 *
 * <AUTHOR>
 */
public abstract class AbstractContactWayAccountStrategy {

    protected org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(this.getClass());

    @SCFClient(lookup = IWxWorkStateMappingService.SCF_URL)
    private IWxWorkStateMappingService stateMappingService;

    public abstract boolean matched(@NonNull final ContactWayReq contactWayReq);

    @Nullable
    public abstract List<String> getUserIds(@NonNull final ContactWayReq contactWayReq);

    protected JSONObject getStateMappedValueObj(@NonNull final ContactWayReq contactWayReq) {
        String state = contactWayReq.getState();

        if (ObjectUtils.isEmpty(state)) {
            return null;
        }

        // 需要从state参数中解析出58id
        String stateMappedValue;
        try {
            Result<String> stateMappedValueResult = stateMappingService.getStateMappedValue(state);
            stateMappedValue = stateMappedValueResult.getData();
        } catch (Exception e) {
            log.error(String.format("get state mapped value error, state = %s, err msg = %s", state,
                    e.getMessage()), e);
            return null;
        }

        if (ObjectUtils.isEmpty(stateMappedValue)) {
            log.error("not found state mapped value, state = {}", state);
            return null;
        }

        JSONObject stateMappedValueObj;
        try {
            stateMappedValueObj = JSON.parseObject(stateMappedValue);
        } catch (Exception e) {
            log.error(String.format("parse state mapped value error, state = %s, state mapped value = %s, err msg = %s",
                    state, stateMappedValue, e.getMessage()), e);
            return null;
        }

        return stateMappedValueObj;
    }

}
