package com.bj58.hy.wx.qywxbiz.entity.converter;


import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.entity.enums.NotReqAiReason;

import javax.persistence.AttributeConverter;

/**
 * Description: 状态
 *
 * <AUTHOR>
 */
@javax.persistence.Converter
public class NotReqAiReasonConverter implements AttributeConverter<NotReqAiReason, Integer> {

    @Override
    public Integer convertToDatabaseColumn(NotReqAiReason attribute) {
        if (ObjectUtils.isNull(attribute)) {
            return null;
        }

        return attribute.getCode();
    }

    @Override
    public NotReqAiReason convertToEntityAttribute(Integer dbData) {
        if (ObjectUtils.isNull(dbData)) {
            return null;
        }

        return NotReqAiReason.strictOf(dbData);
    }

}
