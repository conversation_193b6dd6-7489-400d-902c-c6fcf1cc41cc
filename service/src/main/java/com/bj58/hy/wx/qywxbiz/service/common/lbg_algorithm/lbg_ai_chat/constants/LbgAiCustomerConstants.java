package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.constants;

public class LbgAiCustomerConstants {

    // ----欢迎语、转人工等文案
    public static String AI_TO_STAFF_MESSAGE_VOICE = "客户：%s当前使用语音咨询了问题，请尽快协助处理";

    public static String AI_TO_USER_MESSAGE = "客户：%s当前咨询的问题AI无法解答，请尽快协助处理。";

    public static String AI_TO_EXTERNAL_USER_MESSAGE_WHEN_ERROR = "当前咨询人数较多，请您耐心等待";

    public static String CORP_USER_BUSY_TO_EXTERNAL_USER = "当前客服坐席繁忙，如您有紧急问题，可拨打客服热线400-8583645";

    /**
     * 用户反馈回收问题的消息内容
     */
    public static final String FEEDBACK_RECOVERY_MESSAGE = "亲，请问您的问题是否已解决？\n" +
            "1、已解决\n" +
            "2、未解决\n" +
            "如果您还有其他问题随时联系我们为您解决哈，祝您生活愉快！";

    /**
     * 长时间未聊天时，第一次客户询问，客服发送的应答语
     */
    public static final String QUICK_RESPONSE_MESSAGE = "在的，稍等，我看下历史消息，方便为您提供解答～";
    
    /**
     * 用户加微后首次咨询，客服发送的承接语
     */
    public static final String FIRST_CONSULT_RESPONSE_MESSAGE = "在的，稍等哈";

    /**
     * 线上售后加微用户，会话过程中触发【转人工】 发送的文案
     */
    public static final String AFTER_SALES_ACCOUNT_TO_EXTERNAL_USER_MESSAGE = "当前咨询人数较多，非常抱歉无法及时回复， 为保证您的问题得到及时处理，您可以直接拨打客服热线 ********** ，感谢您的理解与支持！";

}
