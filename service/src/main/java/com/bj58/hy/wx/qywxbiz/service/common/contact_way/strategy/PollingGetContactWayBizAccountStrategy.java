package com.bj58.hy.wx.qywxbiz.service.common.contact_way.strategy;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayReq;
import com.bj58.hy.wx.qywxbiz.entity.WxWorkContactWayBizAccountConfEntity;
import com.bj58.hy.wx.qywxbiz.repository.WxWorkContactWayBizAccountConfRepository;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 提供轮询规则的 获取企业成员的 策略
 */
@Slf4j
@Component
public class PollingGetContactWayBizAccountStrategy {

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private RedisLockSupport lockSupport;

    @Autowired
    protected WxWorkContactWayBizAccountConfRepository repository;

    @Nullable
    public List<String> getUser(@NonNull final ContactWayReq req) {
        List<String> userIds = new ArrayList<>();

        // 获取数据库配置
        List<WxWorkContactWayBizAccountConfEntity> accountEntities = repository.getAvailableUser(
                req.getBizLine(), Collections.singleton(req.getBizScene()),
                req.getCorpId(), req.getCityId());

        log.info("查询业务账号配置，业务线：{}, 业务场景：{}, 可用企业成员账号：{}",
                req.getBizLine(), req.getBizScene(),
                JSONObject.toJSONString(accountEntities));

        String userId = getNextUserId(accountEntities, req);

        log.info("最终查到的账号id : {}", userId);

        if (StringUtils.isNotEmpty(userId)) {
            userIds.add(userId);
        }

        return userIds;
    }


    private String getNextUserId(@NonNull final List<WxWorkContactWayBizAccountConfEntity> availableUsers,
                                 @NonNull final ContactWayReq req) {

        if (ObjectUtils.isEmpty(availableUsers)) {
            return null;
        }

        return lockSupport.execute(
                String.format("%s:CONTACT_WAY_PRE_ACCOUNT:BIZ_LINE:%s:BIZ_SCENE:%s:LOCK", req.getCorpId(), req.getBizLine(), req.getBizScene()),
                () -> {
                    List<String> availableUserIds = availableUsers.stream()
                            .map(WxWorkContactWayBizAccountConfEntity::getUserId)
                            .collect(Collectors.toList());

                    // redis取上一次的user
                    RBucket<String> prevUserBucket = redisson.getBucket(
                            String.format("%s:WxWorkContactWayBizAccount:bizLine:%s:bizScene:%s", req.getCorpId(), req.getBizLine(), req.getBizScene()),
                            StringCodec.INSTANCE);

                    String nextUserId = null;

                    String prevUser = prevUserBucket.get();
                    if (ObjectUtils.notEmpty(prevUser)) { // 如果之前处理过，则尝试取下一个
                        nextUserId = getNext(availableUserIds, prevUser);
                    }

                    // 如果之前没有记录，或者其他的特殊情况
                    if (ObjectUtils.isEmpty(nextUserId)) {
                        nextUserId = availableUserIds.get(0);
                    }

                    // 存入最新账号
                    prevUserBucket.set(nextUserId, 5, TimeUnit.DAYS);

                    return nextUserId;
                }
        );
    }

    private static String getNext(@NonNull final List<String> availableUserIds,
                                  @NonNull final String currUserId) {
        if (ObjectUtils.isEmpty(availableUserIds)) {
            return null;
        }

        int currentIndex = availableUserIds.indexOf(currUserId);

        if (currentIndex == -1) { // 之前处理过，但是最新的配置中没有对应的企业成员
            return null;
        }

        int nextIndex = (currentIndex + 1) % availableUserIds.size();
        return availableUserIds.get(nextIndex);
    }
}
