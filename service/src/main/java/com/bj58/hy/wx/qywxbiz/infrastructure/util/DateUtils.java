package com.bj58.hy.wx.qywxbiz.infrastructure.util;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 */
public class DateUtils {

    private DateUtils() {
    }

    public static Date getTomorrowZeroOClock() {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MILLISECOND, 0);
        instance.add(Calendar.DAY_OF_YEAR, 1);

        return instance.getTime();
    }

    public static Date getYesterdayZeroOClock() {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MILLISECOND, 0);
        instance.add(Calendar.DAY_OF_YEAR, -1);

        return instance.getTime();
    }

    public static Date getTodayZeroOClock() {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MILLISECOND, 0);

        return instance.getTime();
    }

    public static List<String> getDateStringOf14Days() {

        return getDateStringOfSpecialDays(14);
    }

    public static List<String> getDateStringOfSpecialDays(int days) {
        List<String> result = new ArrayList<>(days);

        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MILLISECOND, 0);

        instance.add(Calendar.DAY_OF_YEAR, 1);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

        for (int i = 0; i < days; i++) {
            instance.add(Calendar.DAY_OF_YEAR, -1);
            String yyyyMMdd = dateFormat.format(instance.getTime());
            result.add(yyyyMMdd);
        }

        return result;
    }

    public static long getTomorrowZeroTimeInterval() {
        LocalDateTime now = LocalDateTime.now();

        LocalDateTime tomorrowZeroOClock = DateUtils.getTomorrowZeroOClock()
                .toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        return Duration.between(now, tomorrowZeroOClock).getSeconds();
    }

}
