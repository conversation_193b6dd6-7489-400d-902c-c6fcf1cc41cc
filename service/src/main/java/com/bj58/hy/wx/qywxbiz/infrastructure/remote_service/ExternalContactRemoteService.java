package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.support.pojo.KeyValue;
import com.bj58.hy.lib.core.support.pojo.PagedResult;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IExternalContactMappingService;
import com.bj58.hy.wx.qywx.contract.IExternalContactService;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.*;
import com.bj58.hy.wx.qywx.contract.dto.external_contact_mapping.ExternalUserLinkedWuBaIdResp;
import com.bj58.hy.wx.qywx.contract.dto.external_contact_mapping.BizKeyLinkedExternalUserResp;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class ExternalContactRemoteService {

    @SCFClient(lookup = IExternalContactService.SCF_URL)
    private IExternalContactService externalContactService;

    @SCFClient(lookup = IExternalContactMappingService.SCF_URL)
    private IExternalContactMappingService externalContactMappingService;

    public GetExternalContactRelationshipResp getRelationship(@NonNull final String corpId,
                                                              @NonNull final String userId,
                                                              @NonNull final String externalUserId) {

        Result<PagedResult<GetExternalContactRelationshipResp>> contactRelationshipResult = null;
        try {
            GetExternalContactRelationshipReq relationshipReq = new GetExternalContactRelationshipReq();
            relationshipReq.setCorpId(corpId);
            relationshipReq.setUserIds(Collections.singleton(userId));
            relationshipReq.setExternalUserId(externalUserId);
            relationshipReq.setPage(1);
            relationshipReq.setSize(1);
            contactRelationshipResult = externalContactService.getExternalContactRelationship(relationshipReq);

        } catch (Exception e) {
            log.error("get external contact relation ship error, " +
                    "corp id = {}, user id = {}, external user id = {}", corpId, userId, externalUserId, e);
            return null;
        }

        if (ObjectUtils.isNull(contactRelationshipResult) ||
                contactRelationshipResult.isFailed() ||
                ObjectUtils.isNull(contactRelationshipResult.getData()) ||
                ObjectUtils.isEmpty(contactRelationshipResult.getData().getResults())) {
            log.error("not found external contact relation ship, " +
                    "corp id = {}, user id = {}, external user id = {}", corpId, userId, externalUserId);
            return null;
        }

        return contactRelationshipResult.getData().getResults().get(0);
    }

    public Long get58IdByExternalUserId(@NonNull final String corpId,
                                        @NonNull final String externalUserId) {

        Result<List<ExternalUserLinkedWuBaIdResp>> result;
        try {
            result = externalContactMappingService.getExternalUserLinkedWuBaIds(corpId, externalUserId);

        } catch (Exception e) {
            log.error("get external user linked wuba ids error, " +
                    "corp id = {}, external user id = {}", corpId, externalUserId, e);
            return null;
        }

        if (ObjectUtils.isNull(result) ||
                result.isFailed() ||
                ObjectUtils.isNull(result.getData())) {
            log.error("get external user linked wuba ids error, " +
                    "corp id = {}, external user id = {}", corpId, externalUserId);
            return null;
        }

        for (ExternalUserLinkedWuBaIdResp item : result.getData()) {
            if ("_wx_binding_".equals(item.getReason())) {
                return item.getExternalUser58Id();
            }
        }

        return null;
    }

    public List<Long> get58IdsByExternalUserId(@NonNull final String corpId,
                                        @NonNull final String externalUserId) {

        Result<List<ExternalUserLinkedWuBaIdResp>> result;
        try {
            result = externalContactMappingService.getExternalUserLinkedWuBaIds(corpId, externalUserId);

        } catch (Exception e) {
            log.error("get external user linked wuba ids error, " +
                    "corp id = {}, external user id = {}", corpId, externalUserId, e);
            return null;
        }

        if (ObjectUtils.isNull(result) ||
                result.isFailed() ||
                ObjectUtils.isNull(result.getData())) {
            log.error("get external user linked wuba ids error, " +
                    "corp id = {}, external user id = {}", corpId, externalUserId);
            return null;
        }

        return result.getData().stream().map(ExternalUserLinkedWuBaIdResp::getExternalUser58Id).collect(Collectors.toList());
    }

    /**
     * 获取 指定企业成员的 历史好友关系总数
     */
    public long countRelationByDate(@NonNull final String corpId,
                                    @NonNull final String userId,
                                    @Nullable final Date createTimeBegin,
                                    @Nullable final Date createTimeEnd) {

        Result<PagedResult<GetCorpUserHistoricalRelationshipResp>> contactRelationshipResult;
        try {
            GetCorpUserHistoricalRelationshipReq relationshipReq = new GetCorpUserHistoricalRelationshipReq();
            relationshipReq.setCorpId(corpId);
            relationshipReq.setUserId(userId);
            relationshipReq.setCreateTimeBegin(createTimeBegin);
            relationshipReq.setCreateTimeEnd(createTimeEnd);
            relationshipReq.setOnlyTotal(true);

            contactRelationshipResult = externalContactService.getCorpUserHistoricalRelationship(relationshipReq);

        } catch (Exception e) {
            log.error("count external contact relation error, " +
                    "corp id = {}, user id = {}, begin date = {}, end date = {}", corpId, userId, createTimeBegin, createTimeEnd, e);
            return 0;
        }

        if (ObjectUtils.isNull(contactRelationshipResult) ||
                contactRelationshipResult.isFailed()) {
            log.error("count external contact relation error, " +
                    "corp id = {}, user id = {}, begin date = {}, end date = {}", corpId, userId, createTimeBegin, createTimeEnd);
            return 0;
        }

        return contactRelationshipResult.getData().getTotal();
    }

    /**
     * 获取 指定企业成员的 当天的好友数
     */
    public long getCorpUserAddExternalUserIntradayCount(@NonNull final String corpId,
                                                        @NonNull final String userId) {

        String currDay = new SimpleDateFormat("yyyyMMdd").format(new Date());

        Result<List<KeyValue<String, Integer>>> currDayStatistics;
        try {
            GetCorpUserAddExternalUserDailyStatisticsReq dailyStatisticsReq = new GetCorpUserAddExternalUserDailyStatisticsReq();
            dailyStatisticsReq.setCorpId(corpId);
            dailyStatisticsReq.setUserId(userId);

            dailyStatisticsReq.setTargetDates(Collections.singletonList(currDay));

            currDayStatistics = externalContactService.getCorpUserAddExternalUserDailyStatistics(dailyStatisticsReq);

        } catch (Exception e) {
            log.error("get corp user add external contact relation statistics error, " +
                    "corp id = {}, user id = {}", corpId, userId, e);
            return 0;
        }

        if (ObjectUtils.isNull(currDayStatistics) ||
                currDayStatistics.isFailed() ||
                ObjectUtils.isEmpty(currDayStatistics.getData()) ||
                ObjectUtils.isEmpty(currDayStatistics.getData().get(0))) {
            log.error("get corp user add external contact relation statistics error, " +
                    "corp id = {}, user id = {}, result = {}", corpId, userId, JacksonUtils.format(currDayStatistics));
            return 0;
        }

        KeyValue<String, Integer> keyValuePojo = currDayStatistics.getData().get(0);
        return keyValuePojo.getValue();
    }

    /**
     * 根据58用户ID或业务ID查询外部联系人关系
     */
    public Result<List<GetExternalContactRelationshipResp>> getExternalContactRelationshipByBindOrBiz58Id(
            @NonNull final GetExternalContactRelationshipByBindOrBiz58IdReq req) {

        try {
            Result<List<GetExternalContactRelationshipResp>> result =
                    externalContactService.getExternalContactRelationshipByBindOrBiz58Id(req);

            log.info("查询58用户{}的外部联系人关系，结果：{}", req.getWubaUid(),
                    result != null ? result.isSuccess() : "null");

            return result;

        } catch (Exception e) {
            log.error("查询58用户{}的外部联系人关系失败，错误信息：{}", req.getWubaUid(), e.getMessage(), e);
            return Result.failure("查询外部联系人关系失败：" + e.getMessage());
        }
    }

    /**
     * 根据业务key查询已绑定该key的外部用户信息
     * 使用 {@link IExternalContactMappingService#getBizKeyLinkedExternalUsers(String, String)} 获取结果
     */
    public List<String> getBizKeyLinkedExternalUsers(@NonNull final String corpId,
                                                     @NonNull final String bizKey) {
        try {
            log.info("查询业务key{}绑定的外部用户信息，corpId：{}", bizKey, corpId);

            Result<List<BizKeyLinkedExternalUserResp>> result =
                    externalContactMappingService.getBizKeyLinkedExternalUsers(corpId, bizKey);

            if (result != null && result.isSuccess() && ObjectUtils.notEmpty(result.getData())) {
                log.info("找到业务key{}绑定的外部用户数量：{}", bizKey, result.getData().size());

                // 从BizKeyLinkedExternalUserResp中提取externalUserId
                return result.getData().stream()
                        .map(BizKeyLinkedExternalUserResp::getExternalUserId)
                        .collect(Collectors.toList());
            } else {
                log.info("业务key{}未绑定任何外部用户，result: {}", bizKey,
                        result != null ? result.getMsg() : "null");
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("查询业务key{}绑定的外部用户信息失败，错误信息：{}", bizKey, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

}
