package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.bo;

import com.bj58.hy.fx.bcore.entity.order.COrderWashInfoItemEntity;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 14:46
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class WashProgressInfo {

    /**
     * 订单状态
     */
    private Integer orderState;

    /**
     * 洗衣洗鞋进度
     */
    private List<COrderWashInfoItemEntity> items;

    /**
     * 服务完成时间
     */
    private String serviceCompleteTime;

    /**
     * 订单创建时间
     */
    private String orderCreateTime;

    /**
     * 结果
     */
    private boolean result;

    /**
     * 查询失败原因
     */
    private String failReason;

}
