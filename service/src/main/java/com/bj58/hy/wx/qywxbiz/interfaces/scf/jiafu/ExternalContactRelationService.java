package com.bj58.hy.wx.qywxbiz.interfaces.scf.jiafu;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.support.pojo.PagedResult;
import com.bj58.hy.lib.spring.support.aspect.GlobalExceptionWrapper;
import com.bj58.hy.lib.spring.support.aspect.VerifiedParams;
import com.bj58.hy.wx.qywx.contract.IExternalContactService;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipReq;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.JuZiCustomerDetailResp;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.enums.ExternalContactStatus;
import com.bj58.hy.wx.qywxbiz.contract.IExternalContactRelationService;
import com.bj58.hy.wx.qywxbiz.contract.dto.external_contact.ExternalContactRelationResp;
import com.bj58.hy.wx.qywxbiz.contract.dto.external_contact.enums.ExternalContactRelationStatus;
import com.bj58.spat.scf.server.contract.annotation.ServiceBehavior;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;

@Slf4j
@Component
@ServiceBehavior
public class ExternalContactRelationService implements IExternalContactRelationService {

    @SCFClient(lookup = IExternalContactService.SCF_URL)
    private IExternalContactService externalContactService;

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<ExternalContactRelationResp> getAllExternalRelationWithUsers(@NotEmpty final String corpId,
                                                                               @NotNull final Set<String> userIds,
                                                                               @NotEmpty final String externalUserId) throws Exception {
        ExternalContactRelationResp resp = new ExternalContactRelationResp();
        if (CollectionUtils.isEmpty(userIds)) {
            return Result.success(resp);
        }
        HashMap<String, Integer> map = Maps.newHashMap();
        for (String userId : userIds) {
            map.put(userId, ExternalContactRelationStatus.NO_RELATION.getCode());
        }
        GetExternalContactRelationshipReq contactRelationshipReq = new GetExternalContactRelationshipReq();
        contactRelationshipReq.setCorpId(corpId);
        contactRelationshipReq.setUserIds(userIds);
        contactRelationshipReq.setExternalUserId(externalUserId);
        contactRelationshipReq.setPage(1);
        contactRelationshipReq.setSize(userIds.size());

        Result<PagedResult<GetExternalContactRelationshipResp>> externalContactRelationship = externalContactService.getExternalContactRelationship(contactRelationshipReq);

        if (externalContactRelationship.isSuccess()) {
            PagedResult<GetExternalContactRelationshipResp> data = externalContactRelationship.getData();
            if (CollectionUtils.isNotEmpty(data.getResults())) {
                for (GetExternalContactRelationshipResp result : data.getResults()) {
                    if (Objects.equals(result.getStatus(), ExternalContactStatus.ENABLE.getCode())) {
                        Result<JuZiCustomerDetailResp> juZiCustomerDetail = externalContactService.getJuZiCustomerDetail(corpId, result.getUserId(), externalUserId);
                        if (juZiCustomerDetail.isSuccess()) {
                            JuZiCustomerDetailResp juZiCustomer = juZiCustomerDetail.getData();
                            if (StringUtils.isNotBlank(juZiCustomer.getImContactId())) {
                                map.put(result.getUserId(), ExternalContactRelationStatus.ENABLE.getCode());
                            }
                        } else if (Objects.equals(juZiCustomerDetail.getCode(), 400200)) {
                            // 句子删除的code
                            map.put(result.getUserId(), ExternalContactRelationStatus.DELETE_BY_JUZI.getCode());
                        }
                    } else if (Arrays.asList(ExternalContactStatus.DELETE_BY_EXTERNAL_USER.getCode(), ExternalContactStatus.DELETE_BY_USER.getCode())
                            .contains(result.getStatus())) {
                        map.put(result.getUserId(), ExternalContactRelationStatus.DELETED.getCode());
                    }
                }
            }
            HashSet<ExternalContactRelationResp.Item> resSet = Sets.newHashSet();
            for (Map.Entry<String, Integer> entry : map.entrySet()) {
                resSet.add(new ExternalContactRelationResp.Item(entry.getKey(), entry.getValue()));
            }
            resp.setRelations(resSet);
            return Result.success(resp);
        }
        return Result.failure("获取客户关系失败");
    }

}
