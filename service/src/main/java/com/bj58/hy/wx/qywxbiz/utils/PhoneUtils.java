package com.bj58.hy.wx.qywxbiz.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 手机号脱敏工具类
 */
public class PhoneUtils {

    /**
     * 手机号脱敏处理
     * 中间4位数字用*号替换，格式：138****1234
     *
     * @param phone 原始手机号
     * @return 脱敏后的手机号，如果输入为空或格式不正确则返回原值
     */
    public static String maskPhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return phone;
        }

        // 去除空格和特殊字符，只保留数字
        String cleanPhone = phone.replaceAll("[^0-9]", "");

        // 检查是否为11位手机号
        if (cleanPhone.length() != 11) {
            return phone; // 不是标准11位手机号，返回原值
        }

        // 检查是否以1开头（中国大陆手机号格式）
        if (!cleanPhone.startsWith("1")) {
            return phone; // 不是以1开头，返回原值
        }

        // 进行脱敏：前3位 + **** + 后4位
        return cleanPhone.substring(0, 3) + "****" + cleanPhone.substring(7);
    }

    /**
     * 批量手机号脱敏处理
     *
     * @param phones 手机号数组
     * @return 脱敏后的手机号数组
     */
    public static String[] maskPhones(String... phones) {
        if (phones == null) {
            return null;
        }

        String[] maskedPhones = new String[phones.length];
        for (int i = 0; i < phones.length; i++) {
            maskedPhones[i] = maskPhone(phones[i]);
        }
        return maskedPhones;
    }
}
