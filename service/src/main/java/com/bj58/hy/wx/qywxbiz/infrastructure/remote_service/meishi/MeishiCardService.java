package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.meishi.openapi.entity.card.model.UserAndVar;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/15 14:46
 */
@Slf4j
@Component
public class MeishiCardService {

    public String buildContent(Map<String, String> contentMap) {

        if (ObjectUtils.isEmpty(contentMap)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        for (Map.Entry<String, String> entry : contentMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            sb.append(key).append(value)
                    .append("\n\n");
        }

        String content = sb.toString();

        if (StringUtils.isEmpty(content)) {
            return "";
        }

        return escapeString(content);
    }

    public UserAndVar buildCard(String bspId, String title, String content) {
        UserAndVar var = new UserAndVar();
        var.setUserId(bspId);

        Map<String, String> map = new HashMap<>();
        map.put("title", title);
        map.put("content", content);
        var.setVarKeyAndValMap(map);
        return var;
    }

    /**
     * 将变量值中的换行符、制表符和双引号等进行转义
     *
     * @param value 原始的变量值
     * @return 转义后的字符串
     */
    private String escapeString(String value) {
        if (StringUtils.isEmpty(value)) {
            return value;
        }

        // 将原始的变量值JSON化，可以将其中所有的字符转义
        String string = new Gson().toJson(value);
        // 然后去掉开头和结尾的双引号
        return string.substring(1, string.length() - 1);
    }
}
