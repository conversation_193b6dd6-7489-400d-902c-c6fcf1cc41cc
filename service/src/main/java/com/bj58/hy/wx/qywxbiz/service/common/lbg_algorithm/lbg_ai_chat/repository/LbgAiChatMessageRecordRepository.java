package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository;

import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Description: 聊天记录的缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LbgAiChatMessageRecordRepository {

    private final RedissonClient redisson;

    private static final String CHAT_MESSAGES_KEY_TEMPLATE = "AIChatMessages:%s";


    public List<LbgAiMessageChatRecordBo> getChatMessages(@NonNull final String chatId) {
        String key = String.format(CHAT_MESSAGES_KEY_TEMPLATE, chatId);

        RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);

        List<LbgAiMessageChatRecordBo> res = JacksonUtils.parse(bucket.get(), new TypeReference<List<LbgAiMessageChatRecordBo>>() {
        });
        if (ObjectUtils.notEmpty(res)) {
            res.sort(Comparator.comparing(LbgAiMessageChatRecordBo::getDateStamp));
        }

        return res;
    }

    public void save(@NonNull final String chatId,
                     @NonNull final LbgAiMessageChatRecordBo message) {
        RBucket<String> bucket = redisson.getBucket(
                String.format(CHAT_MESSAGES_KEY_TEMPLATE, chatId), StringCodec.INSTANCE);

        List<LbgAiMessageChatRecordBo> res = Lists.newArrayList();
        if (ObjectUtils.notEmpty(bucket.get())) {
            res = JacksonUtils.parse(bucket.get(), new TypeReference<List<LbgAiMessageChatRecordBo>>() {
            });
        }
        res.add(message);
        bucket.set(JacksonUtils.format(res));
        bucket.expire(7, TimeUnit.DAYS);
    }

}
