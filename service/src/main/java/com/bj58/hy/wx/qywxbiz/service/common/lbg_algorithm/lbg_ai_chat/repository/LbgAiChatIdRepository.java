package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository;

import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.bo.LbgAiChatPartnersBo;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Description: 聊天记录的缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LbgAiChatIdRepository {

    private final RedissonClient redisson;

    private static final String CHAT_ID_KEY_TEMPLATE = "AI_CHAT_ID:%s:%s:%s";

    private static final String CHAT_ID_RELATION_KEY_TEMPLATE = "AI_CHAT_ID_RELATION:%s";

    public String getChatId(@NonNull final String corpId,
                            @NonNull final String botUserId,
                            @NonNull final String externalUserId) {
        String key = String.format(CHAT_ID_KEY_TEMPLATE, corpId, botUserId, externalUserId);

        RBucket<String> chatIdBucket = redisson.getBucket(key);
        String chatId = chatIdBucket.get();

        if (StringUtils.isBlank(chatId)) {
            // chatId = UUID.randomUUID().toString(true);
            chatId = String.format("%s|%s", botUserId, externalUserId);

            chatIdBucket.set(chatId);
        }
        chatIdBucket.expire(1, TimeUnit.DAYS);

        this.saveChatPartnersBo(corpId, botUserId, externalUserId, chatId);

        return chatId;
    }

    private void saveChatPartnersBo(@NonNull final String corpId,
                                    @NonNull final String botUserId,
                                    @NonNull final String externalUserId,
                                    @NonNull final String chatId) {

        String key = String.format(CHAT_ID_RELATION_KEY_TEMPLATE, chatId);

        RBucket<String> chatIdRelation = redisson.getBucket(key, StringCodec.INSTANCE);
        LbgAiChatPartnersBo chatIdRelationBo = new LbgAiChatPartnersBo();
        chatIdRelationBo.setChatId(chatId);
        chatIdRelationBo.setCorpId(corpId);
        chatIdRelationBo.setBotUserId(botUserId);
        chatIdRelationBo.setExternalUserId(externalUserId);

        chatIdRelation.set(JacksonUtils.format(chatIdRelationBo));
        chatIdRelation.expire(7, TimeUnit.DAYS);
    }

    public LbgAiChatPartnersBo getChatPartnersBo(@NonNull final String chatId) {
        String key = String.format(CHAT_ID_RELATION_KEY_TEMPLATE, chatId);

        RBucket<String> chatIdRelation = redisson.getBucket(key, StringCodec.INSTANCE);
        if (ObjectUtils.isEmpty(chatIdRelation.get())) {
            return null;
        }

        return JacksonUtils.parse(chatIdRelation.get(), LbgAiChatPartnersBo.class);
    }

}
