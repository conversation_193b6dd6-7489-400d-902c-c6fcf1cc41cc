package com.bj58.hy.wx.qywxbiz.service.bo;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;

import java.util.concurrent.TimeUnit;

@Slf4j
@Getter
public class RateLimiterWrapper {

    private final RRateLimiter limiter;

    private final long rate;

    private final long rateInterval;

    private final RateIntervalUnit rateIntervalUnit;

    public RateLimiterWrapper(final RRateLimiter limiter,
                              final long rate,
                              final long rateInterval,
                              final RateIntervalUnit rateIntervalUnit) {
        this.limiter = limiter;
        this.rate = rate;
        this.rateInterval = rateInterval;
        this.rateIntervalUnit = rateIntervalUnit;
    }

    public String getName() {
        return limiter.getName();
    }

    public boolean couldExecute() {
        boolean b = true;

        try {
            b = limiter.tryAcquire(1, 100, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            // 校对配置不一致时删除的操作 没有加锁，极端情况下这里可能出现异常，忽略即可
            log.error("try acquire error, rate limiter key = {}", limiter.getName(), e);
        }

        return b;
    }
}