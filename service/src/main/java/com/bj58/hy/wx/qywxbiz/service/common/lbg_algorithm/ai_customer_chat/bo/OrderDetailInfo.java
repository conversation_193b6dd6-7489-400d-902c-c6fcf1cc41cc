package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.bo;

import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单详细信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class OrderDetailInfo {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 预约服务时间
     */
    private String serviceStartTime;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 订单创建时间
     */
    private String orderCreateTime;

    /**
     * 是否已分配服务人员
     */
    private Boolean isAssigned = false;

    /**
     * 服务人员信息
     */
    private StaffInfoItem staffInfo;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 三级类目id
     */
    private Integer threeCateId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SCFSerializable
    public static class StaffInfoItem {

        /**
         * 服务人员姓名
         */
        private String staffName;

        /**
         * 性别
         */
        private String gender;

        /**
         * 年龄
         */
        private Integer age;
    }

}