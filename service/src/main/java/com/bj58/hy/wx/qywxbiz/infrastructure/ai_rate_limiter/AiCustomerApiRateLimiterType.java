package com.bj58.hy.wx.qywxbiz.infrastructure.ai_rate_limiter;

import lombok.Data;
import lombok.Getter;
import lombok.NonNull;
import org.redisson.api.RateIntervalUnit;

import java.util.Arrays;
import java.util.List;

@Getter
public enum AiCustomerApiRateLimiterType {
    /**
     * 发送消息
     */
    SEND_MESSAGE("%s:aiCustomerSendMessage",
            300, 30, RateIntervalUnit.SECONDS),


    ;


    AiCustomerApiRateLimiterType(final String rate_limiter_key_prefix_template,
                                 final int rate,
                                 final int rateInterval,
                                 final RateIntervalUnit rateIntervalUnit) {

        this(
                Arrays.asList(
                        new RateRule(
                                rate_limiter_key_prefix_template,
                                rate, rateInterval, rateIntervalUnit
                        )
                )
        );
    }

    private final List<RateRule> rateRules;

    AiCustomerApiRateLimiterType(@NonNull final List<RateRule> rateRules) {
        this.rateRules = rateRules;
    }


    @Data
    static class RateRule {
        private final String rate_limiter_key_prefix_template;

        private final int rate;

        private final long rateInterval;

        private final RateIntervalUnit rateIntervalUnit;
    }
}
