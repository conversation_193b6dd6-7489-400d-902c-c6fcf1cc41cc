package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.strategy;

import com.bj58.hy.fx.bcore.entity.CheckCanReworkOutputDto;
import com.bj58.hy.fx.bcore.entity.neworder.OrderEntity;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo.CheckCanReworkResp;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo.ReworkOrderResp;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.cmcpc.CMCPC;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanOrderQueryService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanReWorkService;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.AbstractQueryDataStrategy;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:19
 */
@Slf4j
@Component
public class CheckCanReworkStrategy extends AbstractQueryDataStrategy {

    @Autowired
    private JingXuanReWorkService jingXuanReWorkService;

    @Autowired
    private JingXuanOrderQueryService jingXuanOrderQueryService;

    @Override
    public boolean matched(@NonNull Object biztypeObj) {

        if (StringUtils.equalsIgnoreCase(biztypeObj.toString(), "checkCanRework")) {
            return true;
        }

        return false;
    }

    @Override
    public Result<Object> process(@NonNull Map<String, Object> queryParam) {

        log.info("checkCanRework : " + JacksonUtils.format(queryParam));

        Object orderIdObj = queryParam.get("orderId");
        String orderId = orderIdObj.toString();

        CheckCanReworkOutputDto checkCanReworkOutputDto = jingXuanReWorkService.checkCanRework(Long.parseLong(orderId));
        log.info("checkCanRework orderId:" + orderId + ",result:" + JacksonUtils.format(checkCanReworkOutputDto));
        if (ObjectUtils.isEmpty(checkCanReworkOutputDto)){
            return Result.failure("checkCanReworkOutputDto is empty");
        }

        CheckCanReworkResp checkCanReworkResp = new CheckCanReworkResp();
        checkCanReworkResp.setCanRework(checkCanReworkOutputDto.getCanRework());
        checkCanReworkResp.setStartTime(checkCanReworkOutputDto.getStartTime());
        checkCanReworkResp.setEndTime(checkCanReworkOutputDto.getEndTime());
        checkCanReworkResp.setReason(checkCanReworkOutputDto.getReason());
        if (checkCanReworkOutputDto.getCanRework() == 1){
            // 查询订单信息
            OrderEntity orderEntity = jingXuanOrderQueryService.query(Long.parseLong(orderId));
            log.info("查询订单信息 orderId:" + orderId + ",result:" + JacksonUtils.format(orderEntity));
            if (ObjectUtils.isEmpty(orderEntity)){
                return Result.failure("orderEntity is empty");
            }

            ReworkOrderResp reworkOrderResp = new ReworkOrderResp();
            reworkOrderResp.setOrderId(orderEntity.getId());
            reworkOrderResp.setPayTime(orderEntity.getPlatPayTime() != null ? DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,orderEntity.getPlatPayTime()) : null);
            reworkOrderResp.setServiceStartTime(orderEntity.getServiceStarttime() != null ? DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,orderEntity.getServiceStarttime()) : null);
            reworkOrderResp.setServiceEndTime(orderEntity.getServiceEndtime() != null ? DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,orderEntity.getServiceEndtime()) : null);
            reworkOrderResp.setCompleteTime(orderEntity.getServiceEndtime() != null ? DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,orderEntity.getServiceEndtime()) : null);
            reworkOrderResp.setServiceId(orderEntity.getServiceId() != null ? orderEntity.getServiceId() : 0);
            reworkOrderResp.setServiceName(StringUtils.isNotEmpty(orderEntity.getServiceName()) ? orderEntity.getServiceName() : "");
            int forthCateId = getForthCateId(orderEntity.getCateFullPath());
            String categoryName = CMCPC.getCategoryNameById(forthCateId);
            reworkOrderResp.setForthCateId(forthCateId);
            reworkOrderResp.setForthCateName(categoryName);
            reworkOrderResp.setCateId(orderEntity.getCateId());
            reworkOrderResp.setCateName(CMCPC.getCategoryNameById(orderEntity.getCateId()));
            checkCanReworkResp.setReworkOrder(reworkOrderResp);
        }

        log.info("CheckCanReworkStrategy req:" + JacksonUtils.format(queryParam) + ",result:" + JacksonUtils.format(checkCanReworkResp));
        return Result.success(JacksonUtils.format(checkCanReworkResp));

    }

    private int getForthCateId(String cateFullPath){
        if (StringUtils.isEmpty(cateFullPath)){
            return 0;
        }

        String[] parts = cateFullPath.split(",");

        return parts.length > 3 ? Integer.parseInt(parts[3]) : 0;
    }

}
