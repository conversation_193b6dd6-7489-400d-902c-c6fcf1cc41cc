package com.bj58.hy.wx.qywxbiz.service.common.contact_way.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 联系方式获取结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserContactWayResult {
    
    /**
     * 可用的用户ID列表
     */
    private List<String> userIds;
    
    /**
     * 是否有好友关系
     * true: 已有好友关系，false: 无好友关系使用轮询
     */
    private Boolean hasFriendRelation;

    /**
     * 外部联系人ID（当有好友关系时返回）
     */
    private String externalUserId;

}