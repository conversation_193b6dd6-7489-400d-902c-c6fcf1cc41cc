package com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.comp;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziChatEventHandler;
import com.bj58.hy.wx.qywxbiz.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.SerializationCodec;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:43
 */
@Slf4j
@Component
public class JuziChatEventThenPublish2SandboxComponent {

    @Autowired
    private RedissonClient redisson;

    public static String TOPIC_KEY = "JuziChatEventThenPublish2Sandbox_Topic";

    private RTopic topic;

    private final String currIp = IpUtils.getIp();

    @Autowired
    private ObjectProvider<AbstractJuziChatEventHandler> juziChatEventHandlers;

    @PostConstruct
    void openReceiving() {
        topic = redisson
                .getTopic(TOPIC_KEY, new SerializationCodec());

        // 仅在沙箱开启
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            return;
        }

        topic.addListener(String.class, (charSequence, str) -> {
            log.info("listener : {}", str);

            JSONObject event = JSONObject.parseObject(str);
            if (ObjectUtils.isEmpty(event)) {
                return;
            }

            ChatMessagesEventBo.Body callbackBo = event.getObject("Body", ChatMessagesEventBo.Body.class);
            if (ObjectUtils.isNull(callbackBo)) {
                return;
            }

            // 是否命中具体ip
            boolean match = isMatch(callbackBo, true);
            if (!match) {
                return;
            }

            for (AbstractJuziChatEventHandler juziEventHandler : juziChatEventHandlers) {
                try {
                    juziEventHandler.process(event);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        });
    }

    public void publish(String str) {
        try {
            topic.publish(str);
            log.info("publish : {}", str);
        } catch (Exception e) {
            log.error("publish error, str = {}", str, e);
        }
    }

    public boolean isMatch(final ChatMessagesEventBo.Body callbackBo,
                           final boolean checkCurrIp) {
        if (ObjectUtils.isNull(callbackBo)) {
            return false;
        }

        if (ObjectUtils.isEmpty(callbackBo.getBotUserId())) {
            return false;
        }

        RMap<String, String> testUserConfig = redisson.getMap(
                "JuziChatEventThenPublish2SandboxConfig", StringCodec.INSTANCE);
        String allowIp = testUserConfig.get(callbackBo.getBotUserId());
        if (ObjectUtils.isEmpty(allowIp)) {
            return false;
        }

        if (checkCurrIp) {
            if (ObjectUtils.isEmpty(currIp)) {
                return false;
            }

            Set<String> collect = Arrays.stream(allowIp.split(";"))
                    .collect(Collectors.toSet());

            return collect.contains(currIp);
        }

        return true;
    }
}
