
package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 *
 */
@Data
public class WorkflowResponse {

    /**
     * workflow 执行 ID
     */
    private String workflowRunId;

    /**
     * 任务 ID，用于请求跟踪和下方的停止响应接口
     */
    private String taskId;

    /**
     * dify中存在workflow_finished节点时返回的data数据
     */
    private JSONObject data;

}
