package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.enums;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum BanjiaOrderStatusEnum {
    /**
     * 线索订单状态枚举
     */
    WAIT_CHECK(8101, "待审核"),
    WAIT_FOLLOW(8107, "待跟进"),
    BAD(8103, "已取消"),
    ORDERED(8102, "待服务"),
    OVER_CLOSE(8301, "已取消"),//担心其他地方可能会用，暂时留着


    /**
     * 支付订单状态枚举
     */
    SERVING(2001, "待服务"),
    FINISHED(9001, "服务完成"),
    REFUND(9003, "已退单"),
    ;

    private final Integer code;
    private final String name;

    public final static Map<Integer, BanjiaOrderStatusEnum> MAP = Arrays.stream(BanjiaOrderStatusEnum.values()).collect(Collectors.toMap(BanjiaOrderStatusEnum::getCode, Function.identity()));

    public static BanjiaOrderStatusEnum getEnumByCode(Integer code) {
        return MAP.get(code);
    }

    public static String getNameByCode(Integer code) {
        BanjiaOrderStatusEnum banjiaOrderStatusEnum = MAP.get(code);
        if (ObjectUtils.isEmpty(banjiaOrderStatusEnum)) {
            return null;
        }
        return banjiaOrderStatusEnum.getName();
    }

}
