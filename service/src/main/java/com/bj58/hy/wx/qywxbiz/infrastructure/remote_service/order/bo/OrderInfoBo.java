package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.bo;

import lombok.Data;

import java.util.Date;

@Data
public class OrderInfoBo {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单创建时间
     */
    private Date orderCreateTime;

    private Integer cateId;

    /**
     * 订单状态
     */
    private String orderStatusName;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 服务开始时间
     */
    private Date serviceStartTime;

    /**
     * 服务完成时间
     */
    private Date serviceCompletionTime;


}