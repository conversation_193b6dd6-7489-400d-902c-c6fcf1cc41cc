package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/31 14:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class CheckCanReworkResp {


    /**
     * 是否能生成客诉返工单
     * 1:可以生成返工单
     * 2:不能生成客诉返工单
     */
    private int canRework;

    /**
     * 客诉返工单可申请开始时间
     */
    private Long startTime;

    /**
     * 客诉返工单可申请结束时间
     */
    private Long endTime;

    /**
     * 不能生成客诉单原因
     */
    private String reason;

    /**
     * 订单信息
     */
    private ReworkOrderResp reworkOrder;
}
