package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.card;

/**
 * <AUTHOR>
 * @date 2024/11/26 10:43
 */
// @Component
public class AbstractMeishiSingleCardHandler /*extends AbstractMeishiMessageHandler */ {

    // @Autowired
    // private RedissonClient redisson;
    //
    // @Autowired
    // private MeishiCardService meishiCardService;
    //
    // @Override
    // public void process(@NonNull MeishiMessageBo bo) {
    //
    //     Map<String, String> contentMap = bo.getContentMap();
    //
    //     try {
    //         // 构建content
    //         String title = bo.getTitle();
    //         String content = meishiCardService.buildContent(contentMap);
    //
    //         //  {
    //         //    "chengtaiqi": "202103301002016aa4c001",
    //         //    "hezhe": "202007071012001213ef30",
    //         //    "wangyanrui": "2020081110120022b787bc"
    //         //  }
    //         RMap<String, String> to = redisson.getMap("MEISHI_SINGLECARD_OA_BSPID", StringCodec.INSTANCE);
    //
    //         if (ObjectUtils.isEmpty(to)) {
    //             log.warn("没有可发送的账号,请添加需要发送的账号");
    //             return;
    //         }
    //
    //         List<UserAndVar> msgList = new ArrayList<>();
    //         for (String value : to.values()) {
    //             msgList.add(meishiCardService.buildCard(value, title, content));
    //         }
    //
    //         SendCardParam param = new SendCardParam();
    //         param.setCardId("ctp_202411166b42b1");
    //         param.setRobotId("MIS_ROBOT_hyqywx");
    //         param.setGroup(false);
    //         param.setUidAndVarList(msgList);
    //
    //         TransJsonData<String> sendResult = MeishiOpenapiCardClient
    //                 .sendCardByCardId(UUID.randomUUID().toString(), param);
    //
    //         log.info("AbstractMeishiSingleCardHandler send result : {}, param：{}", sendResult, JSONObject.toJSONString(param));
    //
    //     } catch (Exception e) {
    //         log.error("AbstractMeishiSingleCardHandler send error, contentMap:{}, param:{}",
    //                 JSONObject.toJSONString(contentMap), JSONObject.toJSONString(bo), e);
    //     }
    // }

}
