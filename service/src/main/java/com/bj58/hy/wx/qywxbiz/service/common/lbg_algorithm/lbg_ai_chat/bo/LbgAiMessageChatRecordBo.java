package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LbgAiMessageChatRecordBo {

    /**
     * 消息的发送时间
     */
    @JSONField(name = "dateStamp") // fastjson
    @JsonProperty("dateStamp")// jackson
    private Long dateStamp;

    /**
     * 消息内容
     */
    @JSONField(name = "message") // fastjson
    @JsonProperty("message")// jackson
    private String message;

    /**
     * 发送人
     */
    @JSONField(name = "sender") // fastjson
    @JsonProperty("sender")// jackson
    private String sender;

    /**
     * 接收人
     */
    @JSONField(name = "receiver") // fastjson
    @JsonProperty("receiver")// jackson
    private String receiver;

    /**
     * 消息id
     */
    @JSONField(name = "messageId") // fastjson
    @JsonProperty("messageId")// jackson
    private String messageId;

    /**
     * 是否为客户发送的消息,true是，false否
     */
    @JSONField(name = "isUserSend") // fastjson
    @JsonProperty("isUserSend")// jackson
    private Boolean isUserSend;
}
