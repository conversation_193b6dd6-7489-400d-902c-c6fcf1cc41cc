package com.bj58.hy.wx.qywxbiz.repository;

import com.bj58.hy.wx.qywxbiz.entity.AccountConfOperatorLogEntity;
import com.bj58.hy.wx.qywxbiz.entity.QAccountConfOperatorLogEntity;
import com.bj58.hy.wx.qywxbiz.repository.jpa.AccountConfOperatorLogJpaRepository;
import com.querydsl.core.QueryResults;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;

@Slf4j
@Component
@RequiredArgsConstructor
public class AccountConfOperatorLogRepository {

    protected final AccountConfOperatorLogJpaRepository repository;

    private final EntityManager entityManager;

    @Transactional
    public void save(AccountConfOperatorLogEntity logEntity) {
        repository.save(logEntity);
    }

    @Transactional(readOnly = true)
    public QueryResults<AccountConfOperatorLogEntity> listOperatorLog(@NonNull final Long accountConfId,
                                                                      @NonNull final Integer pageNum,
                                                                      @NonNull final Integer pageSize) {
        JPAQuery<AccountConfOperatorLogEntity> query = new JPAQueryFactory(entityManager)
                .selectFrom(QAccountConfOperatorLogEntity.accountConfOperatorLogEntity)
                .where(QAccountConfOperatorLogEntity.accountConfOperatorLogEntity.accountConfId.eq(accountConfId))
                .orderBy(QAccountConfOperatorLogEntity.accountConfOperatorLogEntity.createTime.desc())
                .limit(pageSize)
                .offset((long) (pageNum - 1) * pageSize);

        return query.fetchResults();
    }

}
