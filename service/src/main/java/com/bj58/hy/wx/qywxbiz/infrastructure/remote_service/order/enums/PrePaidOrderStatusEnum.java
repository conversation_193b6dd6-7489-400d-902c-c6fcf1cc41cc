package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum PrePaidOrderStatusEnum {

    wait_pay("待支付", 3002),

    success_pay("支付成功", 3003),

    refund("退款", 9009);

    private final String name;
    private final Integer code;

    public final static Map<Integer, PrePaidOrderStatusEnum> MAP = Arrays.stream(PrePaidOrderStatusEnum.values()).collect(Collectors.toMap(PrePaidOrderStatusEnum::getCode, Function.identity()));

    public static PrePaidOrderStatusEnum getEnumByCode(Integer code) {
        return MAP.get(code);
    }

    public static String getNameByCode(Integer code) {
        PrePaidOrderStatusEnum djjxOrderStatusEnum = MAP.get(code);
        if (ObjectUtils.isEmpty(djjxOrderStatusEnum)) {
            return null;
        }
        return djjxOrderStatusEnum.getName();
    }

}
