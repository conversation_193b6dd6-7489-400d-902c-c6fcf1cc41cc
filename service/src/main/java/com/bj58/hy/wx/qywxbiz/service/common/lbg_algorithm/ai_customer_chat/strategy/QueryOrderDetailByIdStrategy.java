package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.strategy;

import com.bj58.hy.fx.bcore.contract.IFxStaffCoreService;
import com.bj58.hy.fx.bcore.entity.FxCoreResponseT;
import com.bj58.hy.fx.bcore.entity.neworder.OrderEntity;
import com.bj58.hy.fx.bcore.entity.staff.StaffInfoEntity;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.cmcpc.CMCPC;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanOrderQueryService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.enums.DjjxOrderStatusEnum;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.AbstractQueryDataStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.bo.OrderDetailInfo;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.bj58.hy.wx.qywxbiz.utils.PhoneUtils;
import com.bj58.spat.cmc.entity.CategoryEntity;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 根据订单ID查询订单详细信息
 */
@Slf4j
@Component
public class QueryOrderDetailByIdStrategy extends AbstractQueryDataStrategy {

    @Autowired
    private JingXuanOrderQueryService jingXuanOrderQueryService;

    @SCFClient(lookup = IFxStaffCoreService.SCF_URL)
    private IFxStaffCoreService fxStaffCoreService;

    @Override
    public boolean matched(@NonNull Object biztypeObj) {
        return StringUtils.equalsIgnoreCase(biztypeObj.toString(), "queryOrderDetailById");
    }

    @Override
    public Result<Object> process(@NonNull Map<String, Object> queryParam) {

        log.info("queryOrderDetailById: {}", JacksonUtils.format(queryParam));

        Object orderIdObj = queryParam.get("orderId");
        if (ObjectUtils.isEmpty(orderIdObj)) {
            return Result.failure("orderId is empty");
        }

        Object externalUserIdObj = queryParam.get("externalUserId");
        if (ObjectUtils.isEmpty(externalUserIdObj)) {
            return Result.failure("externalUserId is empty");
        }

        Long orderId = Long.parseLong(orderIdObj.toString());
        String externalUserId = externalUserIdObj.toString();

        // 查询订单详细信息
        OrderEntity orderEntity = jingXuanOrderQueryService.query(orderId);
        if (ObjectUtils.isEmpty(orderEntity)) {
            return Result.failure("订单不存在");
        }

        // 验证外部联系人是否有权限查看此订单
        if (!validateExternalContactOwnership(externalUserId, orderEntity.getWubaId())) {
            log.info("External contact {} does not have permission to access order {}", externalUserId, orderId);
            return Result.failure("无权限查看此订单");
        }

        OrderDetailInfo orderDetailInfo = new OrderDetailInfo();
        orderDetailInfo.setOrderId(orderEntity.getId());
        orderDetailInfo.setOrderStatus(DjjxOrderStatusEnum.getEnumByCode(orderEntity.getSubOrderState()).getName());
        orderDetailInfo.setServiceStartTime(orderEntity.getServiceStarttime() != null ?
                DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, orderEntity.getServiceStarttime()) : null);
        orderDetailInfo.setServiceName(StringUtils.isNotEmpty(orderEntity.getServiceName()) ?
                orderEntity.getServiceName() : "");
        orderDetailInfo.setPayTime(orderEntity.getPlatPayTime() != null ?
                DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, orderEntity.getPlatPayTime()) : null);
        orderDetailInfo.setOrderCreateTime(orderEntity.getCreateTime() != null ?
                DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, orderEntity.getCreateTime()) : null);

        // 设置联系电话并进行脱敏处理
        orderDetailInfo.setContactPhone(PhoneUtils.maskPhone(orderEntity.getCphone()));
        if (ObjectUtils.isNotEmpty(orderEntity.getCateFullPath())) {
            String[] split = orderEntity.getCateFullPath().split(",");
            if (ObjectUtils.isNotEmpty(split) && split.length > 2) {
                orderDetailInfo.setThreeCateId(Integer.parseInt(split[2]));
            }
        }

        try {
            FxCoreResponseT<StaffInfoEntity> staffInfo = fxStaffCoreService.getStaffToC(orderId);
            if (ObjectUtils.isNotEmpty(staffInfo) && staffInfo.isSuccess() && ObjectUtils.isNotEmpty(staffInfo.getData())) {
                StaffInfoEntity staffInfoEntity = staffInfo.getData();
                String sexName = "";
                if (Objects.equals(staffInfoEntity.getSex(), 1)) {
                    sexName = "男";
                } else if (Objects.equals(staffInfoEntity.getSex(), 0)) {
                    sexName = "女";
                }
                orderDetailInfo.setStaffInfo(new OrderDetailInfo.StaffInfoItem(staffInfoEntity.getName(), sexName, staffInfoEntity.getAge()));
                orderDetailInfo.setIsAssigned(true);
            }
        } catch (Exception e) {
            log.warn("Failed to get staff info for orderId: {}, error: {}", orderId, e.getMessage());
        }

        log.info("QueryOrderDetailByIdStrategy result: {}", JacksonUtils.format(orderDetailInfo));
        return Result.success(JacksonUtils.format(orderDetailInfo));
    }

} 