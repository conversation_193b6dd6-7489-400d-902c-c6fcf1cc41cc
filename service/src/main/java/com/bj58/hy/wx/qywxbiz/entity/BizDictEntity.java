package com.bj58.hy.wx.qywxbiz.entity;

import com.bj58.hy.lib.spring.support.jpa.superclass.Identifiable;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/11/20 11:42
 */
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "t_biz_dict", indexes = {
        @Index(name = "uniq_biz", columnList = "biz_line, biz_scene", unique = true),
})
public class BizDictEntity extends Identifiable {

    /**
     * 业务线
     */
    @Getter
    @Column(name = "biz_line", nullable = false)
    private Integer bizLine;

    /**
     * 业务场景
     */
    @Getter
    @Column(name = "biz_scene", nullable = false)
    private Integer bizScene;

    /**
     * 企业微信id
     */
    @Column(name = "corp_id", nullable = false, length = 18)
    private String corpId;

    /**
     * 规范化的业务名称
     */
    @Getter
    @Column(name = "user_id", nullable = false, length = 64)
    private String bizName;

}
