package com.bj58.hy.wx.qywxbiz.interfaces.wmb.wx_work_event;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb.WmbProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework.AbstractWmbHandler;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractWxWorkEventHandler;
import com.bj58.spat.esbclient.ESBClient;
import com.bj58.spat.esbclient.ESBMessage;
import com.bj58.spat.esbclient.ESBReceiveHandler;
import com.bj58.spat.esbclient.ESBSubject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * Description: 同步 企业微信 事件数据到所有的业务方
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WxWorkEventCallback extends AbstractWmbHandler {

    private ESBClient client;

    private WmbProperties.Client.Publish publish;

    @Autowired
    private ObjectProvider<AbstractWxWorkEventHandler> wxWorkEventHandlers;

    @Override
    protected void init() {
        publish = wmbProperties.getPublish("qywx_event");

        WmbProperties.Client.Subscribe subscribe = wmbProperties.getSubscribe("qywx_event");

        if (Objects.isNull(subscribe)) {
            log.info("No configuration item exists, " + getWarningPrefix() + " wmb client init failure. ");
            return;
        }

        if (!subscribe.isEnabled()) {
            log.info(getWarningPrefix() + " esb subscribe function has not been activated");
            return;
        }

        try {
            client = new ESBClient(wmbProperties.getPath());
            client.setReceiveSubject(new ESBSubject(
                    subscribe.getSubjectId(), subscribe.getClientId()
            ));
            client.setReceiveHandler(new ESBReceiveHandler() {
                @Override
                public void messageReceived(final ESBMessage esbMessage) {
                    handler(esbMessage);
                }
            });

            log.info(getWarningPrefix() + " esb client init success");
        } catch (Exception e) {
            log.error(String.format(getWarningPrefix() + " esb client init failed: %s", e.getMessage()), e);
        }
    }

    public void handler(ESBMessage esbMessage) {
        String esbMsg = new String(esbMessage.getBody(), StandardCharsets.UTF_8);
        handler(esbMsg);
    }


    public void handler(String esbMsg) {
        log.info("receive wx work event callback, body = {}", esbMsg);
        if (ObjectUtils.isEmpty(esbMsg)) {
            return;
        }

        JSONObject event = JSONObject.parseObject(esbMsg);
        if (ObjectUtils.isEmpty(event)) {
            return;
        }

        for (AbstractWxWorkEventHandler wxWorkEventHandler : wxWorkEventHandlers) {
            try {
                if (!wxWorkEventHandler.matched(event)) {
                    continue;
                }

                wxWorkEventHandler.process(event);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }


    @Override
    protected int getWmbSubjectId() {
        return publish.getSubjectId();
    }

    @Override
    protected ESBClient getWmbClient() {
        return this.client;
    }

    @Override
    protected String getWarningPrefix() {
        return "[qywx_event]";
    }
}
