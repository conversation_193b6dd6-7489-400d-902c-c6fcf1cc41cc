package com.bj58.hy.wx.qywxbiz.service.jiafu.dify_ai_workflow;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowRequest;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowResponse;
import com.bj58.hy.wx.qywxbiz.service.common.dify.bo.DifyApiInfoBo;
import lombok.NonNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class JuziChatEventThenDifyJiaFuPrivateChatAiWorkflowHandler extends AbstractJiaFuAiWorkflowHandler {

    private static final String PRIVATE_CHAT_TYPE = "private_chat";
    private static final String HISTORY_KEY_PREFIX = "JiaFuPrivateChatAutoAiReply:History";
    private static final String AI_USER_IDS = "JIAFU_PRIVATE_CHAT_AI_USER_IDS:%s";
    private static final long INTERVAL_SECONDS = 20L; // 设置20秒的消息间隔

    @Override
    public String getMessageType() {
        return PRIVATE_CHAT_TYPE;
    }

    @Override
    protected String getHistoryKeyPrefix() {
        return HISTORY_KEY_PREFIX;
    }

    @Override
    protected boolean shouldProcess(@NonNull ChatMessagesEventBo.Body callbackBo) {
        // 只处理用户发送的私聊消息
        return ObjectUtils.isEmpty(callbackBo.getRoomWecomChatId()) &&
                !Boolean.TRUE.equals(callbackBo.getIsSelf());
    }

    @Override
    protected long getDelaySeconds() {
        return INTERVAL_SECONDS; // 私聊使用20秒延迟
    }

    @Override
    protected boolean beforeSubmitTask(@NonNull ChatMessagesEventBo.Body callbackBo) {
        // 获取私聊记录
        JuziSingleChatContentRecord singleChatContentRecord =
                chatContentRemoteService.getSingleChatContentRecord(callbackBo.getMessageId());

        if (ObjectUtils.isEmpty(singleChatContentRecord)) {
            log.error("not found standard 1v1 chat content record, event body = {}", JacksonUtils.format(callbackBo));
            return false;
        }

        final String corpId = singleChatContentRecord.getCorpId();
        final String userId = singleChatContentRecord.getUserId();
        final String externalUserId = singleChatContentRecord.getExternalUserId();

        // 检查是否为AI启用账号
        if (!isAiUser(corpId, userId)) {
            log.info("current user is not ai user, corp id = {}, user id = {}", corpId, userId);
            return false;
        }

        // 检查是否在允许的时间间隔内
        if (!canSendMessage(corpId, userId, externalUserId)) {
            log.info("message is in cool down period, waiting for interval, corpId={}, userId={}, externalUserId={}",
                    corpId, userId, externalUserId);
            return false;
        }

        // 更新下次可以发送消息的时间
        updateNextAllowedTime(corpId, userId, externalUserId);

        return true;
    }

    /**
     * 判断是否可以发送消息（是否在冷却期）
     */
    private boolean canSendMessage(@NonNull final String corpId,
                                   @NonNull final String userId,
                                   @NonNull final String externalUserId) {
        String key = String.format("JiaFuPrivateChatNextAllowedTime:%s:%s:%s", corpId, userId, externalUserId);
        RAtomicLong rAtomicLong = redisson.getAtomicLong(key);
        if (!rAtomicLong.isExists()) {
            return true;
        }

        return System.currentTimeMillis() >= rAtomicLong.get();
    }

    /**
     * 更新下次允许发送消息的时间
     */
    private void updateNextAllowedTime(@NonNull final String corpId,
                                       @NonNull final String userId,
                                       @NonNull final String externalUserId) {
        String key = String.format("JiaFuPrivateChatNextAllowedTime:%s:%s:%s", corpId, userId, externalUserId);
        RAtomicLong rAtomicLong = redisson.getAtomicLong(key);
        long nextAllowedTime = System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(INTERVAL_SECONDS);
        rAtomicLong.set(nextAllowedTime);
        rAtomicLong.expire(INTERVAL_SECONDS * 2, TimeUnit.SECONDS); // 设置过期时间为间隔的2倍
    }

    /**
     * 判断用户是否为AI用户
     */
    private boolean isAiUser(final String corpId, final String userId) {
        if (ObjectUtils.isEmpty(userId)) {
            return false;
        }

        RSet<String> aiUserIds;
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            aiUserIds = redisson.getSet(String.format(AI_USER_IDS, corpId), StringCodec.INSTANCE);
        } else {
            aiUserIds = redisson.getSet(String.format(AI_USER_IDS + ":SANDBOX", corpId), StringCodec.INSTANCE);
        }

        boolean contains = aiUserIds.contains(userId);

        if (!contains) {
            log.warn("user id not has jiafu private chat ai auth, user id = {}", userId);
        }

        return contains;
    }

    @Override
    protected void execWorkflow(@NonNull final String messageId) {
        execPrivateChatWorkflow(messageId);
    }

    /**
     * 执行私聊工作流处理
     */
    public void execPrivateChatWorkflow(@NonNull final String messageId) {
        // 是否标准化了聊天记录?
        JuziSingleChatContentRecord singleChatContentRecord = chatContentRemoteService.getSingleChatContentRecord(messageId);
        if (ObjectUtils.isEmpty(singleChatContentRecord)) {
            log.error("not found standard private chat content record, message id = {}", messageId);
            return;
        }

        final String corpId = singleChatContentRecord.getCorpId();
        final String userId = singleChatContentRecord.getUserId();
        final String externalUserId = singleChatContentRecord.getExternalUserId();

        // 检查是否为AI启用账号
        if (!isAiUser(corpId, userId)) {
            log.info("current user is not ai user, corp id = {}, user id = {}", corpId, userId);
            return;
        }

        // 先从用户apikey映射表中查询
        RMap<String, String> userApiKeyMap = redisson.getMap("JiaFuPrivateChatAutoAiReplyWorkFlow:UserApiKeyMap", StringCodec.INSTANCE);
        String apiKey = userApiKeyMap.get(userId);

        if (ObjectUtils.isEmpty(apiKey)) { // 这里不应该查不到 api key
            log.error("not found JiaFuPrivateChatAutoAiReplyWorkFlow api key, corp id = {}, user id = {}",
                    corpId, userId);
            return;
        }

        @NonNull final DifyApiInfoBo.Item apiInfo =
                new DifyApiInfoBo.Item("闫风", "f7786c82-0c50-46dc-a468-66a13fa89448", apiKey);

        WorkflowRequest request = new WorkflowRequest();
        String user = String.format("%s:%s:%s", corpId, userId, externalUserId);
        request.setUser(user);

        request.getInputs().put("corpId", corpId);
        request.getInputs().put("userId", userId);
        request.getInputs().put("externalUserId", externalUserId);

        try {
            log.info("request JiaFuPrivateChatAutoAiReplyWorkFlow, req = {}", JacksonUtils.format(request));

            Result<WorkflowResponse> result = difyRemoteService.workflow(
                    apiKey, request);

            log.info("request JiaFuPrivateChatAutoAiReplyWorkFlow done, req = {}, resp = {}",
                    JacksonUtils.format(request), JacksonUtils.format(result));

            if (ObjectUtils.isNull(result) || result.isFailed() || ObjectUtils.isNull(result.getData())) {
                difyWorkflowAlarmComponent.sendTipsToMeiShi_1V1(corpId, userId, apiInfo, result);
            }

        } catch (Exception e) {
            log.error("trigger JiaFuPrivateChatAutoAiReplyWorkFlow exception, workflow request = {}", JacksonUtils.format(request), e);
        }
    }

}