package com.bj58.hy.wx.qywxbiz.service.common.dify.workflow;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziGroupChatContentRecord;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.service.common.dify.workflow.comp.DifyWorkflowInterventionConditionsComponent;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziChatEventHandler;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class JuziChatEventThenDifyWorkflowHandler extends AbstractJuziChatEventHandler {

    @Autowired
    private DifyAsyncWorkflowHandler asyncWorkflowHandler;

    @Autowired
    private DifyWorkflowInterventionConditionsComponent interventionConditionsComponent;

    @Override
    public void process(@NonNull final ChatMessagesEventBo.Body event) {
        @NonNull final String messageId = event.getMessageId();

        if (ObjectUtils.notEmpty(event.getRoomWecomChatId())) { // 群聊

            // 一般的，收到句子回调后会在30s左右才能合并完聊天记录，需要延迟处理
            // 所以无法提前判断 是否标准化了聊天记录

            asyncWorkflowHandler.commitGroupChatTask(messageId);

        } else {
            // 提前判断 是否标准化了聊天记录?
            JuziSingleChatContentRecord singleChatContentRecord = getCurrentSingleChatContentRecord();
            if (ObjectUtils.isNull(singleChatContentRecord)) {
                return;
            }

            // 提前判断 是否是 AI能承接的 客服+外部联系人 聊天
            if (!checkCanBeJoinWorkflow(singleChatContentRecord)) {
                return;
            }

            asyncWorkflowHandler.commit1v1Task(messageId);
        }
    }

    @Override
    public boolean matched(@NonNull final ChatMessagesEventBo.Body event) {
        return true;
    }


    private boolean checkCanBeJoinWorkflow(@NonNull final JuziGroupChatContentRecord groupChatContentRecord) {

        String corpId = groupChatContentRecord.getCorpId();

        return interventionConditionsComponent.canJoin(corpId,
                // 约定的KEY ~
                "_GROUP_");
    }

    private boolean checkCanBeJoinWorkflow(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        String corpId = singleChatContentRecord.getCorpId();
        String botUserId = singleChatContentRecord.getUserId();
        String externalUserId = singleChatContentRecord.getExternalUserId();

        return interventionConditionsComponent.canJoin(corpId, botUserId);
    }
}

