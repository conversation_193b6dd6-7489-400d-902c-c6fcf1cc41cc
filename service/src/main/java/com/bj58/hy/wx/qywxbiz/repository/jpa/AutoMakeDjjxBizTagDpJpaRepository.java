package com.bj58.hy.wx.qywxbiz.repository.jpa;

import com.bj58.hy.wx.qywxbiz.entity.AutoMakeDjjxBizTagDpEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Repository
public interface AutoMakeDjjxBizTagDpJpaRepository
        extends JpaRepository<AutoMakeDjjxBizTagDpEntity, Long> {

    @Transactional(readOnly = true)
    List<AutoMakeDjjxBizTagDpEntity> getByExternalUser58Id(long externalUser58Id);

}
