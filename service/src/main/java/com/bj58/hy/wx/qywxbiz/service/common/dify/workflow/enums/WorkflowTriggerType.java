package com.bj58.hy.wx.qywxbiz.service.common.dify.workflow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WorkflowTriggerType {

    CHAT_1V1(1, "1v1私聊"),
    CHAT_1V1_AGGREGATION_CYCLE(3, "1v1私聊(聚合版本)"),

    CHAT_IN_GROUP(2, "群聊"),
    CHAT_IN_GROUP_AGGREGATION_CYCLE(4, "群聊(聚合版本)"),

    ;

    private final int code;

    private final String desc;

}
