package com.bj58.hy.wx.qywxbiz.entity.enums;

import com.bj58.hy.lib.core.util.ObjectUtils;
import lombok.Getter;

import java.util.Optional;

/**
 * Description: AI类型
 *
 * <AUTHOR>
 */
@Getter
public enum AiType {

    /**
     * LBG算法的AI
     */
    LBG_AI(1),

    /**
     * LBG dify的AI
     */
    DIFY_AI(2),

    /**
     * 未知
     */
    UNKNOWN(-1),
    ;

    private final int code;

    AiType(int code) {
        this.code = code;
    }

    public static AiType of(Integer code) {
        if (ObjectUtils.isNull(code)) {
            return null;
        }


        for (AiType source : AiType.values()) {
            if (source.getCode() == code) {
                return source;
            }
        }

        return null;
    }

    public static AiType strictOf(Integer code) {
        return Optional.ofNullable(of(code))
                .orElseThrow(() -> new UnsupportedOperationException(
                        String.format("unsupported enum, value = %s", code)));
    }
}
