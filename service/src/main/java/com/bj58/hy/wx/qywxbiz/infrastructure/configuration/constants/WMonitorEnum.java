package com.bj58.hy.wx.qywxbiz.infrastructure.configuration.constants;

import lombok.Getter;

@Getter
public enum WMonitorEnum {

    AI_SEND_MESSAGE_ERROR_BECAUSE_OUT_OF_LIMIT(100001, "AI客服发送消息失败[频率限制]"),

    // =====> 到家精选，离线打标签 相关 <=====
    DAO_JIA_JING_XUAN_AUTO_MAKE_BIZ_TAG_NOT_FOUND_TAG_GROUP(124655, "到家精选离线打标签未找到标签组"),
    DAO_JIA_JING_XUAN_AUTO_MAKE_BIZ_TAG_NOT_FOUND_TAG(124656, "到家精选离线打标签未找到标签"),


    RECORD_SINGLE_CHAT_CONTENT_BIZ_INFO_ERROR(129702, "记录会话内容BIZ信息失败"),

    ;


    private final int attribute;

    private final String desc;

    WMonitorEnum(int attribute, String desc) {
        this.attribute = attribute;
        this.desc = desc;
    }
}
