package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bj58.hy.fx.bcore.entity.ReworkOrderOutputDto;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo.ReworkOrderResp;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.ai_customer.config.AiCustomerProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanReWorkService;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.AbstractQueryDataStrategy;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:19
 */
@Slf4j
@Component
public class GetCanReworkOrderListByUidStrategy extends AbstractQueryDataStrategy {


    @Autowired
    private ExternalContactRemoteService externalContactService;

    @Autowired
    private AiCustomerProperties aiCustomerProperties;

    @Autowired
    private JingXuanReWorkService jingXuanReWorkService;


    @Override
    public boolean matched(@NonNull Object biztypeObj) {

        if (StringUtils.equalsIgnoreCase(biztypeObj.toString(), "getCanReworkOrderListByUid")) {
            return true;
        }

        return false;
    }

    @Override
    public Result<Object> process(@NonNull Map<String, Object> queryParam) {

        log.info("getCanReworkOrderListByUid参数 : " + JacksonUtils.format(queryParam));

        Object externalUserIdObj = queryParam.get("externalUserId");
        String externalUserId = externalUserIdObj.toString();


        // 外部联系人转userId
        Long externalUser58Id = externalContactService.get58IdByExternalUserId(aiCustomerProperties.getCorpId(), externalUserId);
        if (ObjectUtils.isEmpty(externalUser58Id)){
            return Result.failure("externalUser58Id is empty");
        }

        List<ReworkOrderOutputDto> canReworkOrderList = jingXuanReWorkService.getCanReworkOrderByUid(externalUser58Id);
        log.info("checkCanRework externalUser58Id:" + externalUser58Id + ",result:" + JacksonUtils.format(canReworkOrderList));
        if (ObjectUtils.isEmpty(canReworkOrderList)) {
            return Result.failure("canReworkOrderList is empty");
        }

        List<ReworkOrderResp> reworkOrderRespList = canReworkOrderList.stream().map(dto -> {
            ReworkOrderResp reworkOrderResp = new ReworkOrderResp();
            reworkOrderResp.setOrderId(dto.getOrderId());
            reworkOrderResp.setPayTime(dto.getPayTime() != null ? DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,new Date(dto.getPayTime())) : "");
            reworkOrderResp.setServiceStartTime(dto.getServiceStartTime() != null ? DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,new Date(dto.getServiceStartTime())) : "");
            reworkOrderResp.setCompleteTime(dto.getCompleteTime() != null ? DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,new Date(dto.getCompleteTime())) : "");
            reworkOrderResp.setServiceEndTime(dto.getCompleteTime() != null ? DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss,new Date(dto.getCompleteTime())) : "");
            reworkOrderResp.setServiceId(dto.getServiceId());
            reworkOrderResp.setServiceName(dto.getServiceName());
            reworkOrderResp.setForthCateId(dto.getForthCateId());
            reworkOrderResp.setForthCateName(dto.getForthCateName());
            reworkOrderResp.setCateId(dto.getForthCateId());
            reworkOrderResp.setCateName(dto.getForthCateName());
            return reworkOrderResp;

        }).collect(Collectors.toList());

        log.info("GetCanReworkOrderListByUidStrategy req:" + JacksonUtils.format(queryParam) + ",result:" + JacksonUtils.format(reworkOrderRespList));
        if (CollectionUtils.isEmpty(reworkOrderRespList)){
            return Result.success(new JSONArray());
        }

        return Result.success(JSON.toJSONString(reworkOrderRespList));
    }
}
