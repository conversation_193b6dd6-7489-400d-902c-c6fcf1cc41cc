package com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.enums.AccountHostingEventTypeEnum;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.AccountHostingBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.AbstractMeishiMessageHandler;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.MeishiMessageBo;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziEventHandler;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.enums.JuziAccountAlarmEnum;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/11/15 10:13
 */
@Component
public class AccountHostingMonitorEventHandler extends AbstractJuziEventHandler {

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private ObjectProvider<AbstractMeishiMessageHandler> meishiMessageHandlers;

    @Override
    public void process(@NonNull JSONObject event) {

        AccountHostingBo eventBo = JSON.toJavaObject(event, AccountHostingBo.class);

        AccountHostingEventTypeEnum hostingType = AccountHostingEventTypeEnum.getEnum(
                eventBo.getBody().getEventType()
        );
        if (ObjectUtils.isNull(hostingType)) {
            return; // 不会发生
        }

        MeishiMessageBo meishiBo = buildMeishiMessageBo(hostingType, eventBo);

        for (AbstractMeishiMessageHandler meishiMessageHandler : meishiMessageHandlers) {
            try {
                meishiMessageHandler.process(meishiBo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        RBucket<String> limitBucket = redisson.getBucket(
                String.format("AccountHostingMonitor_%s_%s_%s",
                        eventBo.getEvent(),
                        eventBo.getBody().getBot().getWecomUserId(),
                        eventBo.getBody().getEventType()),
                StringCodec.INSTANCE);

        RBucket<Long> bucket = redisson.getBucket("AccountHostingMonitorFreq", LongCodec.INSTANCE);
        Long freq = bucket.get();
        if (ObjectUtils.isNull(freq)) {
            freq = 10L;
        }
        if (freq < 0) {
            freq = 10L;
        }

        limitBucket.set(String.format("%s_%s_%s",
                        System.currentTimeMillis(),
                        eventBo.getBody().getBot().getWecomUserId(),
                        hostingType),
                freq, TimeUnit.MINUTES);
    }

    @NotNull
    private MeishiMessageBo buildMeishiMessageBo(final AccountHostingEventTypeEnum hostingType, final AccountHostingBo eventBo) {
        Map<String, String> contentMap = new LinkedHashMap<>();

        contentMap.put("通知内容：", hostingType.getDesc());

        if (AccountHostingEventTypeEnum.托管账号离线.equals(hostingType) &&
                StringUtils.isNotEmpty(eventBo.getBody().getLogoutReason())) {
            contentMap.put("账号离线原因：", eventBo.getBody().getLogoutReason());
        }

        contentMap.put("托管账号企业微信员工id：", eventBo.getBody().getBot().getWecomUserId());
        contentMap.put("托管账号名称：", eventBo.getBody().getBot().getName());
        contentMap.put("小组名称：", eventBo.getBody().getBot().getGroup().getGroupName());

        // 3.推送美事消息
        MeishiMessageBo meishiBo = new MeishiMessageBo();
        meishiBo.setContentMap(contentMap);
        meishiBo.setTitle(JuziAccountAlarmEnum.账号托管事件回调.alarmTitle);

        // 判断是否需要 发送到特定的美事群里
        RMap<String, String> rMap;
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            rMap = redisson.getMap("ALARM_MEISHI_GROUP_ID", StringCodec.INSTANCE);
        } else {
            rMap = redisson.getMap("ALARM_MEISHI_GROUP_ID_SANDBOX", StringCodec.INSTANCE);
        }

        for (Map.Entry<String, String> entry : contentMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            String ruleStr = key + value;
            String specialGroupId = rMap.get(ruleStr);

            if (ObjectUtils.notEmpty(specialGroupId)) {
                log.info("发现特定的告警群组，规则 = {}, 对应的群id = {}", ruleStr, specialGroupId);
                meishiBo.getSpecialGroupIds().add(specialGroupId); // set, 不需要手动去重
            }
        }

        return meishiBo;
    }

    @Override
    public boolean matched(@NonNull JSONObject event) {
        if (!"Product".equals(System.getenv("WCloud_Env"))) {
            return false; // 沙箱不需要
        }

        if (!JuziAccountAlarmEnum.matched(event, JuziAccountAlarmEnum.账号托管事件回调.getEvent())) {
            return false;
        }

        AccountHostingBo eventBo = JSON.toJavaObject(event, AccountHostingBo.class);
        if (eventBo == null) {
            return false;
        }

        // 具体事件过滤非报警信息 需要自定义 因为每个事件需要报警的信息不一样
        int hostingTypeCode = eventBo.getBody().getEventType();

        boolean b = hostingTypeCode == AccountHostingEventTypeEnum.托管账号离线.getType()
                || hostingTypeCode == AccountHostingEventTypeEnum.托管账号被删除.getType();

        if (!b) {
            return false;
        }

        if (isFiltered(eventBo) || isFreqLimited(eventBo)) {
            return false;
        }

        return true;
    }


    private boolean isFiltered(@NonNull AccountHostingBo eventBo) {
        if (ObjectUtils.isNull(eventBo.getBody())
                || ObjectUtils.isNull(eventBo.getBody().getBot())
                || ObjectUtils.isEmpty(eventBo.getBody().getBot().getWecomUserId())
                || ObjectUtils.isNull(eventBo.getBody().getBot().getGroup())
                || ObjectUtils.isEmpty(eventBo.getBody().getBot().getGroup().getGroupId())
        ) {
            return true;
        }

        // 小组过滤
        RMap<String, String> filterGroups =
                redisson.getMap("ACCOUNT_HOSTING_EVENT_ALARM_IGNORE_GROUPS", StringCodec.INSTANCE);

        if (ObjectUtils.notEmpty(filterGroups)) {
            boolean contains = filterGroups.values().stream()
                    .anyMatch(value -> value.equals(eventBo.getBody().getBot().getGroup().getGroupId()));
            if (contains) {
                return true;
            }
        }

        final String wecomUserId = eventBo.getBody().getBot().getWecomUserId();

        // 待托管的账号没有wecomUserId 不报警
        if (ObjectUtils.isEmpty(wecomUserId)) {
            return true;
        }

        // 账号过滤
        RSet<String> filterUsers =
                redisson.getSet("ACCOUNT_HOSTING_EVENT_ALARM_IGNORE_USERS", StringCodec.INSTANCE);
        if (ObjectUtils.notEmpty(filterUsers)) {
            if (filterUsers.contains(wecomUserId)) {
                return true;
            }
        }

        // 获取配置，查询是否需要禁止提醒
        AccountHostingEventTypeEnum eventType = AccountHostingEventTypeEnum.getEnum(
                eventBo.getBody().getEventType()
        );
        if (ObjectUtils.isNull(eventType)) {
            return true; // 不会发生
        }

        try {
            RBucket<Long> config = redisson.getBucket(String.format("AccountHostingMonitor:%s:%s",
                    wecomUserId, eventType.getType()), LongCodec.INSTANCE);
            Long timestamp = config.get();

            if (ObjectUtils.isEmpty(timestamp)) {
                return false; // 没有配置

            } else {
                // 查看配置的截止时间
                if (timestamp > System.currentTimeMillis()) {
                    log.info("当前客服对应类型告警禁用规则启用中，corp user id = {}, alarm type = {}",
                            wecomUserId, eventType);
                    return true;

                } else {
                    log.info("当前客服对应类型告警禁用规则已失效，corp user id = {}, alarm type = {}",
                            wecomUserId, eventType);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("判断客服对应告警类型是否开启时发生错误, event = {}", JacksonUtils.format(eventBo), e);
        }

        return false;

    }

    private boolean isFreqLimited(@NonNull AccountHostingBo eventBo) {
        if (ObjectUtils.isNull(eventBo.getBody())
                || ObjectUtils.isNull(eventBo.getBody().getBot())
                || ObjectUtils.isEmpty(eventBo.getBody().getBot().getWecomUserId())
                || ObjectUtils.isNull(eventBo.getBody().getBot().getGroup())
                || ObjectUtils.isEmpty(eventBo.getBody().getBot().getGroup().getGroupId())
        ) {
            return true;
        }

        RBucket<String> limitBucket = redisson.getBucket(
                String.format("AccountHostingMonitor_%s_%s_%s",
                        eventBo.getEvent(),
                        eventBo.getBody().getBot().getWecomUserId(),
                        eventBo.getBody().getEventType()),
                StringCodec.INSTANCE);

        String value = limitBucket.get();

        if (ObjectUtils.notEmpty(value)) {
            log.warn("当前告警频繁触发已被忽略, event = {}", JacksonUtils.format(eventBo));
            return true;
        }

        return false;

    }


}
