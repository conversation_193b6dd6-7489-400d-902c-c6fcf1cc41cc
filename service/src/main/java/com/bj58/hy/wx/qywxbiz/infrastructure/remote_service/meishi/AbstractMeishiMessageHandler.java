package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi;

import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.MeishiMessageBo;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2024/11/26 10:41
 */
public abstract class AbstractMeishiMessageHandler {

    protected org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(this.getClass());

    public abstract void process(@NonNull final MeishiMessageBo bo);

}
