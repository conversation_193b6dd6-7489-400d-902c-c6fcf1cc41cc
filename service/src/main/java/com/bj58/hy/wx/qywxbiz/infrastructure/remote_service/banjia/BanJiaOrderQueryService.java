package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.banjia;

import com.bj58.lbg.daojia.fxbanjia.contract.IMovingOrderService;
import com.bj58.lbg.daojia.fxbanjia.pojo.dto.OrderQueryDTO;
import com.bj58.lbg.daojia.fxbanjia.pojo.vo.BanjiaBaseResult;
import com.bj58.lbg.daojia.fxbanjia.pojo.vo.order.OrderCsCDetailVO;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BanJiaOrderQueryService {

    @SCFClient(lookup = IMovingOrderService.SCF_URL)
    private IMovingOrderService orderQueryService;

    /**
     * 根据订单id获取搬家订单信息
     *
     * @param orderId
     */
    public OrderCsCDetailVO query(Long orderId) {

        try {
            OrderQueryDTO queryDTO = new OrderQueryDTO();
            queryDTO.setPayOrderId(orderId);
            BanjiaBaseResult<OrderCsCDetailVO> orderDetail2 = orderQueryService.getOrderDetail(queryDTO);
            if (orderDetail2 != null && orderDetail2.getData() != null) {
                return orderDetail2.getData();
            }
        } catch (Exception e) {
            log.error("BanJiaOrderQueryService query error", e);
        }

        return null;
    }

}
