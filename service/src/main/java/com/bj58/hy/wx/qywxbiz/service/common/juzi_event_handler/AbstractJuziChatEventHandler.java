package com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziGroupChatContentRecord;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ChatContentRemoteService;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.comp.JuziChatEventThenPublish2SandboxComponent;
import lombok.NonNull;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public abstract class AbstractJuziChatEventHandler extends AbstractJuziEventHandler {

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private RedisLockSupport lockSupport;

    @Autowired
    private ChatContentRemoteService chatContentRemoteService;

    @Autowired
    private JuziChatEventThenPublish2SandboxComponent juziChatEventThenPublish2SandboxComponent;

    private final ThreadLocal<Map<String, Object>> juziChatContentRecordCache =
            ThreadLocal.withInitial(HashMap::new);

    public void process(@NonNull final JSONObject event) {
        ChatMessagesEventBo.Body callbackBo = event.getObject("Body", ChatMessagesEventBo.Body.class);
        if (ObjectUtils.isNull(callbackBo)) {
            return;
        }

        // 获取当前执行的类
        Class<? extends AbstractJuziChatEventHandler> clz = this.getClass();
        final String name = clz.getSimpleName();

        lockSupport.executeWithoutResult(
                String.format("JuziChatEvent:%s", callbackBo.getChatId()),
                () -> {
                    @NonNull final String messageId = callbackBo.getMessageId();

                    try {
                        if (ObjectUtils.notEmpty(callbackBo.getRoomWecomChatId())) {
                            // 是否已经处理过？
                            RBucket<String> hasBeenProcessBucket = redisson.getBucket(
                                    String.format("JuziChatEvent:%s:%s:History", messageId, name));
                            boolean hasBeenProcess = false;
                            String prev = hasBeenProcessBucket.get();
                            if (ObjectUtils.notEmpty(prev)) {
                                // 判断当前 roomWxChatId是否处理过？
                                if (prev.contains(callbackBo.getRoomWecomChatId())) {
                                    hasBeenProcess = true;
                                }
                            }

                            if (hasBeenProcess) {
                                log.warn("curr event has been process, event = {}", JacksonUtils.format(event));
                                return;
                            }

                            String newVal = ObjectUtils.isEmpty(prev) ?
                                    callbackBo.getRoomWecomChatId() :
                                    prev + "," + callbackBo.getRoomWecomChatId();

                            hasBeenProcessBucket.set(newVal, 3, TimeUnit.DAYS);

                            // 一般的，收到句子回调后会在30s左右才能合并完聊天记录，需要延迟处理


                        } else {
                            // 是否已经处理过？
                            RBucket<String> hasBeenProcessBucket = redisson.getBucket(
                                    String.format("JuziChatEvent:%s:%s:History", messageId, name));
                            if (hasBeenProcessBucket.isExists()) {
                                log.warn("curr event has been process, event = {}", JacksonUtils.format(event));
                                return;
                            }

                            hasBeenProcessBucket.set("1", 3, TimeUnit.DAYS);

                            // 判断是否当前线程已提前缓存了聊天记录
                            JuziSingleChatContentRecord singleChatContentRecord = getCurrentSingleChatContentRecord();
                            if (ObjectUtils.isNull(singleChatContentRecord)) {
                                // 提前判断 是否标准化了聊天记录?
                                singleChatContentRecord = chatContentRemoteService.getSingleChatContentRecord(messageId);
                                if (ObjectUtils.isNull(singleChatContentRecord)) {
                                    log.error("not found standard 1v1 chat content record, event body = {}", JacksonUtils.format(event));
                                    return;
                                }
                                setCurrentSingleChatContentRecord(singleChatContentRecord);
                            }
                        }

                        process(callbackBo);

                    } finally {
                        juziChatContentRecordCache.remove();
                    }
                });
    }

    public abstract void process(@NonNull final ChatMessagesEventBo.Body event);

    public boolean matched(@NonNull final JSONObject event) {
        if (!StringUtils.equalsIgnoreCase(event.getString("Event"), "聊天消息回调")) {
            return false;
        }

        ChatMessagesEventBo.Body callbackBo = event.getObject("Body", ChatMessagesEventBo.Body.class);
        if (ObjectUtils.isNull(callbackBo)) {
            return false;
        }


        Date timestamp = callbackBo.getStandardTimestamp();
        if (ObjectUtils.isNull(timestamp)) {
            log.error("not found standard timestamp, event body = {}", JacksonUtils.format(callbackBo));
            return false;
        }

        // 托管账号掉线后重新登录，句子互动会推送近期的聊天记录（真垃呀~）
        if (System.currentTimeMillis() - timestamp.getTime() > 20 * 60 * 1000) {
            log.error("found historical event, event body = {}", JacksonUtils.format(callbackBo));
            return false;
        }

        if (juziChatEventThenPublish2SandboxComponent.isMatch(callbackBo, false)) {
            return false;
        }

        return matched(callbackBo);
    }

    public abstract boolean matched(@NonNull final ChatMessagesEventBo.Body event);

    protected void setCurrentSingleChatContentRecord(@NonNull final JuziSingleChatContentRecord record) {
        Map<String, Object> map = juziChatContentRecordCache.get();
        map.put("1v1", record);
    }

    protected JuziSingleChatContentRecord getCurrentSingleChatContentRecord() {
        Map<String, Object> map = juziChatContentRecordCache.get();
        if (ObjectUtils.isEmpty(map)) {
            return null;
        }
        return (JuziSingleChatContentRecord) map.get("1v1");
    }

    protected void setCurrentGroupChatContentRecord(final JuziGroupChatContentRecord record) {
        Map<String, Object> map = juziChatContentRecordCache.get();
        map.put("group", record);
    }

    protected JuziGroupChatContentRecord getCurrentGroupChatContentRecord() {
        Map<String, Object> map = juziChatContentRecordCache.get();
        if (ObjectUtils.isEmpty(map)) {
            return null;
        }
        return (JuziGroupChatContentRecord) map.get("group");
    }

}
