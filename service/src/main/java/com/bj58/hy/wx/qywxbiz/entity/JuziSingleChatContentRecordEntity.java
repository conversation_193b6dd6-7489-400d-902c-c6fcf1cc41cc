package com.bj58.hy.wx.qywxbiz.entity;

import com.bj58.hy.lib.spring.support.jpa.superclass.Identifiable;
import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.bj58.hy.wx.qywxbiz.entity.converter.SingleChatSendByConverter;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import java.util.Date;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Getter
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "t_juzi_single_chat_content_record", indexes = {
        @Index(name = "uniq_message", columnList = "message_id", unique = true),
        @Index(name = "idx_corp_user_external_user", columnList = "user_id, external_user_id"),
        @Index(name = "idx_external_user_corp_user", columnList = "external_user_id, user_id"),
})
public class JuziSingleChatContentRecordEntity extends Identifiable {

    /**
     * 企业微信id
     */
    @Column(name = "corp_id", nullable = false, length = 18)
    private String corpId;

    /**
     * 消息的唯一标识
     */
    @Column(name = "message_id", nullable = false, length = 64)
    private String messageId;

    /**
     * 企业成员id
     */
    @Column(name = "user_id", nullable = false, length = 64)
    private String userId;

    /**
     * 外部联系人id
     */
    @Column(name = "external_user_id", nullable = false, length = 32)
    private String externalUserId;

    /**
     * 私聊时，消息发送人 的类型
     */
    @Column(name = "send_by", nullable = false)
    @Convert(converter = SingleChatSendByConverter.class)
    private SingleChatSendBy sendBy;

    /**
     * 消息来源
     * 0：手机推送过来的消息
     * 1：小组级控制台手动发送消息
     * 2：群发
     * 3：自动回复
     * 4：创建群聊
     * 5：其他机器人回复
     * 6：api发消息
     * 7：sop功能
     */
    @Column(name = "send_source", nullable = true)
    private Integer sendSource;


    /**
     * 消息类型
     */
    @Column(name = "message_type", nullable = false)
    private Integer messageType;

    /**
     * 消息体
     */
    @Setter
    @Lob
    @Column(name = "payload")
    private String payload;

    /**
     * 业务扩展参数
     */
    @Setter
    @Lob
    @Column(name = "biz_info")
    private String bizInfo;


    @Column(name = "create_time", nullable = false)
    private Date createTime;

}
