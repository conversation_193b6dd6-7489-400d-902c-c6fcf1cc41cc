package com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.enums;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/11/15 15:42
 */
@Getter
@AllArgsConstructor
public enum JuziAccountAlarmEnum {

    // 账号托管相关 ======>
    账号托管事件回调("账号托管事件回调", "企微句子账号托管异常通知"),

    托管账号被限制回调("托管账号被限制回调", "企微句子托管账号被限制通知"),
    ;

    public final String event;

    public final String alarmTitle;


    public static boolean matched(JSONObject json, String targetEvent) {
        String event = json.getString("Event");

        if (ObjectUtils.notNull(event)) {
            JuziAccountAlarmEnum juziAccountAlarmEnum = getEnumByJson(json);
            if (juziAccountAlarmEnum != null
                    && juziAccountAlarmEnum.getEvent().equals(targetEvent)) {
                return true;
            }
        }
        return false;
    }


    public static JuziAccountAlarmEnum getEnumByJson(JSONObject json) {
        String event = json.getString("Event");

        if (ObjectUtils.notNull(event)) {
            return getEnumByParam(event);
        }

        return null;
    }

    public static JuziAccountAlarmEnum getEnumByParam(String event) {
        for (JuziAccountAlarmEnum item : JuziAccountAlarmEnum.values()) {
            if (item.getEvent().equalsIgnoreCase(event)) {
                return item;
            }
        }

        return null;
    }
}
