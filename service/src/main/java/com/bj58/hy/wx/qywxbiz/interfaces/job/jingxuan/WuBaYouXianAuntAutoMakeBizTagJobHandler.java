package com.bj58.hy.wx.qywxbiz.interfaces.job.jingxuan;

import cn.hutool.core.thread.BlockPolicy;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.core.util.ValidationUtils;
import com.bj58.hy.wx.qywx.contract.ICorpCustomerTagService;
import com.bj58.hy.wx.qywx.contract.IExternalContactService;
import com.bj58.hy.wx.qywx.contract.IStaffService;
import com.bj58.hy.wx.qywx.contract.dto.corp_tag.CorpCustomerTagResp;
import com.bj58.hy.wx.qywx.contract.dto.corp_tag.GetCorpCustomerTagsReq;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.ListResp;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.UpdateExternalContactTagsReq;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.enums.ExternalContactStatus;
import com.bj58.hy.wx.qywxbiz.entity.AutoMakeAuntBizTagDpEntity;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.cmcpc.CMCPC;
import com.bj58.hy.wx.qywxbiz.repository.jpa.AutoMakeAuntBizTagDpJpaRepository;
import com.bj58.job.core.biz.model.ReturnT;
import com.bj58.job.core.handler.IJobHandler;
import com.bj58.job.core.handler.annotation.JobHandler;
import com.bj58.job.core.log.WJobLogger;
import com.bj58.spat.cmc.entity.CategoryEntity;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotEmpty;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * https://ee.58corp.com/base2/w/items/GRD-103
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@JobHandler(value = "WuBaYouXianAuntAutoMakeBizTagJobHandler")
public class WuBaYouXianAuntAutoMakeBizTagJobHandler extends IJobHandler {

    @SCFClient(lookup = ICorpCustomerTagService.SCF_URL)
    private ICorpCustomerTagService corpCustomerTagService;

    @SCFClient(lookup = IStaffService.SCF_URL)
    private IStaffService corpUserComponent;

    @SCFClient(lookup = IExternalContactService.SCF_URL)
    private IExternalContactService externalContactService;

    @Autowired
    private AutoMakeAuntBizTagDpJpaRepository autoMakeAuntBizTagDpJpaRepository;

    private final Executor executor = new ThreadPoolExecutor(
            // 这里的并发并不能给的太大, 企业微信有频次限制
            0, 2,
            1, TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("auto_make_aunt_biz_tag-%d").build(),
            new BlockPolicy(Runnable::run)
    );


    @Autowired
    private RedissonClient redisson;

    private RRateLimiter updateExternalContactTagsRateLimiter;

    private RRateLimiter fetchExternalContactsRateLimiter;

    @PostConstruct
    public void init() {
        updateExternalContactTagsRateLimiter = redisson.getRateLimiter("HYQYWX:updateExternalContactTags");
        updateExternalContactTagsRateLimiter.setRate(RateType.OVERALL, 10, 1, RateIntervalUnit.SECONDS);

        fetchExternalContactsRateLimiter = redisson.getRateLimiter("FetchExternalContacts");
        fetchExternalContactsRateLimiter.setRate(RateType.OVERALL, 10, 1, RateIntervalUnit.SECONDS);
    }

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }
        JSONObject jobParam = JSONObject.parseObject(param);
        if (ObjectUtils.isEmpty(jobParam)) {
            return ReturnT.FAIL;
        }

        String corpId = jobParam.getString("corpId");
        String departmentIdsStr = jobParam.getString("departmentIds");
        List<Integer> departmentIds = JSONObject.parseArray(departmentIdsStr, Integer.class);
        if (CollectionUtils.isEmpty(departmentIds) || StringUtils.isBlank(corpId)) {
            return ReturnT.FAIL;
        }

        AutoMakeBizTagConfigInfo autoMakeBizTagConfigInfo = new AutoMakeBizTagConfigInfo(
                corpId,
                new HashSet<>(departmentIds)
        );

        try {
            autoMakeBizTag(autoMakeBizTagConfigInfo);

        } catch (Exception e) {
            String logInfo = String.format("auto make biz tag error, ex msg = %s", e.getMessage());
            log.error(logInfo, e);

            WJobLogger.log(logInfo);
            WJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    private static final List<Integer> ALLOW_STATUS = Arrays.asList(ExternalContactStatus.ENABLE.getCode(), ExternalContactStatus.DELETE_BY_EXTERNAL_USER.getCode());


    private void autoMakeBizTag(@NonNull final AutoMakeBizTagConfigInfo autoMakeBizTagConfigInfo) throws Exception {
        // 1. 获取所有的本次要打的标签的元数据
        final Map<String, String> allTagsInfo = getAutoMakeBizTagInfo(autoMakeBizTagConfigInfo);

        String logInfo = String.format("found auto make biz tags map, info = %s", JacksonUtils.format(allTagsInfo));
        log.info(logInfo);
        WJobLogger.log(logInfo);

        if (ObjectUtils.isEmpty(allTagsInfo)) {
            return;
        }

        // 2. 获取对应部门下，所有的企业员工
        final Set<String> corpUserIds = getAutoMakeBizTagCorpUserIds(autoMakeBizTagConfigInfo);

        logInfo = String.format("found %s corp users that can be tagged, user ids = %s",
                corpUserIds.size(), JacksonUtils.format(corpUserIds));
        log.info(logInfo);
        WJobLogger.log(logInfo);

        if (ObjectUtils.isEmpty(corpUserIds)) {
            return;
        }


        int limit = 1000;

        // 3. 获取所有企业员工的 状态正常，可以打标签的 外部联系人
        for (String corpUserId : corpUserIds) {
            String cursor = null;
            Result<ListResp> listRespResult =
                    externalContactService.listByUserId(autoMakeBizTagConfigInfo.getCorpId(), corpUserId, cursor, limit);
            List<ListResp.Item> externalContactEntities = Lists.newArrayList();

            while (Objects.nonNull(listRespResult) &&
                    Objects.nonNull(listRespResult.getData()) &&
                    ObjectUtils.notEmpty(listRespResult.getData().getList()) &&
                    ObjectUtils.notEmpty((listRespResult.getData().getNextCursor()))
            ) {
                cursor = listRespResult.getData().getNextCursor();
                ListResp listRespResultData = listRespResult.getData();
                externalContactEntities.addAll(listRespResultData.getList());

                fetchExternalContactsRateLimiter.acquire();
                listRespResult = externalContactService.listByUserId(autoMakeBizTagConfigInfo.getCorpId(), corpUserId, cursor, limit);
            }

            if (ObjectUtils.isEmpty(externalContactEntities)) {
                continue;
            }

            for (ListResp.Item externalContactEntity : externalContactEntities) {

                if (!ALLOW_STATUS.contains(externalContactEntity.getStatus())) {
                    continue;
                }

                AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo = new AutoMakeBizTagExternalContactInfo(
                        autoMakeBizTagConfigInfo.getCorpId(),
                        externalContactEntity.getUserId(),
                        externalContactEntity.getExternalUserId(),
                        externalContactEntity.getExternalUser58Id()
                );

                // 4. 自动打标签（打标签的时候可能因为用户的并发操作，导致失败，忽略即可）
                executor.execute(() -> {

                    try {
                        autoMarkBizTag(autoMakeBizTagConfigInfo.getCorpId(),
                                allTagsInfo, autoMakeBizTagExternalContactInfo);

                    } catch (Exception e) {

                        String errLogInfo = String.format("auto make biz tag error, external contact info = %s, ex msg = %s",
                                JacksonUtils.format(autoMakeBizTagExternalContactInfo), e.getMessage());
                        log.error(errLogInfo, e);

                        WJobLogger.log(errLogInfo);
                        WJobLogger.log(e);
                    }
                });
            }
        }
    }

    private void autoMarkBizTag(@NonNull final String corpId,
                                @NonNull final Map<String, String> allTagsInfo,
                                @NonNull final AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo) throws Exception {

        String logInfo = String.format("[corp user = %s, external user 58 id = %s] try auto make biz tag.",
                autoMakeBizTagExternalContactInfo.getUserId(), autoMakeBizTagExternalContactInfo.getExternalUser58Id());
        log.info(logInfo);
        WJobLogger.log(logInfo);


        // 本次需要新增的标签
        final Set<String> addTagIds = getAddTagIds(allTagsInfo, autoMakeBizTagExternalContactInfo);

        // 需要清除掉所有之前可能打上的标签
        final Set<String> delTagIds = new HashSet<>(allTagsInfo.values());
        delTagIds.removeAll(addTagIds);


        UpdateExternalContactTagsReq updateExtTagsReq = new UpdateExternalContactTagsReq();
        updateExtTagsReq.setCorpId(corpId);
        updateExtTagsReq.setUserId(autoMakeBizTagExternalContactInfo.getUserId());
        updateExtTagsReq.setExternalUserId(autoMakeBizTagExternalContactInfo.getExternalUserId());
        updateExtTagsReq.setAddTagIds(addTagIds);
        updateExtTagsReq.setRemoveTagIds(delTagIds);

        updateExternalContactTagsRateLimiter.acquire();
        corpCustomerTagService.updateExternalContactTags(updateExtTagsReq);

        if (ObjectUtils.isEmpty(addTagIds)) { // 如果没有新增的标签，则只删除旧标签

            logInfo = String.format("[corp user = %s, external user 58 id = %s] not found any new biz tag.",
                    autoMakeBizTagExternalContactInfo.getUserId(), autoMakeBizTagExternalContactInfo.getExternalUser58Id());

        } else {

            // 根据标签id，反查名字，用以日志打印
            Set<String> addTagNames = new HashSet<>();
            for (Map.Entry<String, String> entry : allTagsInfo.entrySet()) {
                if (addTagIds.contains(entry.getValue())) {
                    addTagNames.add(entry.getKey());
                }
            }

            logInfo = String.format("[corp user = %s, external user 58 id = %s] make new biz tag done, new tags = %s",
                    autoMakeBizTagExternalContactInfo.getUserId(), autoMakeBizTagExternalContactInfo.getExternalUser58Id(), JacksonUtils.format(addTagNames));
        }

        log.info(logInfo);
        WJobLogger.log(logInfo);

    }

    private Set<String> getAddTagIds(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NotNull final AutoMakeBizTagExternalContactInfo autoMakeBizTagExternalContactInfo) {

        Set<String> addTagIds = new HashSet<>();

        Long wubaId = autoMakeBizTagExternalContactInfo.getExternalUser58Id();
        if (ObjectUtils.isNull(wubaId)) {// 如果无58id，不需要打新的标签

            String tagName = "未绑定58id";
            String tagId = Objects.requireNonNull(autoMakeBizTagsMap.get(tagName), String.format("[%s] 标签不存在", tagName));

            addTagIds.add(tagId);

        } else {
            Calendar instance = Calendar.getInstance();
            instance.add(Calendar.DAY_OF_YEAR, -1);
            Date time = instance.getTime();

            String importData = new SimpleDateFormat("yyyyMMdd").format(time);

            List<AutoMakeAuntBizTagDpEntity> autoMakeAuntBizTagDpEntities = autoMakeAuntBizTagDpJpaRepository.getByExternalUser58IdAndImportDate(
                    wubaId, importData
            );

            for (AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity : autoMakeAuntBizTagDpEntities) {
                addNullableTagId2Arr(addTagIds, getServiceCity1NameTagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagIds2Arr(addTagIds, getServiceCate4NameTagIds(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, getReceivingStatusTagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, getJoinInStatusTagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, getBindingMerchantTypeTagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, getCrowdsourcingFinishNumTagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, getFinishNumTagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, getCrowdsourcingLastFinishIntervalDaysTagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));

                addNullableTagId2Arr(addTagIds, getAuntServiceLevelTagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_last_order_pre7day_zhongbao_finish_order_num_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_last_order_pre7day_finish_order_num_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_last_10_baojie_order_service_rate_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_last_10_baojie_order_complaint_rate_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));

                addNullableTagId2Arr(addTagIds, get_is_zhongdiangong_aunt_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_is_new_zhongdiangong_aunt_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_zhongdiangong_finish_order_num_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_gender_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_background_check_status_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_medical_status_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));
                addNullableTagId2Arr(addTagIds, get_age_tagId(autoMakeBizTagsMap, autoMakeAuntBizTagDpEntity));

            }
        }

        return addTagIds;
    }

    private void addNullableTagId2Arr(@NonNull final Set<String> addTagIds,
                                      @Nullable final String tagId) {
        if (ObjectUtils.isEmpty(tagId)) {
            return;
        }

        addTagIds.add(tagId);
    }

    private void addNullableTagIds2Arr(@NonNull final Set<String> addTagIds,
                                       @Nullable final Set<String> tagIds) {
        if (ObjectUtils.isEmpty(tagIds)) {
            return;
        }

        addTagIds.addAll(tagIds);
    }


    private String getServiceCity1NameTagId(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String serviceCity1Name = autoMakeAuntBizTagDpEntity.getServiceCity1Name();
        if (ObjectUtils.isEmpty(serviceCity1Name)) {
            return null;
        }

        return autoMakeBizTagsMap.get(serviceCity1Name);
    }

    private Set<String> getServiceCate4NameTagIds(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                                  @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String serviceCate4IdsStr = autoMakeAuntBizTagDpEntity.getServiceCate4Ids();
        if (ObjectUtils.isEmpty(serviceCate4IdsStr)) {
            return null;
        }

        Set<Integer> serviceCate4Ids = Arrays.stream(serviceCate4IdsStr.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toSet());

        Set<String> serviceCate4Names = new HashSet<>();
        for (Integer serviceCate4Id : serviceCate4Ids) {
            CategoryEntity category = CMCPC.getCategoryById(serviceCate4Id);
            if (Objects.isNull(category)) {
                log.error("not found category by id, id = {}", serviceCate4Id);
                continue;
            }
            serviceCate4Names.add(category.getCateName());
        }

        Set<String> tagIds = new HashSet<>();
        for (String serviceCate4Name : serviceCate4Names) {

            String tagId = autoMakeBizTagsMap.get(serviceCate4Name);
            if (ObjectUtils.notEmpty(tagId)) {
                tagIds.add(tagId);
            }
        }

        return tagIds;
    }

    private String getReceivingStatusTagId(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                           @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String receivingStatus = autoMakeAuntBizTagDpEntity.getReceivingStatus();
        if (ObjectUtils.isEmpty(receivingStatus)) {
            return null;
        }

        return autoMakeBizTagsMap.get(receivingStatus);
    }

    private String getJoinInStatusTagId(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                        @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String joinInStatus = autoMakeAuntBizTagDpEntity.getJoinInStatus();
        if (ObjectUtils.isEmpty(joinInStatus)) {
            return null;
        }

        return autoMakeBizTagsMap.get(joinInStatus);
    }

    private String getBindingMerchantTypeTagId(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                               @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String bindingMerchantType = autoMakeAuntBizTagDpEntity.getBindingMerchantType();
        if (ObjectUtils.isEmpty(bindingMerchantType)) {
            return null;
        }

        return autoMakeBizTagsMap.get(bindingMerchantType);
    }

    private String getCrowdsourcingFinishNumTagId(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                                  @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String crowdsourcingFinishNum = autoMakeAuntBizTagDpEntity.getCrowdsourcingFinishNum();
        if (ObjectUtils.isEmpty(crowdsourcingFinishNum)) {
            return null;
        }

        int i = Integer.parseInt(crowdsourcingFinishNum);
        // 新众包阿姨：累计众包完单=0
        // 新人期众包阿姨：0<累计众包完单≤5
        // 成长期众包阿姨：5<累计众包完单≤30
        // 熟练期众包阿姨：30<累计众包完单≤100
        // 成熟期众包阿姨：累计众包完单>100

        String tagName;
        if (i > 100) {
            tagName = "成熟期众包阿姨";
        } else if (i > 30) {
            tagName = "熟练期众包阿姨";
        } else if (i > 5) {
            tagName = "成长期众包阿姨";
        } else if (i > 0) {
            tagName = "新人期众包阿姨";
        } else {
            tagName = "新众包阿姨";
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String getFinishNumTagId(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                     @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String finishNum = autoMakeAuntBizTagDpEntity.getFinishNum();
        if (ObjectUtils.isEmpty(finishNum)) {
            return null;
        }

        int i = Integer.parseInt(finishNum);

        // 新平台阿姨：累计平台完单=0
        // 新人期平台阿姨：0<累计平台完单≤5
        // 成长期平台阿姨：5<累计平台完单≤30
        // 熟练期平台阿姨：30<累计平台完单≤100
        // 成熟期平台阿姨：累计平台完单>100

        String tagName;
        if (i > 100) {
            tagName = "成熟期平台阿姨";
        } else if (i > 30) {
            tagName = "熟练期平台阿姨";
        } else if (i > 5) {
            tagName = "成长期平台阿姨";
        } else if (i > 0) {
            tagName = "新人期平台阿姨";
        } else {
            tagName = "新平台阿姨";
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String getCrowdsourcingLastFinishIntervalDaysTagId(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                                               @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String crowdsourcingLastFinishIntervalDays = autoMakeAuntBizTagDpEntity.getCrowdsourcingLastFinishIntervalDays();
        if (ObjectUtils.isEmpty(crowdsourcingLastFinishIntervalDays)) {
            return null;
        }

        int i = Integer.parseInt(crowdsourcingLastFinishIntervalDays);

        // 近7天活跃众包阿姨：上次众包完单距今≤7天
        // 8-30天活跃众包阿姨：7天<上次众包完单距今≤30天
        // 31-60天沉默众包阿姨：30天<上次众包完单距今≤60天
        // 61-120天沉默众包阿姨：60天<上次众包完单距今≤120天
        // 流失众包阿姨：上次众包完单距今>120天

        String tagName;
        if (i > 120) {
            tagName = "流失众包阿姨";
        } else if (i > 60) {
            tagName = "61-120天沉默众包阿姨";
        } else if (i > 30) {
            tagName = "31-60天沉默众包阿姨";
        } else if (i > 7) {
            tagName = "8-30天活跃众包阿姨";
        } else {
            tagName = "近7天活跃众包阿姨";
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String getAuntServiceLevelTagId(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String aunt_service_level = autoMakeAuntBizTagDpEntity.getAunt_service_level();
        if (ObjectUtils.isEmpty(aunt_service_level)) {
            return null;
        }

        // 服务等级：S、A、B、C、D、服务新人，无

        String tagName = null;
        if (Objects.equals("S", aunt_service_level)) {
            tagName = "S级阿姨";
        } else if (Objects.equals("A", aunt_service_level)) {
            tagName = "A级阿姨";
        } else if (Objects.equals("B", aunt_service_level)) {
            tagName = "B级阿姨";
        } else if (Objects.equals("C", aunt_service_level)) {
            tagName = "C级阿姨";
        } else if (Objects.equals("D", aunt_service_level)) {
            tagName = "D级阿姨";
        } else if (Objects.equals("服务新人", aunt_service_level)) {
            tagName = "服务新人";
        } else if (Objects.equals("无", aunt_service_level)) {
            tagName = "无服务分";
        }

        if (ObjectUtils.isNull(tagName)) {
            return null;
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String get_last_order_pre7day_zhongbao_finish_order_num_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String last_order_pre7day_zhongbao_finish_order_num = autoMakeAuntBizTagDpEntity.getLast_order_pre7day_zhongbao_finish_order_num();
        if (ObjectUtils.isEmpty(last_order_pre7day_zhongbao_finish_order_num)) {
            return null;
        }

        int i = Integer.parseInt(last_order_pre7day_zhongbao_finish_order_num);

        // 末单前7天低频众包阿姨：0<众包服务完成订单量≤5
        // 末单前7天中频众包阿姨：5<众包服务完成订单量≤14
        // 末单前7天高频众包阿姨：服务完成订单量＞14

        String tagName;
        if (i > 14) {
            tagName = "末单前7天高频众包阿姨";
        } else if (i > 5) {
            tagName = "末单前7天中频众包阿姨";
        } else {
            tagName = "末单前7天低频众包阿姨";
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String get_last_order_pre7day_finish_order_num_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String last_order_pre7day__finish_order_num = autoMakeAuntBizTagDpEntity.getLast_order_pre7day_finish_order_num();
        if (ObjectUtils.isEmpty(last_order_pre7day__finish_order_num)) {
            return null;
        }

        int i = Integer.parseInt(last_order_pre7day__finish_order_num);

        // 末单前7天低频平台阿姨：0<众包服务完成订单量≤5
        // 末单前7天中频平台阿姨：5<众包服务完成订单量≤14
        // 末单前7天高频平台阿姨：服务完成订单量＞14

        String tagName;
        if (i > 14) {
            tagName = "末单前7天高频平台阿姨";
        } else if (i > 5) {
            tagName = "末单前7天中频平台阿姨";
        } else {
            tagName = "末单前7天低频平台阿姨";
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String get_last_10_baojie_order_service_rate_tagId(@NonNull final Map<String, String> autoMakeBizTagsMap,
                                                               @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String last_10_baojie_order_service_rate = autoMakeAuntBizTagDpEntity.getLast_10_baojie_order_service_rate();
        if (ObjectUtils.isEmpty(last_10_baojie_order_service_rate)) {
            return null;
        }

        double i = Double.parseDouble(last_10_baojie_order_service_rate) * 100;

        // 0-20%服务率
        // 21-80%服务率
        // 81-90%服务率
        // 91-100%服务率

        String tagName;
        if (i >= 90) {
            tagName = "91-100%服务率";
        } else if (i >= 80) {
            tagName = "81-90%服务率";
        } else if (i >= 20) {
            tagName = "21-80%服务率";
        } else {
            tagName = "0-20%服务率";
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String get_last_10_baojie_order_complaint_rate_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String last_10_baojie_order_complaint_rate = autoMakeAuntBizTagDpEntity.getLast_10_baojie_order_complaint_rate();
        if (ObjectUtils.isEmpty(last_10_baojie_order_complaint_rate)) {
            return null;
        }

        double i = Double.parseDouble(last_10_baojie_order_complaint_rate) * 100;

        // 无投诉
        // 10-19%投诉率
        // 20-49%投诉率
        // 50-79%投诉率
        // 80-100%投诉率

        String tagName;
        if (i >= 80) {
            tagName = "80-100%投诉率";
        } else if (i >= 50) {
            tagName = "50-79%投诉率率";
        } else if (i >= 20) {
            tagName = "20-49%投诉率";
        } else if (i >= 10) {
            tagName = "10-19%投诉率";
        } else {
            tagName = "无投诉";
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String get_is_zhongdiangong_aunt_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        Integer zhongdiangong_aunt = autoMakeAuntBizTagDpEntity.getZhongdiangong_aunt();
        if (ObjectUtils.isNull(zhongdiangong_aunt)) {
            return null;
        }

        // 直聘钟点工阿姨基础信息：钟点工阿姨
        // 直聘钟点工阿姨基础信息：无完单钟点工阿姨
        String tagName = null;
        if (zhongdiangong_aunt == 1) {
            tagName = "钟点工阿姨";
        }

        if (ObjectUtils.isEmpty(tagName)) {
            return null;
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String get_is_new_zhongdiangong_aunt_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        Integer new_zhongdiangong_aunt = autoMakeAuntBizTagDpEntity.getNew_zhongdiangong_aunt();
        if (ObjectUtils.isNull(new_zhongdiangong_aunt)) {
            return null;
        }

        // 直聘钟点工阿姨基础信息：无完单钟点工阿姨
        String tagName = null;
        if (new_zhongdiangong_aunt == 1) {
            tagName = "无完单钟点工阿姨";
        }

        if (ObjectUtils.isEmpty(tagName)) {
            return null;
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String get_zhongdiangong_finish_order_num_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        Integer i = autoMakeAuntBizTagDpEntity.getZhongdiangong_finish_order_num();
        if (ObjectUtils.isNull(i)) {
            return null;
        }

        // 新人期直聘钟点工阿姨：0<累计钟点工完单≤5
        // 成长期直聘钟点工阿姨：5<累计钟点工完单≤30
        // 熟练期直聘钟点工阿姨：30<累计钟点工完单≤100
        // 成熟期直聘钟点工阿姨：累计钟点工完单>100

        String tagName = null;
        if (i > 100) {
            tagName = "成熟期直聘钟点工阿姨";
        } else if (i > 30) {
            tagName = "熟练期直聘钟点工阿姨";
        } else if (i > 5) {
            tagName = "成长期直聘钟点工阿姨";
        } else if (i > 0) {
            tagName = "新人期直聘钟点工阿姨";
        }

        if (ObjectUtils.isEmpty(tagName)) {
            return null;
        }

        return autoMakeBizTagsMap.get(tagName);
    }

    private String get_gender_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String str = autoMakeAuntBizTagDpEntity.getGender();
        if (ObjectUtils.isEmpty(str)) {
            return null;
        }

        return autoMakeBizTagsMap.get(str);
    }

    private String get_background_check_status_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String str = autoMakeAuntBizTagDpEntity.getBackground_check_status();
        if (ObjectUtils.isEmpty(str)) {
            return null;
        }

        return autoMakeBizTagsMap.get(str);
    }

    private String get_medical_status_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        String str = autoMakeAuntBizTagDpEntity.getMedical_status();
        if (ObjectUtils.isEmpty(str)) {
            return null;
        }
        return autoMakeBizTagsMap.get(str);
    }


    private String get_age_tagId(
            @NonNull final Map<String, String> autoMakeBizTagsMap,
            @NonNull final AutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity) {
        Integer i = autoMakeAuntBizTagDpEntity.getAge();
        if (ObjectUtils.isNull(i)) {
            return null;
        }

        String tagName = null;
        if (i > 60) {
            tagName = "61岁及以上";
        } else if (i >= 40) {
            tagName = i + "岁";
        } else if (i >= 30) {
            tagName = "30-39岁";
        } else if (i >= 20) {
            tagName = "20-30岁";
        }

        if (ObjectUtils.isEmpty(tagName)) {
            return null;
        }

        return autoMakeBizTagsMap.get(tagName);
    }


    @NotNull
    private Set<String> getAutoMakeBizTagCorpUserIds(@NonNull final AutoMakeBizTagConfigInfo autoMakeBizTagConfigInfo) {

        Set<String> simpleCorpUserIds = new HashSet<>();
        for (Integer departmentId : autoMakeBizTagConfigInfo.getDepartmentIds()) {
            Result<List<String>> corpUserIdsByDepartmentId = corpUserComponent.getByDepartmentId(
                    autoMakeBizTagConfigInfo.getCorpId(), departmentId);
            List<String> simpleCorpUsers = corpUserIdsByDepartmentId.getData();
            if (CollectionUtils.isEmpty(simpleCorpUsers)) {
                continue;
            }
            simpleCorpUserIds.addAll(simpleCorpUsers);
        }

        return simpleCorpUserIds;
    }

    /**
     * key = tag name
     * value = tag id
     */
    @NotNull
    private Map<String, String> getAutoMakeBizTagInfo(@NonNull final AutoMakeBizTagConfigInfo autoMakeBizTagConfigInfo) throws
            Exception {
        GetCorpCustomerTagsReq corpCustomerTagsReq = new GetCorpCustomerTagsReq();
        corpCustomerTagsReq.setCorpId(autoMakeBizTagConfigInfo.getCorpId());
        Result<List<CorpCustomerTagResp>> corpCustomerTags = corpCustomerTagService.getCorpCustomerTags(corpCustomerTagsReq);
        List<CorpCustomerTagResp> allTags = corpCustomerTags.getData();
        if (CollectionUtils.isEmpty(allTags)) {
            return Collections.emptyMap();
        }
        allTags.removeIf(tagGroup ->
                !tagGroup.getGroupName().startsWith("众包-") &&
                        !Objects.equals(tagGroup.getGroupName(), "直聘钟点工阿姨基础信息") &&
                        !Objects.equals(tagGroup.getGroupName(), "直聘钟点工阿姨行为") &&
                        !Objects.equals(tagGroup.getGroupName(), "家服&精选-阿姨基础信息") &&
                        !Objects.equals(tagGroup.getGroupName(), "家服&精选-阿姨年龄")
        );

        Map<String, String> tagMap = new HashMap<>();
        for (CorpCustomerTagResp tagGroup : allTags) {
            for (CorpCustomerTagResp.Tag tag : tagGroup.getTags()) {
                String name = tag.getName();
                String id = tag.getId();

                tagMap.put(name, id);
            }
        }

        return tagMap;
    }


    @Data
    private static class AutoMakeBizTagConfigInfo {

        @NotEmpty
        private final String corpId;

        @NotEmpty
        private final Set<Integer> departmentIds;

        public AutoMakeBizTagConfigInfo(final String corpId,
                                        final Set<Integer> departmentIds) {
            this.corpId = corpId;
            this.departmentIds = departmentIds;

            ValidationUtils.validate(this);
        }
    }

    @Data
    private static class AutoMakeBizTagExternalContactInfo {

        @NotEmpty
        private final String corpId;

        @NotEmpty
        private final String userId;

        @NotEmpty
        private final String externalUserId;

        @Nullable
        private final Long externalUser58Id;

        public AutoMakeBizTagExternalContactInfo(@NonNull final String corpId,
                                                 @NonNull final String userId,
                                                 @NonNull final String externalUserId,
                                                 @Nullable final Long externalUser58Id) {
            this.corpId = corpId;
            this.userId = userId;
            this.externalUserId = externalUserId;
            this.externalUser58Id = externalUser58Id;

            ValidationUtils.validate(this);
        }
    }

}
