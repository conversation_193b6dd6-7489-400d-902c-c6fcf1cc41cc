package com.bj58.hy.wx.qywxbiz.service.common.dify;

import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.service.common.dify.bo.DifyApiInfoBo;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DifyComponent {
    @Autowired
    private RedissonClient redisson;

    @Nullable
    public DifyApiInfoBo.Item getChatflowApiInfo(@NonNull final String corpId,
                                                 @NonNull final String userId) {
        @Nullable DifyApiInfoBo apiKeyBo = getApiKeys(corpId, userId);
        if (ObjectUtils.isNull(apiKeyBo)) {
            return null;
        }

        if (ObjectUtils.isEmpty(apiKeyBo.getChatflowInfo())) {
            return null;
        }

        return apiKeyBo.getChatflowInfo();
    }

    @Nullable
    public List<DifyApiInfoBo.Item> getWorkflowApiInfos(@NonNull final String corpId,
                                                        @NonNull final String userId) {
        @Nullable DifyApiInfoBo apiKeyBo = getApiKeys(corpId, userId);
        if (ObjectUtils.isNull(apiKeyBo)) {
            return null;
        }

        if (ObjectUtils.isEmpty(apiKeyBo.getWorkflowInfos())) {
            return null;
        }

        return apiKeyBo.getWorkflowInfos();
    }

    @Nullable
    public DifyApiInfoBo getApiKeys(@NonNull final String corpId,
                                    @NonNull final String userId) {
        DifyApiInfoBo difyApiInfoBo = new DifyApiInfoBo();

        // work flow
        String workflowApiInfoKey = String.format("dify_api_info:%s:%s:workflow", corpId, userId);
        RMap<String, String> workflowApiInfo = redisson.getMap(workflowApiInfoKey, StringCodec.INSTANCE);

        if (ObjectUtils.notEmpty(workflowApiInfo)) {
            String id = workflowApiInfo.get("id");
            String owner = workflowApiInfo.get("owner");
            String difyApiKey = workflowApiInfo.get("apiKey");
            String aggregationCycle = workflowApiInfo.get("aggregationCycle");

            if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(owner) || ObjectUtils.isEmpty(difyApiKey)) {
                log.warn("dify api info in wrong format, key = {}, api info = {}",
                        workflowApiInfoKey, JacksonUtils.format(workflowApiInfo.readAllMap()));

            } else {
                DifyApiInfoBo.Item item = new DifyApiInfoBo.Item(owner, id, difyApiKey,
                        ObjectUtils.notEmpty(aggregationCycle) ?
                                Integer.parseInt(aggregationCycle) : null
                );
                difyApiInfoBo.getWorkflowInfos().add(item);
            }
        }

        // chat flow
        String chatflowApiInfoKey = String.format("dify_api_info:%s:%s:chatflow", corpId, userId);
        RMap<String, String> chatflowApiInfo = redisson.getMap(chatflowApiInfoKey, StringCodec.INSTANCE);
        if (ObjectUtils.notEmpty(chatflowApiInfo)) {
            String id = chatflowApiInfo.get("id");
            String owner = chatflowApiInfo.get("owner");
            String difyApiKey = chatflowApiInfo.get("apiKey");

            if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(owner) || ObjectUtils.isEmpty(difyApiKey)) {
                log.warn("dify api info in wrong format, key = {}, api info = {}",
                        chatflowApiInfoKey, JacksonUtils.format(chatflowApiInfo.readAllMap()));

            } else {
                DifyApiInfoBo.Item chatflowInfo =
                        new DifyApiInfoBo.Item(owner, id, difyApiKey);
                difyApiInfoBo.setChatflowInfo(chatflowInfo);
            }
        }

        if (ObjectUtils.isEmpty(difyApiInfoBo.getChatflowInfo()) &&
                ObjectUtils.isEmpty(difyApiInfoBo.getWorkflowInfos())) {
            // log.warn("not found any dify api info, corp id = {}, user id = {}", corpId, userId);
            return null;
        }

        return difyApiInfoBo;
    }

}
