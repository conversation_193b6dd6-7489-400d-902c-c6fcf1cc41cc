package com.bj58.hy.wx.qywxbiz.service.common.dify.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Data
public class DifyApiInfoBo {

    private Item chatflowInfo;

    private List<Item> workflowInfos = new ArrayList<>();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {

        /**
         * 负责人
         */
        private String owner;

        /**
         * Dify ID
         */
        private String id;

        /**
         * Dify Api Key
         */
        private String apiKey;

        /**
         * 聚合的周期
         */
        private Integer aggregationCycle;

        public boolean needAggregationProcess() {
            return aggregationCycle != null &&
                    aggregationCycle > 5; // 如果聚合周期小于5s, 直接认定为不需要聚合
        }


        public Item(final String owner, final String id, final String apiKey) {
            this.owner = owner;
            this.id = id;
            this.apiKey = apiKey;
        }
    }

}
