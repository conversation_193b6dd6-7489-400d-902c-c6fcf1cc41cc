package com.bj58.hy.wx.qywxbiz.interfaces.scf.jingxuan;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.contract.IReWorkService;
import com.bj58.hy.wx.qywxbiz.contract.dto.rework.ReWorkPushReq;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanReWorkService;
import com.bj58.spat.scf.server.contract.annotation.ServiceBehavior;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/31 14:19
 */
@Slf4j
@Component
@ServiceBehavior
public class ReWorkService implements IReWorkService {

    @Autowired
    private JingXuanReWorkService jingXuanReWorkService;

    @Override
    public Result<String> reWorkPush(ReWorkPushReq reWorkPushReq) {

        jingXuanReWorkService.reWorkPush(reWorkPushReq);

        return Result.success("");
    }
}
