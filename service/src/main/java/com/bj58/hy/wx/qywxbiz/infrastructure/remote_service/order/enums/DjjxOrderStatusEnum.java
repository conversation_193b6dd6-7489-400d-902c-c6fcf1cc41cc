package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.order.enums;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum DjjxOrderStatusEnum {

    wait_pay("待支付", 3002),

    /**
     * 待支付订单，x分钟内没有支付，则流转为超时未支付
     */
    timeout_pay("超时未支付", 9004),

    /**
     * 待支付订单，x分钟内操作取消订单，则流转为未支付取消
     */
    not_pay_cancel("未支付取消", 9012),

    /**
     * 待支付 支付后 流转为 待接单
     */
    wait_accept("待接单", 1001),

    /**
     * 待支付 支付后 流转为 待接单，但是x分钟内，商家没有接单
     */
    timeout_unaccept("超时未接单", 9005),

    /**
     * 待支付 支付后 流转为 待接单，但是x分钟内，商家放弃接单
     */
    unaccept("放弃接单", 9003),

    /**
     * 待接单 商家接单后 流转为 待服务
     */
    wait_service("待服务", 2001),

    user_cancel("用户C端取消订单", 9002),

    /**
     * 待服务 流转为 服务完成
     */
    service_end("服务完成", 9001),

    not_pay_service_end("尾款支付失败", 9013),

    /**
     * 转派订单，转派前订单，流转为系统自动取消
     * 尾款支付失败，可能是之前的枚举状态，现在大概率是系统自动取消
     */
    system_cancel("系统自动取消", 9009),

    deleted("逻辑删除", 6001);

    private final String name;
    private final Integer code;

    public final static Map<Integer, DjjxOrderStatusEnum> MAP = Arrays.stream(DjjxOrderStatusEnum.values()).collect(Collectors.toMap(DjjxOrderStatusEnum::getCode, Function.identity()));

    public static DjjxOrderStatusEnum getEnumByCode(Integer code) {
        return MAP.get(code);
    }

    public static String getNameByCode(Integer code) {
        DjjxOrderStatusEnum djjxOrderStatusEnum = MAP.get(code);
        if (ObjectUtils.isEmpty(djjxOrderStatusEnum)) {
            return null;
        }
        return djjxOrderStatusEnum.getName();
    }

}
