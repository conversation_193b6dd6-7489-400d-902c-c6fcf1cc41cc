package com.bj58.hy.wx.qywxbiz.service.common.dify.workflow;

import cn.hutool.core.thread.BlockPolicy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb.WmbProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.ApplicationUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework.AbstractWmbHandler;
import com.bj58.hy.wx.qywxbiz.service.common.dify.workflow.enums.WorkflowTriggerType;
import com.bj58.spat.esbclient.*;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class DifyAsyncWorkflowHandler {

    @Autowired
    protected WmbProperties wmbProperties;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5, 20,
            1, TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("dify_async_workflow-%d").build(),
            new BlockPolicy(Runnable::run)
    );


    @PostConstruct
    protected void init() {
        try {
            Method initMethod = wmbHandler.getClass().getDeclaredMethod("init");
            initMethod.setAccessible(true);
            initMethod.invoke(wmbHandler);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 故意的没有构建成Spring的Bean
     */
    private final AbstractWmbHandler wmbHandler = new AbstractWmbHandler() {

        private ESBClient client;

        private WmbProperties.Client.Publish publish;

        @Override
        protected int getWmbSubjectId() {
            return publish.getSubjectId();
        }

        @Override
        protected ESBClient getWmbClient() {
            return this.client;
        }

        @Override
        protected String getWarningPrefix() {
            return "[dify_workflow]";
        }

        @Override
        public void init() {
            WmbProperties properties = DifyAsyncWorkflowHandler.this.wmbProperties;
            publish = properties.getPublish("dify_workflow");
            WmbProperties.Client.Subscribe subscribe = properties.getSubscribe("dify_workflow");

            if (Objects.isNull(publish) && Objects.isNull(subscribe)) {
                log.info("No configuration item exists, {} wmb client init failure. ", getWarningPrefix());
                return;
            }

            try {
                client = new ESBClient(properties.getPath());
                log.info("{} esb client init success", getWarningPrefix());

                if (!subscribe.isEnabled()) {
                    log.info("{} esb subscribe function has not been activated", getWarningPrefix());
                    return;
                }

                client.setReceiveSubject(new ESBSubject(
                        subscribe.getSubjectId(), subscribe.getClientId(), SubMode.PULL
                ));
                client.setReceiveQmaxLen(32);
                client.setReceiveHandler(new ESBReceiveHandler() {
                    @Override
                    public void messageReceived(final ESBMessage esbMessage) {
                        handlerMessage(esbMessage);
                    }
                });
                client.startAsyncPull();
                log.info("{} esb receive handler init success", getWarningPrefix());
            } catch (Exception e) {
                log.error(String.format(getWarningPrefix() + " esb client init failed: %s", e.getMessage()), e);
            }
        }
    };

    public void commit1v1Task(@NonNull final String messageId) {

        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("messageId", messageId);

        bodyInfoObj.put("triggerType", WorkflowTriggerType.CHAT_1V1.getCode());

        if ("Product".equals(System.getenv("WCloud_Env"))) {
            wmbHandler.doSend(JSON.toJSONString(bodyInfoObj));

        } else {
            // 沙箱直接执行
            ESBMessage message = new ESBMessage();
            message.setBody(JSON.toJSONString(bodyInfoObj).getBytes(StandardCharsets.UTF_8));
            handlerMessage(message);
        }
    }

    public void commit1v1AggregationCycleTask(@NonNull final String corpId,
                                              @NonNull final String userId,
                                              @NonNull final String externalUserId,
                                              @NonNull final String difyId,
                                              int delaySeconds) {

        Map<String, Object> bodyInfoObj = new HashMap<>();

        bodyInfoObj.put("corpId", corpId);
        bodyInfoObj.put("userId", userId);
        bodyInfoObj.put("externalUserId", externalUserId);
        bodyInfoObj.put("difyId", difyId);

        bodyInfoObj.put("triggerType", WorkflowTriggerType.CHAT_1V1_AGGREGATION_CYCLE.getCode());

        if ("Product".equals(System.getenv("WCloud_Env"))) {
            wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), delaySeconds);
        } else {
            new Thread(() -> {
                try {
                    TimeUnit.SECONDS.sleep(delaySeconds);
                } catch (InterruptedException ignored) {
                }
                // 沙箱直接执行
                ESBMessage message = new ESBMessage();
                message.setBody(JSON.toJSONString(bodyInfoObj).getBytes(StandardCharsets.UTF_8));
                handlerMessage(message);
            }).start();
        }
    }

    public void commitGroupChatTask(@NonNull final String messageId) {

        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("messageId", messageId);

        bodyInfoObj.put("triggerType", WorkflowTriggerType.CHAT_IN_GROUP.getCode());

        if ("Product".equals(System.getenv("WCloud_Env"))) {
            wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), 60);

        } else {
            new Thread(() -> {
                try {
                    TimeUnit.SECONDS.sleep(60);
                } catch (InterruptedException ignored) {
                }
                // 沙箱直接执行
                ESBMessage message = new ESBMessage();
                message.setBody(JSON.toJSONString(bodyInfoObj).getBytes(StandardCharsets.UTF_8));
                handlerMessage(message);
            }).start();
        }
    }

    public void commitGroupChatAggregationCycleTask(@NonNull final String corpId,
                                                    @NonNull final String chatId,
                                                    @NonNull final String difyId,
                                                    int delaySeconds) {

        Map<String, Object> bodyInfoObj = new HashMap<>();

        bodyInfoObj.put("corpId", corpId);
        bodyInfoObj.put("chatId", chatId);
        bodyInfoObj.put("difyId", difyId);

        bodyInfoObj.put("triggerType", WorkflowTriggerType.CHAT_IN_GROUP_AGGREGATION_CYCLE.getCode());

        if ("Product".equals(System.getenv("WCloud_Env"))) {
            wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), delaySeconds);

        } else {
            new Thread(() -> {
                try {
                    TimeUnit.SECONDS.sleep(delaySeconds);
                } catch (InterruptedException ignored) {
                }
                // 沙箱直接执行
                ESBMessage message = new ESBMessage();
                message.setBody(JSON.toJSONString(bodyInfoObj).getBytes(StandardCharsets.UTF_8));
                handlerMessage(message);
            }).start();
        }
    }

    private void handlerMessage(ESBMessage esbMessage) {
        String esbMsg = new String(esbMessage.getBody(), StandardCharsets.UTF_8);
        if (ObjectUtils.isEmpty(esbMsg)) {
            return;
        }

        Runnable runnable = () -> {
            log.info("receive dify async workflow job, body = {}", esbMsg);

            JSONObject esbMsgObj = JSON.parseObject(esbMsg);
            int triggerType = esbMsgObj.getIntValue("triggerType");

            DifyWorkflowComponent workflowComponent =
                    ApplicationUtils.getBean(DifyWorkflowComponent.class);

            try {
                if (Objects.equals(triggerType, WorkflowTriggerType.CHAT_1V1.getCode())) {
                    String messageId = esbMsgObj.getString("messageId");

                    workflowComponent.exec1v1Workflow(messageId);

                } else if (Objects.equals(triggerType, WorkflowTriggerType.CHAT_1V1_AGGREGATION_CYCLE.getCode())) {
                    String corpId = esbMsgObj.getString("corpId");
                    String userId = esbMsgObj.getString("userId");
                    String externalUserId = esbMsgObj.getString("externalUserId");
                    String difyId = esbMsgObj.getString("difyId");

                    workflowComponent.exec1v1AggregationCycleWorkflow(corpId, userId, externalUserId, difyId);

                } else if (Objects.equals(triggerType, WorkflowTriggerType.CHAT_IN_GROUP.getCode())) {
                    String messageId = esbMsgObj.getString("messageId");

                    workflowComponent.execGroupChatWorkflow(messageId);

                } else if (Objects.equals(triggerType, WorkflowTriggerType.CHAT_IN_GROUP_AGGREGATION_CYCLE.getCode())) {
                    String corpId = esbMsgObj.getString("corpId");
                    String chatId = esbMsgObj.getString("chatId");
                    String difyId = esbMsgObj.getString("difyId");

                    workflowComponent.execGroupChatAggregationCycleWorkflow(corpId, chatId, difyId);

                } else {

                    log.warn("unsupported workflow trigger type, trigger type = {}, body = {}",
                            triggerType, esbMsgObj.toJSONString());
                }

                log.info("dify workflow job process completion, body = {}", esbMsgObj.toJSONString());

            } catch (Exception e) {

                log.error("dify workflow job process completion, body = {}, ex msg = {}",
                        esbMsgObj.toJSONString(), e.getMessage(), e);
            }

        };

        executor.execute(runnable);
    }
}
