package com.bj58.hy.wx.qywxbiz.infrastructure.configuration.aop;

import com.bj58.hy.lib.spring.support.aspect.GlobalExceptionWrapperAspect;
import com.bj58.hy.lib.spring.support.aspect.VerifiedParamsAspect;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Configuration
public class AopConfiguration {

    @Bean
    @Order(GlobalExceptionWrapperAspect.ORDER)
    public GlobalExceptionWrapperAspect globalExceptionWrapperAspect() {
        return new GlobalExceptionWrapperAspect();
    }

    @Bean
    @Order(VerifiedParamsAspect.ORDER)
    public VerifiedParamsAspect verifiedParamsAspect() {
        return new VerifiedParamsAspect();
    }

    @Bean
    @Order(RedisLockWrapperAspect.ORDER)
    public RedisLockWrapperAspect redisLockWrapperAspect(RedissonClient redisson) {
        return new RedisLockWrapperAspect(redisson);
    }


}
