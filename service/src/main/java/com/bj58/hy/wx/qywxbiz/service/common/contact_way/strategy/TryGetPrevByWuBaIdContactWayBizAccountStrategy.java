package com.bj58.hy.wx.qywxbiz.service.common.contact_way.strategy;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IExternalContactService;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipByBindOrBiz58IdReq;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayReq;
import com.bj58.hy.wx.qywxbiz.entity.WxWorkContactWayBizAccountConfEntity;
import com.bj58.hy.wx.qywxbiz.repository.WxWorkContactWayBizAccountConfRepository;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 尽可能的添加之前添加过的账号的策略 策略
 */
@Slf4j
@Component
public class TryGetPrevByWuBaIdContactWayBizAccountStrategy {

    @SCFClient(lookup = IExternalContactService.SCF_URL)
    private IExternalContactService externalContactService;

    @Autowired
    protected WxWorkContactWayBizAccountConfRepository repository;

    @Autowired
    protected PollingGetContactWayBizAccountStrategy pollingGetContactWayBizAccountStrategy;

    @Nullable
    public List<String> getUser(@NonNull final ContactWayReq req,
                                @NonNull final Long wubaId) {
        List<String> userIds = new ArrayList<>();

        // 获取数据库配置
        List<WxWorkContactWayBizAccountConfEntity> accountEntities =
                repository.getAvailableUser(req.getBizLine(), Collections.singleton(req.getBizScene()), req.getCorpId(), req.getCityId());

        log.info("查询业务账号配置，业务线：{}, 业务场景：{}, 可用企业成员账号：{}",
                req.getBizLine(), req.getBizScene(),
                JSONObject.toJSONString(accountEntities));

        String userId = getUserId(accountEntities, req, wubaId);

        log.info("最终查到的账号id : {}", userId);

        if (StringUtils.isNotEmpty(userId)) {
            userIds.add(userId);
        }

        if (ObjectUtils.isEmpty(userIds)) { // 没有添加过任何企业微信，走轮询取策略
            return pollingGetContactWayBizAccountStrategy.getUser(req);
        }

        return userIds;
    }


    private String getUserId(@NonNull final List<WxWorkContactWayBizAccountConfEntity> availableUsers,
                             @NonNull final ContactWayReq req,
                             @NonNull final Long wubaId) {

        if (ObjectUtils.isEmpty(availableUsers)) {
            return null;
        }

        List<String> availableUserIds = availableUsers.stream()
                .map(WxWorkContactWayBizAccountConfEntity::getUserId)
                .collect(Collectors.toList());

        GetExternalContactRelationshipByBindOrBiz58IdReq getExternalContactRelationshipByBindOrBiz58IdReq =
                new GetExternalContactRelationshipByBindOrBiz58IdReq();
        getExternalContactRelationshipByBindOrBiz58IdReq.setCorpId(req.getCorpId());
        getExternalContactRelationshipByBindOrBiz58IdReq.setWubaUid(wubaId);
        getExternalContactRelationshipByBindOrBiz58IdReq.setUserIds(availableUserIds);

        List<GetExternalContactRelationshipResp> relationships = new ArrayList<>();
        try {
            Result<List<GetExternalContactRelationshipResp>> result =
                    externalContactService.getExternalContactRelationshipByBindOrBiz58Id(getExternalContactRelationshipByBindOrBiz58IdReq);

            if (result.isSuccess() && ObjectUtils.notEmpty(result.getData())) {
                relationships = result.getData();
            }

        } catch (Exception e) {
            log.error(String.format("获取58用户添加过的好友关系失败，corpId = %s, 58id = %s, user ids = %s, ex msg = %s",
                    req.getCorpId(), wubaId, JacksonUtils.format(availableUserIds), e.getMessage()), e);
        }

        if (ObjectUtils.isEmpty(relationships)) {
            log.info("当前58用户未添加过好友，corpId = {}, 58id = {}, user ids = {}",
                    req.getCorpId(), wubaId, JacksonUtils.format(availableUserIds));
            return null;
        }

        return relationships.stream()
                .filter(relationship -> Objects.equals(relationship.getStatus(), 1))
                .map(GetExternalContactRelationshipResp::getUserId)
                .findFirst()
                .orElse(relationships.get(0).getUserId());
    }
}
