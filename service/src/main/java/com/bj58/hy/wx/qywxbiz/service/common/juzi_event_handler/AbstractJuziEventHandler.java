package com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler;

import com.alibaba.fastjson.JSONObject;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2024/11/15 10:07
 */
public abstract class AbstractJuziEventHandler {

    protected org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(this.getClass());

    public abstract void process(@NonNull final JSONObject event);

    public abstract boolean matched(@NonNull final JSONObject event);

}
