package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.impl.risk_rule;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.service.bo.RateLimiterWrapper;
import com.bj58.hy.wx.qywxbiz.service.common.risk_rule.RiskAlarmComponent;
import com.bj58.hy.wx.qywxbiz.service.common.risk_rule.RiskRuleComponent;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractCreateGroupChatEventHandler;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * Description: 企业群聊相关的风控规则校验基类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCorpGroupChatRiskWxWorkEventHandler extends AbstractCreateGroupChatEventHandler {

    @Autowired
    protected RiskRuleComponent riskRuleComponent;

    @Autowired
    protected RiskAlarmComponent riskAlarmComponent;

    protected RateLimiterWrapper getCorpCreateGroupChatRateLimiter(@NonNull final String corpId) {
        RAtomicLong rateValue = redisson.getAtomicLong(
                String.format("CorpCreateGroupChatFreqRate:%s", corpId)
        );
        if (!rateValue.isExists()) {
            rateValue = redisson.getAtomicLong(
                    String.format("CorpCreateGroupChatFreqRate:%s", "default")
            );
        }

        RAtomicLong rateIntervalValue = redisson.getAtomicLong(
                String.format("CorpCreateGroupChatFreqRateInterval:%s", corpId)
        );
        if (!rateIntervalValue.isExists()) {
            rateIntervalValue = redisson.getAtomicLong(
                    String.format("CorpCreateGroupChatFreqRateInterval:%s", "default")
            );
        }

        RBucket<String> rateIntervalUnitValue = redisson.getBucket(
                String.format("CorpCreateGroupChatFreqRateIntervalUnit:%s", corpId), StringCodec.INSTANCE
        );
        if (!rateIntervalUnitValue.isExists()) {
            rateIntervalUnitValue = redisson.getBucket(
                    String.format("CorpCreateGroupChatFreqRateIntervalUnit:%s", "default"), StringCodec.INSTANCE
            );
        }

        long rate = 1500;
        if (rateValue.isExists()) {
            rate = rateValue.get();
        }

        long rateInterval = 1;
        if (rateIntervalValue.isExists()) {
            rateInterval = rateIntervalValue.get();
        }

        RateIntervalUnit rateIntervalUnit = RateIntervalUnit.DAYS;
        if (rateIntervalUnitValue.isExists()) {
            RateIntervalUnit v = RateIntervalUnit.valueOf(rateIntervalUnitValue.get());
            if (ObjectUtils.notNull(v)) {
                rateIntervalUnit = v;
            }
        }

        RRateLimiter rateLimiter = redisson.getRateLimiter(
                String.format("CorpCreateGroupChatFreqRateLimiter:%s", corpId)
        );

        if (!rateLimiter.isExists()) {
            log.info("[{}]当前企业建群的频率风控规则暂未初始化, 使用默认配置进行处理, rate = {}, rate interval = {}, rate interval unit = {}",
                    corpId, rate, rateInterval, rateIntervalUnit.name());

            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        RateLimiterConfig config = rateLimiter.getConfig();
        long oldRate = config.getRate();
        long oldRateInterval = config.getRateInterval();

        long newRateInterval = rateIntervalUnit.toMillis(rateInterval);

        if (!Objects.equals(oldRate, rate) ||
                !Objects.equals(oldRateInterval, newRateInterval)) {

            log.info("[{}]当前企业添加客户频率的设置发生变动, limiter = {}, old rate = {}, old rate interval = {}, new rate = {}, new rate interval = {}",
                    corpId, rateLimiter.getName(),
                    oldRate, oldRateInterval, rate, newRateInterval);

            redisson.getKeys().delete( // 删除key，这里的删除操作极端情况下可能会导致报错，影响不大暂时忽略
                    rateLimiter.getName(),
                    String.format("{%s}:permits", rateLimiter.getName()),
                    String.format("{%s}:value", rateLimiter.getName())
            );

            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        return new RateLimiterWrapper(rateLimiter, rate, rateInterval, rateIntervalUnit);
    }

    protected RateLimiterWrapper getCorpUserCreateGroupChatRateLimiter(@NonNull final String corpId, @NonNull final String userId) {
        RAtomicLong rateValue = redisson.getAtomicLong(
                String.format("CorpUserCreateGroupChatFreqRate:%s:%s", corpId, userId)
        );
        if (!rateValue.isExists()) {
            rateValue = redisson.getAtomicLong(
                    String.format("CorpUserCreateGroupChatFreqRate:%s", corpId)
            );

            if (!rateValue.isExists()) {
                rateValue = redisson.getAtomicLong(
                        String.format("CorpUserCreateGroupChatFreqRate:%s", "default")
                );
            }
        }

        RAtomicLong rateIntervalValue = redisson.getAtomicLong(
                String.format("CorpUserCreateGroupChatFreqRateInterval:%s:%s", corpId, userId)
        );
        if (!rateIntervalValue.isExists()) {
            rateIntervalValue = redisson.getAtomicLong(
                    String.format("CorpUserCreateGroupChatFreqRateInterval:%s", corpId)
            );

            if (!rateIntervalValue.isExists()) {
                rateIntervalValue = redisson.getAtomicLong(
                        String.format("CorpUserCreateGroupChatFreqRateInterval:%s", "default")
                );
            }
        }

        RBucket<String> rateIntervalUnitValue = redisson.getBucket(
                String.format("CorpUserCreateGroupChatFreqRateIntervalUnit:%s:%s", corpId, userId), StringCodec.INSTANCE
        );
        if (!rateIntervalUnitValue.isExists()) {
            rateIntervalUnitValue = redisson.getBucket(
                    String.format("CorpUserCreateGroupChatFreqRateIntervalUnit:%s", corpId), StringCodec.INSTANCE
            );

            if (!rateIntervalUnitValue.isExists()) {
                rateIntervalUnitValue = redisson.getBucket(
                        String.format("CorpUserCreateGroupChatFreqRateIntervalUnit:%s", "default"), StringCodec.INSTANCE
                );
            }
        }

        long rate = 50;
        if (rateValue.isExists()) {
            rate = rateValue.get();
        }

        long rateInterval = 1;
        if (rateIntervalValue.isExists()) {
            rateInterval = rateIntervalValue.get();
        }

        RateIntervalUnit rateIntervalUnit = RateIntervalUnit.DAYS;
        if (rateIntervalUnitValue.isExists()) {
            RateIntervalUnit v = RateIntervalUnit.valueOf(rateIntervalUnitValue.get());
            if (ObjectUtils.notNull(v)) {
                rateIntervalUnit = v;
            }
        }

        RRateLimiter rateLimiter = redisson.getRateLimiter(
                String.format("CorpUserCreateGroupChatFreqRateLimiter:%s:%s", corpId, userId)
        );

        if (!rateLimiter.isExists()) {
            log.info("[{},{}]当前企业成员建群的频率风控规则暂未初始化, 使用默认配置进行处理, rate = {}, rate interval = {}, rate interval unit = {}",
                    corpId, userId, rate, rateInterval, rateIntervalUnit.name());

            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }


        RateLimiterConfig config = rateLimiter.getConfig();
        long oldRate = config.getRate();
        long oldRateInterval = config.getRateInterval();

        long newRateInterval = rateIntervalUnit.toMillis(rateInterval);

        if (!Objects.equals(oldRate, rate) ||
                !Objects.equals(oldRateInterval, newRateInterval)) {

            log.info("[{},{}]当前企业成员添加客户的频率风控规则发生变动, limiter = {}, old rate = {}, old rate interval = {}, new rate = {}, new rate interval = {}",
                    corpId, userId, rateLimiter.getName(),
                    oldRate, oldRateInterval, rate, newRateInterval);

            redisson.getKeys().delete( // 删除key，这里的删除操作极端情况下可能会导致报错，影响不大暂时忽略
                    rateLimiter.getName(),
                    String.format("{%s}:permits", rateLimiter.getName()),
                    String.format("{%s}:value", rateLimiter.getName())
            );

            rateLimiter.setRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
        }

        return new RateLimiterWrapper(rateLimiter, rate, rateInterval, rateIntervalUnit);
    }

}