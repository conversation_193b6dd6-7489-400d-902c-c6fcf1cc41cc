package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.IMMessageBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.enums.IMMessageLevelEnum;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.utils.IMMessageUtils;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.LbgAiAsyncReplyHandler;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.constants.LbgAiCustomerConstants;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatIdRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatMessageRecordRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LbgAiHumanNotJoinComp {

    @Autowired
    private LbgAiChatIdRepository aiChatIdRepository;

    @Autowired
    private LbgAiChatMessageRecordRepository aiChatMessageRecordRepository;

    @Autowired
    private LbgAiAsyncReplyHandler lbgAiAsyncReplyHandler;

    @Autowired
    private LbgAiSendMessageComp sendMessageComp;

    @Autowired
    private RedissonClient redisson;

    public void sendDelayCheckHumanJoinTask(@NonNull final String corpId,
                                            @NonNull final String userId,
                                            @NonNull final String externalUserId,
                                            @NonNull final String contactName) {

        // 首次转人工，延迟4分钟判断是否人工未介入，未介入则给外部联系人发送消息：当前客服坐席繁忙，如您有紧急问题，可拨打客服热线400-8583645
        // 首次转人工，延迟3分钟判断是否人工未介入，发送强提醒卡片
        final String chatId = aiChatIdRepository.getChatId(corpId, userId, externalUserId);

        // 判断此chat_id 是否是首次转人工
        RBucket<String> firstToHumanBucket =
                redisson.getBucket(String.format("FIRST_TO_HUMAN:%s", chatId), StringCodec.INSTANCE);
        if (firstToHumanBucket.isExists()) {
            return;
        }

        lbgAiAsyncReplyHandler.commitDelayCheckHumanNotJoinThenSendTip2ExternalUser(corpId, userId, externalUserId);
        lbgAiAsyncReplyHandler.commitDelayCheckHumanNotJoinThenForceTipUser(corpId, userId, externalUserId, contactName);

        firstToHumanBucket.set("1", 1, TimeUnit.HOURS);
    }

    public void humanNotJoinThenSendTip2ExternalUser(@NonNull final String corpId,
                                                     @NonNull final String botUserId,
                                                     @NonNull final String externalUserId) {
        // 判断时间内，是否人工已经介入了呢？
        final String chatId = aiChatIdRepository.getChatId(corpId, botUserId, externalUserId);
        List<LbgAiMessageChatRecordBo> messages = aiChatMessageRecordRepository.getChatMessages(chatId);

        long l = System.currentTimeMillis() - 4 * 60 * 1000;

        boolean b = true; // 假定人工没有介入
        for (LbgAiMessageChatRecordBo messageRecord : messages) {
            if (messageRecord.getIsUserSend()) {
                continue;
            }

            Long dateStamp = messageRecord.getDateStamp();
            if (ObjectUtils.notNull(dateStamp)) {
                if (dateStamp > l) {
                    b = false;
                    break;
                }
            }
        }

        if (b) {
            // 发送消息
            sendMessageComp.sendTextToExternalUser(corpId, botUserId, externalUserId,
                    LbgAiCustomerConstants.CORP_USER_BUSY_TO_EXTERNAL_USER,
                    "[AI客服]4min内人工未介入, 自动发送客服坐席繁忙消息失败");
        }
    }

    public void humanNotJoinThenForceTipUser(@NonNull final String corpId,
                                             @NonNull final String botUserId,
                                             @NonNull final String externalUserId,
                                             @NonNull final String contactName) {
        // 判断时间内，是否人工已经介入了呢？
        final String chatId = aiChatIdRepository.getChatId(corpId, botUserId, externalUserId);
        List<LbgAiMessageChatRecordBo> messages = aiChatMessageRecordRepository.getChatMessages(chatId);

        long l = System.currentTimeMillis() - 3 * 60 * 1000;

        boolean b = true; // 假定人工没有介入
        for (LbgAiMessageChatRecordBo messageRecord : messages) {
            if (messageRecord.getIsUserSend()) {
                continue;
            }

            Long dateStamp = messageRecord.getDateStamp();
            if (ObjectUtils.notNull(dateStamp)) {
                if (dateStamp > l) {
                    b = false;
                    break;
                }
            }
        }

        if (!b) {
            return;
        }

        // 强提醒：美事卡片to客服

        // 获取对应关系
        RMap<String, String> to;
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            // 线上使用
            to = redisson.getMap(String.format("AI_HUMAN_NOT_JOIN_THEN_FORCE_TIP_USER_2_OA:%s", corpId), StringCodec.INSTANCE);
        } else {
            //  {
            //    "WangYanRui": "wangyanrui,chengtaiqi,hezhe"
            //  }
            to = redisson.getMap(String.format("AI_HUMAN_NOT_JOIN_THEN_FORCE_TIP_USER_2_OA_SANDBOX:%s", corpId), StringCodec.INSTANCE);
        }

        String oa_strs = to.get(botUserId);
        if (ObjectUtils.isEmpty(oa_strs)) {
            log.warn("not found oa by bot user id, corp id = {}, user id = {}", corpId, botUserId);
            return;
        }

        Set<String> oaSet = Arrays.stream(oa_strs.split(","))
                .filter(ObjectUtils::notEmpty)
                .collect(Collectors.toSet());
        if (ObjectUtils.isEmpty(oaSet)) {
            log.warn("not found oa by bot user id, corp id = {}, user id = {}, oa strs = {}", corpId, botUserId, oa_strs);
            return;
        }

        IMMessageBo imMessageBo = new IMMessageBo();
        imMessageBo.setTitle("客户消息超时未回复");
        imMessageBo.setLevel(IMMessageLevelEnum.Warning);
        imMessageBo.setReceives(new ArrayList<>(oaSet));

        IMMessageBo.TitleTextMap titleText = new IMMessageBo.TitleTextMap();
        titleText.put("通知内容：", "消息需要人工介入处理，已超过3分钟未回复，请尽快跟进！！");
        titleText.put("客户信息：", contactName);
        titleText.put("所属客服：", botUserId);
        imMessageBo.setContent(titleText);

        IMMessageUtils.sendSimpleMessageToOas(imMessageBo);
    }

}
