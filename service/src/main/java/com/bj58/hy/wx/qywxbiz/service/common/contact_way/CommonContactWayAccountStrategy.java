package com.bj58.hy.wx.qywxbiz.service.common.contact_way;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayReq;
import com.bj58.hy.wx.qywxbiz.entity.WxWorkContactWayBizAccountConfEntity;
import com.bj58.hy.wx.qywxbiz.repository.WxWorkContactWayBizAccountConfRepository;
import lombok.NonNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Order(value = Ordered.LOWEST_PRECEDENCE)
@Component
public class CommonContactWayAccountStrategy extends AbstractContactWayAccountStrategy {

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private RedisLockSupport lockSupport;

    @Autowired
    private WxWorkContactWayBizAccountConfRepository wxWorkContactWayBizAccountConfRepository;

    @Override
    public boolean matched(@NonNull ContactWayReq contactWayReq) {
        return true;
    }

    @Override
    @Nullable
    public List<String> getUserIds(@NonNull ContactWayReq contactWayReq) {
        int bizLine = contactWayReq.getBizLine();
        int bizScene = contactWayReq.getBizScene();

        List<WxWorkContactWayBizAccountConfEntity> accountConfEntities = wxWorkContactWayBizAccountConfRepository.getAvailableUser(
                bizLine, Collections.singleton(bizScene),
                contactWayReq.getCorpId(), contactWayReq.getCityId());

        if (accountConfEntities.size() == 1) {
            return Collections.singletonList(accountConfEntities.get(0).getUserId());
        }

        return Collections.singletonList(this.getNextUserId(accountConfEntities, contactWayReq));
    }


    private String getNextUserId(@NonNull final List<WxWorkContactWayBizAccountConfEntity> availableUsers,
                                 @NonNull final ContactWayReq req) {

        if (ObjectUtils.isEmpty(availableUsers)) {
            return null;
        }

        return lockSupport.execute(
                String.format("%s:COMMON_CONTACT_WAY_ACCOUNT:BIZ_LINE:%s:BIZ_SCENE:%s:CITY_ID:%s:LOCK",
                        req.getCorpId(), req.getBizLine(), req.getBizScene(), req.getCityId()),
                () -> {
                    List<String> availableUserIds = availableUsers.stream()
                            .map(WxWorkContactWayBizAccountConfEntity::getUserId)
                            .collect(Collectors.toList());

                    // redis取上一次的user
                    RBucket<String> prevUserBucket = redisson.getBucket(
                            String.format("%s:CommonContactWayAccountStrategy:%s:%s:%s", req.getCorpId(), req.getBizLine(), req.getBizScene(), req.getCityId()),
                            StringCodec.INSTANCE);

                    String nextUserId = null;

                    String prevUser = prevUserBucket.get();
                    if (ObjectUtils.notEmpty(prevUser)) { // 如果之前处理过，则尝试取下一个
                        nextUserId = getNext(availableUserIds, prevUser);
                    }

                    // 如果之前没有记录，或者其他的特殊情况
                    if (ObjectUtils.isEmpty(nextUserId)) {
                        nextUserId = availableUserIds.get(0);
                    }

                    // 存入最新账号
                    prevUserBucket.set(nextUserId, 5, TimeUnit.DAYS);

                    return nextUserId;
                }
        );
    }

    private static String getNext(@NonNull final List<String> availableUserIds,
                                  @NonNull final String currUserId) {
        if (ObjectUtils.isEmpty(availableUserIds)) {
            return null;
        }

        int currentIndex = availableUserIds.indexOf(currUserId);

        if (currentIndex == -1) { // 之前处理过，但是最新的配置中没有对应的企业成员
            return null;
        }

        int nextIndex = (currentIndex + 1) % availableUserIds.size();
        return availableUserIds.get(nextIndex);
    }

}
