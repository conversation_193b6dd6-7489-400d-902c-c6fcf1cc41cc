package com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework;

import com.bj58.hy.lib.core.ResultStatus;
import com.bj58.hy.lib.core.exception.ServiceException;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb.WmbProperties;
import com.bj58.spat.esbclient.ESBClient;
import com.bj58.spat.esbclient.ESBMessage;
import com.bj58.spat.esbclient.config.DelayLevel;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 */
public abstract class AbstractWmbHandler {

    private final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(this.getClass());

    @Autowired
    protected WmbProperties wmbProperties;

    protected abstract int getWmbSubjectId();

    protected abstract ESBClient getWmbClient();

    protected abstract String getWarningPrefix();

    @PostConstruct
    protected abstract void init();


    public void doSend(String body) {
        doSend(body, 0);
    }

    public void doSend(String body, long delaySeconds) {
        try {

            ESBMessage esbMessage = new ESBMessage(getWmbSubjectId(),
                    body.getBytes(StandardCharsets.UTF_8));
            esbMessage.setDelaySeconds(delaySeconds);

            if (Objects.isNull(getWmbClient())) {
                throw new RuntimeException(getWarningPrefix() + " wmb client is null. ");
            }

            getWmbClient().send(esbMessage);

            if (logSendInfo()) {
                log.info("[{}] send wmb message success, body = {}, delay seconds = {}",
                        getWmbSubjectId(), body, delaySeconds
                );
            }

        } catch (Exception e) {
            ServiceException.throwEx(ResultStatus.INTERNAL_SERVER_ERROR, e);
        }
    }

    public void doSend(String body, DelayLevel delayLevel) {
        try {

            ESBMessage esbMessage = new ESBMessage(getWmbSubjectId(),
                    body.getBytes(StandardCharsets.UTF_8));
            esbMessage.setDelayLevel(delayLevel);

            if (Objects.isNull(getWmbClient())) {
                throw new RuntimeException(getWarningPrefix() + " wmb client is null. ");
            }

            getWmbClient().send(esbMessage);

            if (logSendInfo()) {
                log.info("[{}] send wmb message success, body = {}, delay level = {}",
                        getWmbSubjectId(), body, delayLevel
                );
            }

        } catch (Exception e) {
            ServiceException.throwEx(ResultStatus.INTERNAL_SERVER_ERROR, e);
        }
    }

    /**
     * 是否打印发送的数据
     */
    protected boolean logSendInfo() {
        return true;
    }

}
