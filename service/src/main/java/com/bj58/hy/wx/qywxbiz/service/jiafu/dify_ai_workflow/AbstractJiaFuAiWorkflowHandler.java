package com.bj58.hy.wx.qywxbiz.service.jiafu.dify_ai_workflow;

import cn.hutool.core.thread.BlockPolicy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ChatContentRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.DifyRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework.JiaFuAiWorkflowWmbHandler;
import com.bj58.hy.wx.qywxbiz.service.common.dify.workflow.comp.DifyWorkflowAlarmComponent;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziEventHandler;
import com.bj58.spat.esbclient.ESBMessage;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.NonNull;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 家服AI工作流处理器抽象基类，抽取了WMB相关的共享逻辑
 */
public abstract class AbstractJiaFuAiWorkflowHandler extends AbstractJuziEventHandler {

    @Autowired
    protected RedissonClient redisson;

    @Autowired
    protected DifyWorkflowAlarmComponent difyWorkflowAlarmComponent;

    @Autowired
    protected RedisLockSupport lockSupport;

    @Autowired
    protected ChatContentRemoteService chatContentRemoteService;

    @Autowired
    protected DifyRemoteService difyRemoteService;

    @Autowired
    protected JiaFuAiWorkflowWmbHandler wmbHandler;

    /**
     * 线程池，用于异步处理消息
     */
    protected final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5, 20,
            1, TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("JiaFuAiWorkflow-%d").build(),
            new BlockPolicy(Runnable::run)
    );

    /**
     * 获取当前处理器处理的消息类型
     *
     * @return 消息类型标识符
     */
    public abstract String getMessageType();

    /**
     * 获取消息处理历史记录的Redis键前缀
     *
     * @return Redis键前缀
     */
    protected abstract String getHistoryKeyPrefix();

    /**
     * 执行具体的工作流处理逻辑
     *
     * @param messageId 消息ID
     */
    protected abstract void execWorkflow(@NonNull final String messageId);

    /**
     * 判断是否应该处理该消息
     *
     * @param callbackBo 消息回调数据
     * @return 是否应该处理
     */
    protected abstract boolean shouldProcess(@NonNull final ChatMessagesEventBo.Body callbackBo);

    @Override
    public void process(@NonNull JSONObject event) {
        if (!StringUtils.equalsIgnoreCase(event.getString("Event"), "聊天消息回调")) {
            return;
        }

        ChatMessagesEventBo.Body callbackBo = event.getObject("Body", ChatMessagesEventBo.Body.class);
        if (ObjectUtils.isNull(callbackBo)) {
            log.error("not support convert to standard DifyAiRelayTask, event body = {}", JacksonUtils.format(event));
            return;
        }

        Date timestamp = callbackBo.getStandardTimestamp();
        if (ObjectUtils.isNull(timestamp)) {
            log.error("not found standard timestamp, event body = {}", JacksonUtils.format(callbackBo));
            return;
        }

        // 托管账号掉线后重新登录，句子互动会推送近期的聊天记录
        if (System.currentTimeMillis() - timestamp.getTime() > 20 * 60 * 1000) {
            log.error("found historical event, event body = {}", JacksonUtils.format(callbackBo));
            return;
        }

        // 判断是否应该处理该消息
        if (!shouldProcess(callbackBo)) {
            return;
        }

        lockSupport.executeWithoutResult(
                String.format("JuziChatEventThenJiaFuDifyAiWorkflow:%s", callbackBo.getChatId()),
                () -> {
                    @NonNull final String messageId = callbackBo.getMessageId();

                    // 是否已经处理过？
                    RBucket<String> hasBeenProcessBucket = redisson.getBucket(
                            String.format("%s:%s:%s", getHistoryKeyPrefix(), messageId, callbackBo.getRoomWecomChatId()));
                    if (hasBeenProcessBucket.isExists()) {
                        log.warn("curr event has been process, event = {}", JacksonUtils.format(callbackBo));
                        return;
                    }
                    hasBeenProcessBucket.set("1", 3, TimeUnit.DAYS);

                    // 提交任务，子类可能会添加额外的检查逻辑
                    boolean shouldSubmit = beforeSubmitTask(callbackBo);
                    if (shouldSubmit) {
                        commitDelayedTriggerTask(messageId, getMessageType(), callbackBo.getRoomWecomChatId());
                    }
                }
        );
    }

    /**
     * 提交任务前的检查，子类可以覆盖此方法以添加额外的检查逻辑
     *
     * @param callbackBo 消息回调数据
     * @return 是否应该提交任务
     */
    protected boolean beforeSubmitTask(@NonNull final ChatMessagesEventBo.Body callbackBo) {
        return true; // 默认返回true，子类可覆盖
    }

    /**
     * 提交延迟触发任务
     *
     * @param messageId       消息ID
     * @param type            消息类型
     * @param roomWecomChatId
     */
    protected void commitDelayedTriggerTask(@NonNull final String messageId, @NonNull final String type, String roomWecomChatId) {
        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("messageId", messageId);
        bodyInfoObj.put("type", type); // 添加消息类型标识

        // 子类可以通过覆盖getDelaySeconds方法来自定义延迟时间
        wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), getDelaySeconds());
    }

    /**
     * 获取延迟时间，默认为5分钟，子类可覆盖
     *
     * @return 延迟秒数
     */
    protected long getDelaySeconds() {
        return 5 * 60; // 默认5分钟
    }

    /**
     * 处理WMB消息
     *
     * @param esbMessage ESB消息
     */
    public void handlerMessage(ESBMessage esbMessage) {
        String esbMsg = new String(esbMessage.getBody(), StandardCharsets.UTF_8);
        if (ObjectUtils.isEmpty(esbMsg)) {
            return;
        }

        Runnable runnable = () -> {
            log.info("receive jiafu ai workflow job, body = {}", esbMsg);

            JSONObject esbMsgObj = JSON.parseObject(esbMsg);

            try {
                String messageId = esbMsgObj.getString("messageId");
                String type = esbMsgObj.getString("type");

                log.info("message type now, current={}, received={}, messageId={}",
                        getMessageType(), type, messageId);

                // 根据消息类型区分处理逻辑，只处理匹配当前处理器类型的消息
                if (getMessageType().equals(type)) {
                    execWorkflow(messageId);
                } else {
                    log.info("message type not match, current={}, received={}, messageId={}",
                            getMessageType(), type, messageId);
                }

                log.info("jiafu ai workflow job process completion, body = {}", esbMsgObj.toJSONString());

            } catch (Exception e) {
                log.error("jiafu ai workflow job process exception, body = {}, ex msg = {}",
                        esbMsgObj.toJSONString(), e.getMessage(), e);
            }
        };

        executor.execute(runnable);
    }

    @Override
    public boolean matched(@NonNull JSONObject event) {
        return true;
    }
} 