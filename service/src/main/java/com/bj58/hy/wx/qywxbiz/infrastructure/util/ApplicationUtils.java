package com.bj58.hy.wx.qywxbiz.infrastructure.util;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Component
public class ApplicationUtils implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    public static ApplicationContext getApplicationContext() {
        checkApplicationContext();
        return ApplicationUtils.applicationContext;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        ApplicationUtils.applicationContext = applicationContext;
    }

    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) {
        checkApplicationContext();
        return (T) ApplicationUtils.applicationContext.getBean(name);
    }

    public static <T> T getBean(Class<T> clazz) {
        checkApplicationContext();
        return ApplicationUtils.applicationContext.getBean(clazz);
    }

    public static <T> Map<String, T> getBeans(Class<T> clazz) {
        checkApplicationContext();
        return ApplicationUtils.applicationContext.getBeansOfType(clazz);
    }

    public static <T> ObjectProvider<T> getBeanProvider(Class<T> clazz) {
        checkApplicationContext();
        return ApplicationUtils.applicationContext.getBeanProvider(clazz);
    }

    private static void checkApplicationContext() {
        if (ApplicationUtils.applicationContext == null) {
            throw new IllegalStateException(
                    "application context is null.");
        }
    }
}
