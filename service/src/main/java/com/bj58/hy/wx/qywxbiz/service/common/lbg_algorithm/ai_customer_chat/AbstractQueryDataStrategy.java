package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.ai_customer.config.AiCustomerProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import lombok.NonNull;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:14
 */
public abstract class AbstractQueryDataStrategy {

    @Autowired
    protected ExternalContactRemoteService externalContactService;

    @Autowired
    protected AiCustomerProperties aiCustomerProperties;

    public abstract boolean matched(@NonNull final Object biztypeObj);

    public abstract Result<Object> process(@NonNull final Map<String, Object> queryParam);

    /**
     * 验证外部联系人ID与订单用户58ID是否匹配
     *
     * @param externalUserId 外部联系人ID
     * @param order58Id      订单关联的用户58ID
     * @return true 如果匹配，false 如果不匹配
     */
    protected boolean validateExternalContactOwnership(String externalUserId, Long order58Id) {
        if (ObjectUtils.isEmpty(externalUserId) || ObjectUtils.isEmpty(order58Id)) {
            return false;
        }

        List<Long> external58Ids = externalContactService.get58IdsByExternalUserId(
                aiCustomerProperties.getCorpId(), externalUserId);

        return ObjectUtils.isNotEmpty(external58Ids) && external58Ids.contains(order58Id);
    }

}
