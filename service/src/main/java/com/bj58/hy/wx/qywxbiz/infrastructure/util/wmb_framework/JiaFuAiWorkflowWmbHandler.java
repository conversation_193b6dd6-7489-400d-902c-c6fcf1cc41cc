package com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb.WmbProperties;
import com.bj58.hy.wx.qywxbiz.service.jiafu.dify_ai_workflow.AbstractJiaFuAiWorkflowHandler;
import com.bj58.spat.esbclient.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 家服AI工作流WMB消息处理器
 * 负责处理家服AI工作流相关的消息接收和分发
 */
@Component
@Slf4j
public class JiaFuAiWorkflowWmbHandler extends AbstractWmbHandler {

    private static final String WMB_KEY = "jiafu_chat_ai_workflow";

    private ESBClient client;
    private WmbProperties.Client.Publish publish;

    @Autowired
    private ObjectProvider<AbstractJiaFuAiWorkflowHandler> jiaFuAiWorkflowHandlerObjectProvider;

    @Override
    protected int getWmbSubjectId() {
        return publish.getSubjectId();
    }

    @Override
    protected ESBClient getWmbClient() {
        return this.client;
    }

    @Override
    protected String getWarningPrefix() {
        return "[jiafu_ai_workflow]";
    }

    @Override
    @PostConstruct
    public void init() {
        WmbProperties properties = this.wmbProperties;
        publish = properties.getPublish(WMB_KEY);
        WmbProperties.Client.Subscribe subscribe = properties.getSubscribe(WMB_KEY);

        if (Objects.isNull(publish) && Objects.isNull(subscribe)) {
            log.info("No configuration item exists, {} wmb client init failure. ", getWarningPrefix());
            return;
        }

        try {
            client = new ESBClient(properties.getPath());
            log.info("{} esb client init success", getWarningPrefix());

            if (!subscribe.isEnabled()) {
                log.info("{} esb subscribe function has not been activated", getWarningPrefix());
                return;
            }

            client.setReceiveSubject(new ESBSubject(
                    subscribe.getSubjectId(), subscribe.getClientId(), SubMode.PULL
            ));
            client.setReceiveQmaxLen(32);
            client.setReceiveHandler(new ESBReceiveHandler() {
                @Override
                public void messageReceived(final ESBMessage esbMessage) {
                    handleMessage(esbMessage);
                }
            });
            client.startAsyncPull();
            log.info("{} esb receive handler init success", getWarningPrefix());
        } catch (Exception e) {
            log.error(String.format(getWarningPrefix() + " esb client init failed: %s", e.getMessage()), e);
        }
    }

    /**
     * 处理接收到的消息并分发给相应的处理器
     * @param esbMessage ESB消息
     */
    private void handleMessage(ESBMessage esbMessage) {
        String esbMsg = new String(esbMessage.getBody(), StandardCharsets.UTF_8);
        if (ObjectUtils.isEmpty(esbMsg)) {
            return;
        }

        try {
            JSONObject esbMsgObj = JSON.parseObject(esbMsg);
            if (esbMsgObj == null) {
                log.error("Failed to parse ESB message: {}", esbMsg);
                return;
            }

            String messageId = esbMsgObj.getString("messageId");
            String type = esbMsgObj.getString("type");

            if (ObjectUtils.isEmpty(messageId) || ObjectUtils.isEmpty(type)) {
                log.error("Invalid ESB message format, missing messageId or type: {}", esbMsg);
                return;
            }

            log.info("Received jiafu ai workflow message: messageId={}, type={}", messageId, type);

            boolean anyHandlerProcessed = false;
            // 遍历所有处理器，将消息分发给匹配类型的处理器
            for (AbstractJiaFuAiWorkflowHandler handler : jiaFuAiWorkflowHandlerObjectProvider) {
                if (type.equals(handler.getMessageType())) {
                    try {
                        log.info("Dispatching message to handler: {}, type={}, messageId={}", 
                                handler.getClass().getSimpleName(), type, messageId);
                        handler.handlerMessage(esbMessage);
                        anyHandlerProcessed = true;
                    } catch (Exception e) {
                        log.error("Error handling message in {}: {}", 
                                handler.getClass().getSimpleName(), e.getMessage(), e);
                    }
                }
            }

            if (!anyHandlerProcessed) {
                log.warn("No handler processed message with type: {}, messageId: {}", type, messageId);
            }
        } catch (Exception e) {
            log.error("Error processing ESB message: {}, error: {}", esbMsg, e.getMessage(), e);
        }
    }
} 