package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.strategy;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.ai_customer_chat.AbstractQueryDataStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatMessageRecordRepository;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:17
 */
@Component
public class QueryAiCustomerMsgRecordStrategy extends AbstractQueryDataStrategy {

    @Autowired
    private LbgAiChatMessageRecordRepository chatMessageRecordRepository;

    @Override
    public boolean matched(@NonNull Object biztypeObj) {

        if (StringUtils.equalsIgnoreCase(biztypeObj.toString(), "queryAiCustomerMsgRecord")) {
            return true;
        }

        return false;
    }

    @Override
    public Result<Object> process(@NonNull Map<String, Object> queryParam) {

        Object chatIdObj = queryParam.get("chatId");

        // 通过chatId查询Ai用户最近7天的聊天记录
        String chatId = chatIdObj.toString();
        List<LbgAiMessageChatRecordBo> messageRecordBackupList = chatMessageRecordRepository.getChatMessages(chatId);
        return Result.success(JacksonUtils.format(messageRecordBackupList));
    }
}
