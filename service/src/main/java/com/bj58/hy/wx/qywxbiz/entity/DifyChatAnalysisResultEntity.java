package com.bj58.hy.wx.qywxbiz.entity;

import com.bj58.hy.lib.spring.support.jpa.superclass.Identifiable;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.util.Date;

/**
 * Dify聊天分析结果实体类
 */
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "t_dify_chat_analysis_result", indexes = {
        @Index(name = "idx_user_external_date", columnList = "user_id, external_user_id, analysis_date"),
        @Index(name = "idx_analysis_date", columnList = "analysis_date"),
})
public class DifyChatAnalysisResultEntity extends Identifiable {

    /**
     * 企业微信客服用户ID
     */
    @Getter
    @Setter
    @Column(name = "user_id", nullable = false, length = 64)
    private String userId;

    /**
     * 外部用户ID
     */
    @Getter
    @Setter
    @Column(name = "external_user_id", nullable = false, length = 64)
    private String externalUserId;

    /**
     * 分析日期（格式：yyyy-MM-dd）
     */
    @Getter
    @Setter
    @Column(name = "analysis_date", nullable = false, length = 10)
    private String analysisDate;

    /**
     * Dify分析结果（JSON格式）
     */
    @Getter
    @Setter
    @Column(name = "dify_result", nullable = false, columnDefinition = "TEXT")
    private String difyResult;

    /**
     * 聊天记录数量
     */
    @Getter
    @Setter
    @Column(name = "chat_message_count", nullable = false)
    private Integer chatMessageCount;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    @Column(name = "create_time", nullable = false)
    private Date createTime;

    /**
     * 更新时间
     */
    @Getter
    @Setter
    @Column(name = "update_time", nullable = false)
    private Date updateTime;

    /**
     * 处理状态（1：成功，2：失败，0：删除）
     */
    @Getter
    @Setter
    @Column(name = "status", nullable = false)
    private Integer status;

    /**
     * 失败原因（当status=2时记录失败原因）
     */
    @Getter
    @Setter
    @Column(name = "failure_reason", columnDefinition = "TEXT")
    private String failureReason;

    /**
     * 四级类目ID（从Dify分析结果中提取）
     */
    @Getter
    @Setter
    @Column(name = "category_4id", length = 20)
    private String category4Id;

}
