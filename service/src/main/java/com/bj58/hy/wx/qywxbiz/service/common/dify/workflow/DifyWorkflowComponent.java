package com.bj58.hy.wx.qywxbiz.service.common.dify.workflow;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywx.contract.dto.group_chat.ExternalContactGroupChatSnapshotResp;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziGroupChatContentRecord;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ChatContentRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.GroupChatRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.DifyRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowRequest;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowResponse;
import com.bj58.hy.wx.qywxbiz.service.common.dify.DifyComponent;
import com.bj58.hy.wx.qywxbiz.service.common.dify.bo.DifyApiInfoBo;
import com.bj58.hy.wx.qywxbiz.service.common.dify.workflow.comp.DifyWorkflowAlarmComponent;
import com.bj58.hy.wx.qywxbiz.service.common.dify.workflow.comp.DifyWorkflowInterventionConditionsComponent;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class DifyWorkflowComponent {

    @Autowired
    private DifyWorkflowAlarmComponent difyWorkflowAlarmComponent;

    @Autowired
    private DifyComponent difyComponent;

    @Autowired
    private DifyRemoteService difyRemoteService;

    @Autowired
    private ChatContentRemoteService chatContentRemoteService;

    @Autowired
    private GroupChatRemoteService groupChatRemoteService;

    @Autowired
    private DifyAsyncWorkflowHandler asyncWorkflowHandler;

    @Autowired
    private DifyWorkflowInterventionConditionsComponent interventionConditionsComponent;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private RedisLockSupport lockSupport;

    public void exec1v1Workflow(@NonNull final String messageId) {
        // 提前判断 是否标准化了聊天记录?
        JuziSingleChatContentRecord singleChatContentRecord = chatContentRemoteService.getSingleChatContentRecord(messageId);
        if (ObjectUtils.isNull(singleChatContentRecord)) {
            log.error("not found standard 1v1 chat content record, message id = {}", messageId);
            return;
        }

        String corpId = singleChatContentRecord.getCorpId();
        String userId = singleChatContentRecord.getUserId();
        String externalUserId = singleChatContentRecord.getExternalUserId();

        lockSupport.executeWithoutResult(
                String.format("DifyWorkflowComponent:1v1:%s:%s:%s", corpId, userId, externalUserId),
                () -> {

                    List<DifyApiInfoBo.Item> workflowApiInfos = difyComponent.getWorkflowApiInfos(corpId, userId);
                    if (ObjectUtils.isEmpty(workflowApiInfos)) { // 前一步有校验，这里不应该查不到 api key
                        return;
                    }

                    for (DifyApiInfoBo.Item apiInfo : workflowApiInfos) {
                        // 当前workflow，立即执行，还是聚合后执行？

                        if (apiInfo.needAggregationProcess()) {

                            reCommit1v1AggregationCycleTask(apiInfo, singleChatContentRecord);

                        } else {
                            exec1v1Workflow0(corpId, userId, externalUserId, messageId, apiInfo);
                        }
                    }
                }
        );
    }

    public void exec1v1AggregationCycleWorkflow(@NonNull final String corpId,
                                                @NonNull final String userId,
                                                @NonNull final String externalUserId,
                                                @NonNull final String difyId) {

        lockSupport.executeWithoutResult(
                String.format("DifyWorkflowComponent:1v1:%s:%s:%s", corpId, userId, externalUserId),
                () -> {
                    List<DifyApiInfoBo.Item> workflowApiInfos = difyComponent.getWorkflowApiInfos(corpId, userId);
                    if (ObjectUtils.isEmpty(workflowApiInfos)) { // 前一步有校验，这里不应该查不到 api key
                        log.warn("not found any workflow api info, corp id = {}, user id = {}, external user id = {}",
                                corpId, userId, externalUserId);
                        return;
                    }

                    DifyApiInfoBo.Item targetApiInfo = workflowApiInfos.stream()
                            .filter(apiInfo -> Objects.equals(difyId, apiInfo.getId()))
                            .findFirst()
                            .orElse(null);
                    if (ObjectUtils.isNull(targetApiInfo)) {
                        log.error("not found target api info, corp id = {}, user id = {}, external user id = {}, dify id = {}",
                                corpId, userId, externalUserId, difyId);
                        return;
                    }

                    // 再次判断是否可提交
                    boolean permits = interventionConditionsComponent.tryGetCommitAggregationCycleWorkflowTaskPermits(
                            corpId, userId, externalUserId, new Date());
                    if (!permits) {
                        return;
                    }

                    RBucket<String> messageRecords = get1v1AggregationCycleRecordsRepository(corpId, userId, externalUserId);
                    String messageIds = messageRecords.get();
                    if (ObjectUtils.isEmpty(messageIds)) {
                        return;
                    }
                    messageRecords.delete();

                    exec1v1Workflow0(corpId, userId, externalUserId, messageIds, targetApiInfo);
                }
        );
    }

    public void execGroupChatWorkflow(@NonNull final String messageId) {
        // 是否标准化了聊天记录?
        JuziGroupChatContentRecord groupChatContentRecord = chatContentRemoteService.getGroupChatContentRecord(messageId);
        if (ObjectUtils.isNull(groupChatContentRecord)) {
            log.error("not found standard group chat content record, message id = {}", messageId);
            return;
        }

        final String corpId = groupChatContentRecord.getCorpId();
        final String chatId = groupChatContentRecord.getChatId();

        // 是否存在群信息？
        ExternalContactGroupChatSnapshotResp groupChatInfoFromSnapshot =
                groupChatRemoteService.getExternalContactGroupChatInfoFromSnapshot(corpId, chatId);
        if (ObjectUtils.isNull(groupChatInfoFromSnapshot)) {
            log.error("not found group chat info, corp id = {}, chat id = {}", corpId, chatId);
            return;
        }

        lockSupport.executeWithoutResult(
                String.format("DifyWorkflowComponent:group_chat:%s:%s", corpId, chatId),
                () -> {

                    List<DifyApiInfoBo.Item> workflowApiInfos = difyComponent.getWorkflowApiInfos(corpId, "_GROUP_");
                    if (ObjectUtils.isEmpty(workflowApiInfos)) { // 前一步没有校验，这里存在查不到 api key 的情况
                        return;
                    }

                    for (DifyApiInfoBo.Item apiInfo : workflowApiInfos) {
                        // 当前workflow，立即执行，还是聚合后执行？

                        if (apiInfo.needAggregationProcess()) {

                            reCommitGroupChatAggregationCycleTask(apiInfo, groupChatContentRecord);

                        } else {
                            execGroupChatWorkflow0(corpId, chatId, messageId, apiInfo);
                        }
                    }
                }
        );
    }

    public void execGroupChatAggregationCycleWorkflow(@NonNull final String corpId,
                                                      @NonNull final String chatId,
                                                      @NonNull final String difyId) {

        lockSupport.executeWithoutResult(
                String.format("DifyWorkflowComponent:group_chat:%s:%s", corpId, chatId),
                () -> {
                    List<DifyApiInfoBo.Item> workflowApiInfos = difyComponent.getWorkflowApiInfos(corpId, "_GROUP_");
                    if (ObjectUtils.isEmpty(workflowApiInfos)) { // 前一步有校验，这里不应该查不到 api key
                        log.warn("not found any workflow api info, corp id = {}, chat id = {}",
                                corpId, chatId);
                        return;
                    }

                    DifyApiInfoBo.Item targetApiInfo = workflowApiInfos.stream()
                            .filter(apiInfo -> Objects.equals(difyId, apiInfo.getId()))
                            .findFirst()
                            .orElse(null);
                    if (ObjectUtils.isNull(targetApiInfo)) {
                        log.error("not found target api info, corp id = {}, chat id = {}, dify id = {}",
                                corpId, chatId, difyId);
                        return;
                    }

                    // 再次判断是否可提交
                    boolean permits = interventionConditionsComponent.tryGetCommitAggregationCycleWorkflowTaskPermits(
                            corpId, chatId, new Date());
                    if (!permits) {
                        return;
                    }

                    RBucket<String> messageRecords = getGroupChatAggregationCycleRecordsRepository(corpId, chatId);
                    String messageIds = messageRecords.get();
                    if (ObjectUtils.isEmpty(messageIds)) {
                        return;
                    }
                    messageRecords.delete();

                    execGroupChatWorkflow0(corpId, chatId, messageIds, targetApiInfo);
                }
        );
    }

    private void exec1v1Workflow0(@NonNull final String corpId,
                                  @NonNull final String userId,
                                  @NonNull final String externalUserId,
                                  @NonNull final String messageId,
                                  @NonNull final DifyApiInfoBo.Item apiInfo) {


        WorkflowRequest request = new WorkflowRequest();

        String user = String.format("%s:%s:%s", corpId, userId, externalUserId);
        request.setUser(user);

        request.getInputs().put("corpId", corpId);
        request.getInputs().put("userId", userId);
        request.getInputs().put("externalUserId", externalUserId);
        request.getInputs().put("messageId", messageId);

        try {
            log.info("request dify workflow, req = {}", JacksonUtils.format(request));

            Result<WorkflowResponse> result = difyRemoteService.workflow(
                    apiInfo.getApiKey(), request);

            log.info("request dify workflow done, req = {}, resp = {}",
                    JacksonUtils.format(request), JacksonUtils.format(result));

            if (ObjectUtils.isNull(result) || result.isFailed() || ObjectUtils.isNull(result.getData())) {
                difyWorkflowAlarmComponent.sendTipsToMeiShi_1V1(corpId, userId, apiInfo, result);
            }

        } catch (Exception e) {
            log.error("trigger workflow exception, workflow request = {}", JacksonUtils.format(request), e);
        }
    }

    private void reCommit1v1AggregationCycleTask(@NonNull final DifyApiInfoBo.Item apiInfo,
                                                 @NonNull final JuziSingleChatContentRecord singleChatContentRecord) {
        final String messageId = singleChatContentRecord.getMessageId();

        final String corpId = singleChatContentRecord.getCorpId();
        final String userId = singleChatContentRecord.getUserId();
        final String externalUserId = singleChatContentRecord.getExternalUserId();

        // 存储消息唯一标识，供后续聚合后 给到 workflow
        RBucket<String> messageRecords = get1v1AggregationCycleRecordsRepository(corpId, userId, externalUserId);
        String messageIds = messageRecords.get();
        if (ObjectUtils.isEmpty(messageIds)) {
            messageIds = messageId;
        } else {
            messageIds = messageIds + "," + messageId;
        }
        messageRecords.set(messageIds,
                apiInfo.getAggregationCycle() * 2, TimeUnit.SECONDS);

        // 判断当前消息是否需要提交？
        boolean permits = interventionConditionsComponent.tryGetCommitAggregationCycleWorkflowTaskPermits(corpId, userId, externalUserId,
                singleChatContentRecord.getCreateTime());
        if (permits) {
            interventionConditionsComponent.setNextCommitAggregationCycleWorkflowTaskTimestamp(corpId, userId, externalUserId,
                    DateUtils.add(new Date(), Calendar.SECOND, apiInfo.getAggregationCycle()));

            // 提交一个异步聚合的执行
            asyncWorkflowHandler.commit1v1AggregationCycleTask(
                    corpId, userId, externalUserId, apiInfo.getId(), apiInfo.getAggregationCycle()
            );
        }
    }


    private void execGroupChatWorkflow0(@NonNull final String corpId,
                                        @NonNull final String chatId,
                                        @NonNull final String messageId,
                                        @NonNull final DifyApiInfoBo.Item apiInfo) {

        WorkflowRequest request = new WorkflowRequest();

        String user = String.format("%s:%s", corpId, chatId);
        request.setUser(user);

        request.getInputs().put("corpId", corpId);
        request.getInputs().put("chatId", chatId);
        request.getInputs().put("messageId", messageId);

        try {
            log.info("request dify workflow, req = {}", JacksonUtils.format(request));

            Result<WorkflowResponse> result = difyRemoteService.workflow(
                    apiInfo.getApiKey(), request);

            log.info("request dify workflow done, req = {}, resp = {}",
                    JacksonUtils.format(request), JacksonUtils.format(result));

            if (ObjectUtils.isNull(result) || result.isFailed() || ObjectUtils.isNull(result.getData())) {
                difyWorkflowAlarmComponent.sendTipsToMeiShi_GROUP(corpId, chatId, apiInfo, result);
            }

        } catch (Exception e) {
            log.error("trigger workflow exception, workflow request = {}", JacksonUtils.format(request), e);
        }
    }

    private void reCommitGroupChatAggregationCycleTask(@NonNull final DifyApiInfoBo.Item apiInfo,
                                                       @NonNull final JuziGroupChatContentRecord groupChatContentRecord) {
        final String messageId = groupChatContentRecord.getMessageId();

        final String corpId = groupChatContentRecord.getCorpId();
        final String chatId = groupChatContentRecord.getChatId();

        // 存储消息唯一标识，供后续聚合后 给到 workflow
        RBucket<String> messageRecords = getGroupChatAggregationCycleRecordsRepository(corpId, chatId);
        String messageIds = messageRecords.get();
        if (ObjectUtils.isEmpty(messageIds)) {
            messageIds = messageId;
        } else {
            messageIds = messageIds + "," + messageId;
        }
        messageRecords.set(messageIds,
                apiInfo.getAggregationCycle() * 2, TimeUnit.SECONDS);

        // 判断当前消息是否需要提交？
        boolean permits = interventionConditionsComponent.tryGetCommitAggregationCycleWorkflowTaskPermits(corpId, chatId,
                groupChatContentRecord.getCreateTime());
        if (permits) {
            interventionConditionsComponent.setNextCommitAggregationCycleWorkflowTaskTimestamp(corpId, chatId,
                    DateUtils.add(new Date(), Calendar.SECOND, apiInfo.getAggregationCycle()));

            // 提交一个异步聚合的执行
            asyncWorkflowHandler.commitGroupChatAggregationCycleTask(
                    corpId, chatId, apiInfo.getId(), apiInfo.getAggregationCycle()
            );
        }
    }

    private RBucket<String> get1v1AggregationCycleRecordsRepository(@NonNull final String corpId,
                                                                    @NonNull final String userId,
                                                                    @NonNull final String externalUserId) {
        return redisson.getBucket(String.format("1v1DifyAggregationCycleWorkflowRecords:%s:%s:%s",
                corpId, userId, externalUserId), StringCodec.INSTANCE);
    }

    private RBucket<String> getGroupChatAggregationCycleRecordsRepository(@NonNull final String corpId,
                                                                          @NonNull final String chatId) {
        return redisson.getBucket(String.format("GroupChatDifyAggregationCycleWorkflowRecords:%s:%s",
                corpId, chatId), StringCodec.INSTANCE);
    }
}
