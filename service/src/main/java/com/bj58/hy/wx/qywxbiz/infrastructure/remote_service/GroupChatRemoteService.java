package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.support.pojo.PagedResult;
import com.bj58.hy.lib.core.util.DateUtils;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IGroupChatService;
import com.bj58.hy.wx.qywx.contract.dto.group_chat.ExternalContactGroupChatSnapshotResp;
import com.bj58.hy.wx.qywx.contract.dto.group_chat.GetCorpUserGroupChatReq;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GroupChatRemoteService {

    @SCFClient(lookup = IGroupChatService.SCF_URL)
    private IGroupChatService groupChatService;

    public ExternalContactGroupChatSnapshotResp getExternalContactGroupChatInfoFromSnapshot(@NonNull final String corpId,
                                                                                            @NonNull final String chatId) {
        try {
            Result<ExternalContactGroupChatSnapshotResp> result = groupChatService.getExternalContactGroupChatInfoFromSnapshot(corpId, chatId);

            return result.getData();

        } catch (Exception e) {
            log.error("getExternalContactGroupChatInfoFromSnapshot error, corpId = {}, chatId = {}",
                    corpId, chatId, e);
        }

        return null;
    }

    public long countCorpUserTodayCreateGroupChat(@NonNull final String corpId,
                                                  @NonNull final String userId) {

        Result<PagedResult<ExternalContactGroupChatSnapshotResp>> result = null;
        try {
            GetCorpUserGroupChatReq req = new GetCorpUserGroupChatReq();
            req.setCorpId(corpId);
            req.setUserId(userId);

            req.setCreateTimeBegin(DateUtils.getTodayZeroOClock());
            req.setOnlyTotal(true);

            result = groupChatService.getCorpUserGroupChats(req);

        } catch (Exception e) {
            log.error("count external chat error, " +
                    "corp id = {}, user id = {}", corpId, userId, e);
            return 0;
        }


        if (ObjectUtils.isNull(result) ||
                result.isFailed()) {
            log.error("count external contact relation error, " +
                    "corp id = {}, user id = {}", corpId, userId);
            return 0;
        }

        return result.getData().getTotal();
    }

}
