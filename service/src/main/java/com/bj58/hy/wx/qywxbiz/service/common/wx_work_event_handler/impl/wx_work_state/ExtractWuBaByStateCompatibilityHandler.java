package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.impl.wx_work_state;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.fx.bcore.entity.neworder.OrderEntity;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IExternalContactMappingService;
import com.bj58.hy.wx.qywx.contract.dto.external_contact_mapping.SaveExternalUserLinkedWuBaIdReq;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanOrderQueryService;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractWxWorkEventHandler;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/12 16:49
 */
@Component
public class ExtractWuBaByStateCompatibilityHandler extends AbstractWxWorkEventHandler {

    @SCFClient(lookup = IExternalContactMappingService.SCF_URL)
    private IExternalContactMappingService externalContactMappingService;

    @Autowired
    private JingXuanOrderQueryService jingXuanOrderQueryService;

    @Override
    public void process(@NonNull JSONObject event) {

        try {
            String corpId = event.getString("ToUserName");
            String externalUserID = event.getString("ExternalUserID");
            if (StringUtils.isBlank(corpId) || StringUtils.isBlank(externalUserID)) {
                return;
            }

            JSONObject ext = event.getJSONObject("Ext");
            if (ObjectUtils.isEmpty(ext)) {
                return;
            }

            String state = ext.getString("State");
            if (StringUtils.isBlank(state)) {
                return;
            }

            // analysis State
            Long wbId = analysis(state);
            log.info("analysis wuba, id = {}，state={}", wbId, state);

            if (ObjectUtils.isNull(wbId)) {
                return;
            }

            log.info("found external user linked wuba, info = {}", event.toJSONString());

            SaveExternalUserLinkedWuBaIdReq saveExternalUserLinkedWuBaReq = new SaveExternalUserLinkedWuBaIdReq();
            saveExternalUserLinkedWuBaReq.setCorpId(corpId);
            saveExternalUserLinkedWuBaReq.setExternalUserId(externalUserID);
            saveExternalUserLinkedWuBaReq.setWubaId(wbId);
            saveExternalUserLinkedWuBaReq.setReason(state);
            externalContactMappingService.saveExternalUserLinkedWuBaId(saveExternalUserLinkedWuBaReq);

            log.info("save external user linked wuba success, info = {}",
                    JacksonUtils.format(saveExternalUserLinkedWuBaReq));
        } catch (Exception e) {
            log.error("ObtainWuBaWorkEventHandler process error , event:{}", JSONObject.toJSONString(event), e);
        }

    }

    @Override
    public boolean matched(final @NonNull JSONObject event) {
        return true;
    }

    private Long analysis(String state) {
        Long wbId = null;

        try {
            if (state.length() <= 4) {
                return null;
            }

            String origin = state.substring(0, 4);
            OriginEnum originEnum = OriginEnum.getOrigin(origin);

            if (originEnum == null) {
                return null;
            }

            switch (originEnum) {
                case DJJX:
                    Map<String, String> originAuntMap = parseStringToMap(state, "=");
                    String wbIdStr = originAuntMap.entrySet().stream()
                            .filter(entry -> entry.getKey().contains(OriginEnum.DJJX.getName()))
                            .map(Map.Entry::getValue)
                            .findFirst()
                            .orElse(null);

                    if (StringUtils.isNotEmpty(wbIdStr)) {
                        wbId = Long.valueOf(wbIdStr);
                    }

                    break;
                case AUNT:

                    Map<String, String> originDJJXMap = parseStringToMap(state, ":");
                    Map.Entry<String, String> firstEntry = originDJJXMap.entrySet().iterator().next();
                    // AUNT_3 :xxxxxx 为订单id 其他先不解析
                    if (firstEntry.getKey().equals("AUNT_3")) {
                        wbId = Long.valueOf(firstEntry.getValue());
                        OrderEntity orderEntity = jingXuanOrderQueryService.query(wbId);
                        if (orderEntity != null) {
                            wbId = orderEntity.getWubaId();
                        }
                    }

                    break;
                case QWJL:
                    break;
                default:

            }
        } catch (Exception e) {
            log.error("analysis error", e);
        }

        return wbId;
    }


    private static Map<String, String> parseStringToMap(String str, String regex) {
        Map<String, String> map = new HashMap<>();

        String[] keyValuePairs = str.split("\\|");
        for (String keyValuePair : keyValuePairs) {
            String[] keyValue = keyValuePair.split(regex);
            if (keyValue.length == 2) {
                String key = keyValue[0].trim();
                String value = keyValue[1].trim();
                map.put(key, value);
            }
        }

        return map;
    }

    @Getter
    @AllArgsConstructor
    public enum OriginEnum {

        DJJX("DJJX", "DJJX_1_1,DJJX_1_3,DJJX_1_4,DJJX_1_5:单后加企微 DJJX_1_2 : 会员个人中心"),

        AUNT("AUNT", "AUNT_1,AUNT_2,AUNT_3 : 阿姨签到加企微-保洁"),

        QWJL("QWJL", "QWJL : 阿姨签到加企微-维修"),

        ;
        private final String name;
        private final String desc;

        public static OriginEnum getOrigin(String origin) {
            if (ObjectUtils.isEmpty(origin)) {
                return null;
            }

            for (OriginEnum temp : OriginEnum.values()) {
                if (temp.name.equals(origin)) {
                    return temp;
                }
            }

            return null;
        }
    }
}
