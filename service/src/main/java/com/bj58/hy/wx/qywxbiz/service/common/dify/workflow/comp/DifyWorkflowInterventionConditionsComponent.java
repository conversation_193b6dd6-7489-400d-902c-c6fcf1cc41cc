package com.bj58.hy.wx.qywxbiz.service.common.dify.workflow.comp;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.service.common.dify.DifyComponent;
import com.bj58.hy.wx.qywxbiz.service.common.dify.bo.DifyApiInfoBo;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DifyWorkflowInterventionConditionsComponent {

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private DifyComponent difyComponent;

    public boolean canJoin(final String corpId,
                           final String botUserId) {
        if (ObjectUtils.isEmpty(botUserId)) {
            return false;
        }

        List<DifyApiInfoBo.Item> workflowApiInfos = difyComponent.getWorkflowApiInfos(corpId, botUserId);
        return ObjectUtils.notEmpty(workflowApiInfos);
    }

    /**
     * 判断是否可以提交 聚合任务，同时设置下一次可以提交 聚合任务的时间
     */
    public boolean tryGetCommitAggregationCycleWorkflowTaskPermits(@NonNull final String corpId,
                                                                   @NonNull final String userId,
                                                                   @NonNull final String externalUserId,
                                                                   @NonNull final Date timestamp) {

        String key = String.format("CouldCommit1v1DifyAggregationCycleWorkflowTask:%s:%s:%s",
                corpId, userId, externalUserId);

        RAtomicLong rAtomicLong = redisson.getAtomicLong(key);
        if (!rAtomicLong.isExists()) {
            return true;
        }

        return rAtomicLong.get() <= timestamp.getTime();
    }

    /**
     * 设置可以提交 聚合任务的时间：时间戳后 可提交任务
     */
    public void setNextCommitAggregationCycleWorkflowTaskTimestamp(@NonNull final String corpId,
                                                                   @NonNull final String userId,
                                                                   @NonNull final String externalUserId,
                                                                   @NonNull final Date timestamp) {

        String key = String.format("CouldCommit1v1DifyAggregationCycleWorkflowTask:%s:%s:%s",
                corpId, userId, externalUserId);

        RAtomicLong rAtomicLong = redisson.getAtomicLong(key);
        rAtomicLong.set(timestamp.getTime());
    }

    /**
     * 判断是否可以提交 聚合任务，同时设置下一次可以提交 聚合任务的时间
     */
    public boolean tryGetCommitAggregationCycleWorkflowTaskPermits(@NonNull final String corpId,
                                                                   @NonNull final String chatId,
                                                                   @NonNull final Date timestamp) {

        String key = String.format("CouldCommitGroupChatDifyAggregationCycleWorkflowTask:%s:%s",
                corpId, chatId);

        RAtomicLong rAtomicLong = redisson.getAtomicLong(key);
        if (!rAtomicLong.isExists()) {
            return true;
        }

        return rAtomicLong.get() <= timestamp.getTime();
    }

    /**
     * 设置可以提交 聚合任务的时间：时间戳后 可提交任务
     */
    public void setNextCommitAggregationCycleWorkflowTaskTimestamp(@NonNull final String corpId,
                                                                   @NonNull final String chatId,
                                                                   @NonNull final Date timestamp) {

        String key = String.format("CouldCommitGroupChatDifyAggregationCycleWorkflowTask:%s:%s",
                corpId, chatId);

        RAtomicLong rAtomicLong = redisson.getAtomicLong(key);
        rAtomicLong.set(timestamp.getTime());
    }
}
