package com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework;

import cn.hutool.core.thread.BlockPolicy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.bj58.huangye.alg.qasystem.client.entity.AiCustomerDaojiaResult;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb.WmbProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatIdRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.LbgAiReplyComponent;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.bo.LbgAiChatPartnersBo;
import com.bj58.spat.esbclient.*;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.SynchronousQueue;
import java.util.*;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

/**
 * 异步AI客服WMB消息处理器
 * 负责处理异步AI客服相关的消息接收和分发
 */
@Component
@Slf4j
public class AsyncAiChatWmbHandler extends AbstractWmbHandler {

    private static final String WMB_KEY = "async_ai_chat";

    private ESBClient client;

    /**
     * 线程池，用于异步处理消息
     */
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5, 20,
            1, TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("AsyncAiChat-%d").build(),
            new BlockPolicy(Runnable::run)
    );


    @Override
    protected int getWmbSubjectId() {
        return 0;
    }

    @Override
    protected ESBClient getWmbClient() {
        return this.client;
    }

    @Override
    protected String getWarningPrefix() {
        return "[async_ai_chat]";
    }

    @Autowired
    private ExternalContactRemoteService externalContactRemoteService;

    @Autowired
    private LbgAiChatIdRepository aiChatIdRepository;

    @Autowired
    private LbgAiReplyComponent lbgAiReplyComponent;

    @PostConstruct
    public void init() {
        WmbProperties.Client.Subscribe subscribe = wmbProperties.getSubscribe(WMB_KEY);

        if (ObjectUtils.isNull(subscribe)) {
            log.error("{} wmb properties config not found", getWarningPrefix());
            return;
        }

        try {
            client = new ESBClient(wmbProperties.getPath());
            log.info("{} esb client init success", getWarningPrefix());

            if (!subscribe.isEnabled()) {
                log.info("{} esb subscribe function has not been activated", getWarningPrefix());
                return;
            }

            client.setReceiveSubject(new ESBSubject(
                    subscribe.getSubjectId(), subscribe.getClientId(), SubMode.PULL
            ));
            client.setReceiveQmaxLen(32);
            client.setReceiveHandler(new ESBReceiveHandler() {
                @Override
                public void messageReceived(final ESBMessage esbMessage) {
                    handleMessage(esbMessage);
                }
            });
            client.startAsyncPull();
            log.info("{} esb receive handler init success", getWarningPrefix());
        } catch (Exception e) {
            log.error(String.format(getWarningPrefix() + " esb client init failed: %s", e.getMessage()), e);
        }
    }

    /**
     * 处理接收到的消息并分发给相应的处理器
     *
     * @param esbMessage ESB消息
     */
    private void handleMessage(ESBMessage esbMessage) {
        String esbMsg = new String(esbMessage.getBody(), StandardCharsets.UTF_8);
        if (ObjectUtils.isEmpty(esbMsg)) {
            return;
        }

        try {
            JSONObject esbMsgObj = JSON.parseObject(esbMsg);
            if (esbMsgObj == null) {
                log.error("Failed to parse ESB message: {}", esbMsg);
                return;
            }

            // 异步处理消息
            Runnable task = () -> {
                try {
                    handleAsyncAiRequest(esbMsgObj);
                } catch (Exception e) {
                    log.error("Error handling async ai chat message: {}", e.getMessage(), e);
                }
            };

            executor.execute(task);

        } catch (Exception e) {
            log.error("Error processing ESB message: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理异步AI请求
     * {"output":{"code":0,"data":"您可以在58到家APP或者微信小程序里，点开底下的【全部服务】，然后左边列表里找到您需要的服务，直接预约下单哦。","extend":{"res":"\n\n您好，可以在58到家APP/微信小程序，点击底部【全部服务】-在左侧列表处，选择所需服务，预约下单。","isUserFeedback":"","userInquiryCategory":"{\"fourCategoryCode\":\"12172\",\"threeCategoryCode\":\"12037\"}","isSatisfied":"","pushSkuParam":"{}","isPushSku":"true","pushSkuCategory":"新居开荒","intention":"其他意图","sceneState":"主流程"},"type":0},"input":{"cateId":"12037","cateName":"保洁清洗","chatId":"chengtaiqi|wmOrVVCwAAl2R7SiELDHRtrrID3gbOQw","extend":{"threeCode":"12037","recallKbListAll":[{"a":"就回答：行，那有问题您再联系我","q":"如果用户表示确认或收到信息","score":0.7661440968513489},{"a":"就回答：嗯嗯好的，后续有任何问题或服务需求，可以随时给我留言哈~","q":"如果用户表达已进行下单操作","score":0.8709465265274048},{"a":"就回答：您好，可以在58到家APP/微信小程序，点击底部【全部服务】-在左侧列表处，选择所需服务，预约下单","q":"如果用户询问如何下单","score":0.8443350195884705}],"categoryIdentify":"新居开荒","genIntentions":["表达下单的意愿"],"ABTestLabel":"B","ip":"************","redisSnapshot":{"aiCustomer_Daojia_push_sku_chengtaiqi|wmOrVVCwAAl2R7SiELDHRtrrID3gbOQw":"true","aiCusotomer_doajia_category_chengtaiqi|wmOrVVCwAAl2R7SiELDHRtrrID3gbOQw":"新居开荒","aiCustomer_Daojia_push_sku_category_chengtaiqi|wmOrVVCwAAl2R7SiELDHRtrrID3gbOQw":"新居开荒","aiCustomer_Daojia_push_sku_param_chengtaiqi|wmOrVVCwAAl2R7SiELDHRtrrID3gbOQw_12172":"{}"},"isToManual":false,"fourCode":"12172","intentions":"想要购买服务","intention":"其他意图","async":"1","pushSkuCategoryFourCode":"12172","createTime":"2025-06-23 15:54:07.461","pushSkuCategoryThreeCode":"12037","gptRes":"\n\n您好，可以在58到家APP/微信小程序，点击底部【全部服务】-在左侧列表处，选择所需服务，预约下单。","intention_kbList":[{"a":"就回答：行，那有问题您再联系我","q":"如果用户表示确认或收到信息","score":0.7661440968513489},{"a":"就回答：嗯嗯好的，后续有任何问题或服务需求，可以随时给我留言哈~","q":"如果用户表达已进行下单操作","score":0.8709465265274048},{"a":"就回答：您好，可以在58到家APP/微信小程序，点击底部【全部服务】-在左侧列表处，选择所需服务，预约下单","q":"如果用户询问如何下单","score":0.8443350195884705}],"orderInfo":"","userSnapshot":{"queryAiCustomerMsgRecord":[{"dateStamp":1750663385870,"receiver":"chengtaiqi","sender":"wmOrVVCwAAl2R7SiELDHRtrrID3gbOQw","messageId":"bd476dc8bcf66c3d242776a85de6220b","isUserSend":true,"message":"我想下单"},{"dateStamp":1750663690956,"receiver":"wmOrVVCwAAl2R7SiELDHRtrrID3gbOQw","sender":"chengtaiqi","messageId":"1346257cdc30d14179478bb70f5de0e1","isUserSend":false,"message":"您需要购买【新居开荒】服务，请问您家是否有家具？"},{"dateStamp":1750665214875,"receiver":"chengtaiqi","sender":"wmOrVVCwAAl2R7SiELDHRtrrID3gbOQw","messageId":"76cab028432469d58e8738d94afd07d0","isUserSend":true,"message":"我想下单"}]},"categories":"新居开荒","category":"新居开荒","chatList":[{"a":"您需要购买【新居开荒】服务，请问您家是否有家具？","createTime":"2025-06-23 15:28:10.956","q":"我想下单"}],"pushSkuCategory":"新居开荒","sceneState":"主流程"},"fourCateId":"12172","fourCateName":"新居开荒","inputList":[{"inputType":"text","msgId":"76cab028432469d58e8738d94afd07d0","q":"我想下单"}],"qUnion":"我想下单","requestId":1386736085224398848,"userId":"wmOrVVCwAAl2R7SiELDHRtrrID3gbOQw"}}
     *
     * @param esbMsgObj ESB消息对象
     */
    private void handleAsyncAiRequest(JSONObject esbMsgObj) {
        try {
            log.info("Processing async AI request: {}", esbMsgObj.toJSONString());

            // 解析输入数据
            JSONObject input = esbMsgObj.getJSONObject("input");
            JSONObject output = esbMsgObj.getJSONObject("output");

            if (input == null || output == null) {
                log.error("Invalid async AI request format, missing input or output: {}", esbMsgObj.toJSONString());
                return;
            }

            // 从input中提取基本信息
            String chatId = input.getString("chatId");
            String externalUserId = input.getString("userId");

            if (ObjectUtils.isEmpty(chatId) || ObjectUtils.isEmpty(externalUserId)) {
                log.error("Missing required fields in async AI request: chatId={}, externalUserId={}", chatId, externalUserId);
                return;
            }

            // 从chatId中提取corpId和botUserId
            String corpId = extractCorpIdFromChatId(chatId);
            String botUserId = extractBotUserIdFromChatId(chatId);

            if (ObjectUtils.isEmpty(corpId) || ObjectUtils.isEmpty(botUserId)) {
                log.error("Failed to extract corpId or botUserId from chatId: {}", chatId);
                return;
            }

            // 获取外部联系人信息
            GetExternalContactRelationshipResp contactRelationshipResult =
                    externalContactRemoteService.getRelationship(corpId, botUserId, externalUserId);

            if (Objects.isNull(contactRelationshipResult)) {
                log.error("External contact relationship not found: corpId={}, botUserId={}, externalUserId={}",
                        corpId, botUserId, externalUserId);
                return;
            }

            // 从 extend 中获取联系人姓名，如果获取不到则使用默认值
            String contactName = "客户";
            JSONObject inputExtend = input.getJSONObject("extend");
            if (inputExtend != null) {
                String name = inputExtend.getString("customerName");
                if (ObjectUtils.notEmpty(name)) {
                    contactName = name;
                }
            }

            // 构建AI返回结果对象
            AiCustomerDaojiaResult aiResult = new AiCustomerDaojiaResult();
            aiResult.setCode(output.getInteger("code"));
            aiResult.setData(output.getString("data"));
            aiResult.setType(output.getInteger("type"));

            // 处理extend字段
            JSONObject extendObj = output.getJSONObject("extend");
            if (extendObj != null) {
                Map<String, String> extend = new HashMap<>();
                for (String key : extendObj.keySet()) {
                    extend.put(key, extendObj.getString(key));
                }
                aiResult.setExtend(extend);
            }

            // 构建消息记录对象列表
            List<LbgAiMessageChatRecordBo> reqAiMessages = new ArrayList<>();
            JSONArray inputList = input.getJSONArray("inputList");
            if (inputList != null) {
                for (int i = 0; i < inputList.size(); i++) {
                    JSONObject inputItem = inputList.getJSONObject(i);
                    if (inputItem != null) {
                        LbgAiMessageChatRecordBo messageRecord = new LbgAiMessageChatRecordBo();
                        messageRecord.setMessageId(inputItem.getString("msgId"));
                        messageRecord.setMessage(inputItem.getString("q"));
                        messageRecord.setSender(externalUserId);
                        messageRecord.setReceiver(botUserId);
                        messageRecord.setIsUserSend(true);
                        messageRecord.setDateStamp(System.currentTimeMillis());
                        reqAiMessages.add(messageRecord);
                    }
                }
            }

            if (reqAiMessages.isEmpty()) {
                log.error("No message records found in async AI request: {}", esbMsgObj.toJSONString());
                return;
            }

            lbgAiReplyComponent.handleAsyncAiRequest(
                    corpId, botUserId, externalUserId, contactName, aiResult, reqAiMessages);

            log.info("Successfully processed async AI request for chatId: {}", chatId);

        } catch (Exception e) {
            log.error("Error processing async AI request: {}", e.getMessage(), e);
        }
    }

    /**
     * 从chatId中提取corpId
     */
    private String extractCorpIdFromChatId(String chatId) {
        try {
            LbgAiChatPartnersBo chatPartners = aiChatIdRepository.getChatPartnersBo(chatId);
            return chatPartners != null ? chatPartners.getCorpId() : null;
        } catch (Exception e) {
            log.error("Failed to extract corpId from chatId: {}", chatId, e);
            return null;
        }
    }

    /**
     * 从chatId中提取botUserId
     */
    private String extractBotUserIdFromChatId(String chatId) {
        try {
            LbgAiChatPartnersBo chatPartners = aiChatIdRepository.getChatPartnersBo(chatId);
            return chatPartners != null ? chatPartners.getBotUserId() : null;
        } catch (Exception e) {
            log.error("Failed to extract botUserId from chatId: {}", chatId, e);
            return null;
        }
    }

}
