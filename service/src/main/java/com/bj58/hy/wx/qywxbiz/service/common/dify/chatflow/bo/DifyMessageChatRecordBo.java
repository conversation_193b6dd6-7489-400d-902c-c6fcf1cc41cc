package com.bj58.hy.wx.qywxbiz.service.common.dify.chatflow.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DifyMessageChatRecordBo {

    /**
     * 消息的发送时间
     */
    @JSONField(name = "dateStamp") // fastjson
    @JsonProperty("dateStamp")// jackson
    private Long dateStamp;

    /**
     * 消息内容
     */
    @JSONField(name = "message") // fastjson
    @JsonProperty("message")// jackson
    private String message;

    /**
     * 发送人
     */
    @JSONField(name = "sender") // fastjson
    @JsonProperty("sender")// jackson
    private String sender;

    /**
     * 接收人
     */
    @JSONField(name = "receiver") // fastjson
    @JsonProperty("receiver")// jackson
    private String receiver;

    /**
     * 消息id
     */
    @JSONField(name = "messageId") // fastjson
    @JsonProperty("messageId")// jackson
    private String messageId;

    /**
     * @see  SingleChatSendBy
     */
    @JSONField(name = "sendBy") // fastjson
    @JsonProperty("isUserSend")// jackson
    private int sendBy;
}
