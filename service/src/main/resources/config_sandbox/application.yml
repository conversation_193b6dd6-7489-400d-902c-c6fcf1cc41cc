env: sandbox

server:
  port:
    8001
  servlet:
    context-path: /
  http2:
    enabled: true
  tomcat:
    max-swallow-size: 10MB

spring:
  application:
    name: hyqywxbiz
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    deserialization:
      fail-on-ignored-properties: false
  main:
    allow-bean-definition-overriding: true
  redis:
    password: 7f5bcc4f4094679c
    database: 0
    sentinel:
      master: rmaster46896
      nodes: hyqywx-sentinel-3.rdb.58dns.org:56896,hyqywx-sentinel-2.rdb.58dns.org:56896,hyqywx-sentinel-1.rdb.58dns.org:56896
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************
    username: hyqywx_priv
    password: 23b68fcd091e8571
    hikari:
      pool-name: hikariCP
      minimum-idle: 10
      maximum-pool-size: 20
      idle-timeout: 60000       # 1000 * 60 = 1m
      max-lifetime: 1800000     # 1000 * 60 * 30 = 30m (mysql wait_timeout value = 1h)
      validation-timeout: 900   # 1000 * 1 = 1s. this value must be less than the connectionTimeout
      connection-timeout: 5000  # 1000 * 2 = 2s
      connection-init-sql: "SET NAMES utf8mb4"
  jpa:
    database: mysql
    database-platform: org.hibernate.dialect.MySQL8Dialect
    open-in-view: false
    hibernate:
      ddl-auto: none
      query:
        in_clause_parameter_padding: true
  data:
    redis:
      repositories:
        enabled: false

scf:
  serializer:
    scan:
      extraSerializable: com.bj58.hy.wx.qywxbiz

  server:
    enabled: true
    name: hyqywxbiz
    tcp:
      listenPort: 17598
      frameMaxLength: 20971520
    srvmgr:
      controller:
        enabled: true
    scan:
      serviceBehavior: com.bj58.hy.wx.qywxbiz
  client:
    enabled: true
    key:
      file: srvmgr_huangye_wx_hyqywxbiz.scfkey
  jvm:
    options: -Xrunjdwp:transport=dt_socket,address=5005,server=y,suspend=n -Xms2g -Xmx2g -Xmn1g -Xss1024K -XX:ParallelGCThreads=4 -XX:+UseParNewGC -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=60


logging:
  config: config/log4j2-spring.xml

wmb:
  path: "/opt/service/huangye_wx_hyqywxbiz/config/hyqywxbiz_online.key"
  clients:
    qywx_event:
      subscribe:
        subject-id: 112372
        client-id: 10
        enabled: false
    juzi_event:
      subscribe:
        subject-id: 114378
        client-id: 4
        enabled: false
    lbg_ai_chat: # 异步AI托管(LBG算法)
      publish:
        subject-id: 114452
      subscribe:
        subject-id: 114452
        client-id: 1
        enabled: false
    dify_chatflow: # 异步AI应答(DIFY)
      publish:
        subject-id: 114782
      subscribe:
        subject-id: 114782
        client-id: 1
        enabled: false
    dify_workflow: # 异步AI工作流(DIFY)
      publish:
        subject-id: 114942
      subscribe:
        subject-id: 114942
        client-id: 1
        enabled: false
    group_event: # 建群结果
      subscribe:
          subject-id: 114430
          client-id: 5
          enabled: false
    jiafu_chat_ai_workflow: # 家服群聊AI应答工作流
      publish:
        subject-id: 114960
      subscribe:
        subject-id: 114960
        client-id: 1
        enabled: false
    async_ai_chat: # 异步AI客服
      subscribe:
        subject-id: 115168
        client-id: 1
        enabled: false

customer-ai:
  corpId: ww5cfa32107e9a1f20
  agentId: 1000065

wjob:
  executor:
    app-name: "hyqywxbiz-sandbox"
    port: 9999
    log-path: "./job"
    log-retention-days: -1
  admin-address: "http://wjob.58corp.com"

account-conf:
  city: "[{\"cityName\":\"北京\",\"cityId\":\"1\"},{\"cityName\":\"上海\",\"cityId\":\"2\"},{\"cityName\":\"贵阳\",\"cityId\":\"2015\"},{\"cityName\":\"龙口\",\"cityId\":\"233\"},{\"cityName\":\"秦皇岛\",\"cityId\":\"1078\"},{\"cityName\":\"昆明\",\"cityId\":\"541\"},{\"cityName\":\"济南\",\"cityId\":\"265\"},{\"cityName\":\"成都\",\"cityId\":\"102\"},{\"cityName\":\"青岛\",\"cityId\":\"122\"},{\"cityName\":\"石家庄\",\"cityId\":\"241\"},{\"cityName\":\"太原\",\"cityId\":\"740\"},{\"cityName\":\"天津\",\"cityId\":\"18\"},{\"cityName\":\"郑州\",\"cityId\":\"342\"},{\"cityName\":\"西安\",\"cityId\":\"483\"},{\"cityName\":\"重庆\",\"cityId\":\"37\"},{\"cityName\":\"常州\",\"cityId\":\"463\"},{\"cityName\":\"大连\",\"cityId\":\"147\"},{\"cityName\":\"哈尔滨\",\"cityId\":\"202\"},{\"cityName\":\"杭州\",\"cityId\":\"79\"},{\"cityName\":\"南京\",\"cityId\":\"172\"},{\"cityName\":\"合肥\",\"cityId\":\"837\"},{\"cityName\":\"长春\",\"cityId\":\"319\"},{\"cityName\":\"无锡\",\"cityId\":\"93\"},{\"cityName\":\"苏州\",\"cityId\":\"5\"},{\"cityName\":\"宁波\",\"cityId\":\"135\"},{\"cityName\":\"沈阳\",\"cityId\":\"188\"},{\"cityName\":\"东莞\",\"cityId\":\"413\"},{\"cityName\":\"佛山\",\"cityId\":\"222\"},{\"cityName\":\"福州\",\"cityId\":\"304\"},{\"cityName\":\"广州\",\"cityId\":\"3\"},{\"cityName\":\"海口\",\"cityId\":\"2053\"},{\"cityName\":\"南昌\",\"cityId\":\"669\"},{\"cityName\":\"南宁\",\"cityId\":\"845\"},{\"cityName\":\"厦门\",\"cityId\":\"606\"},{\"cityName\":\"深圳\",\"cityId\":\"4\"},{\"cityName\":\"武汉\",\"cityId\":\"158\"},{\"cityName\":\"长沙\",\"cityId\":\"414\"},{\"cityName\":\"中山\",\"cityId\":\"771\"},{\"cityName\":\"泉州\",\"cityId\":\"291\"},{\"cityName\":\"南通\",\"cityId\":\"394\"},{\"cityName\":\"盐城\",\"cityId\":\"613\"},{\"cityName\":\"聊城\",\"cityId\":\"882\"},{\"cityName\":\"扬州\",\"cityId\":\"637\"},{\"cityName\":\"大同\",\"cityId\":\"6964\"},{\"cityName\":\"滨州\",\"cityId\":\"944\"},{\"cityName\":\"三亚\",\"cityId\":\"2422\"},{\"cityName\":\"漳州\",\"cityId\":\"710\"},{\"cityName\":\"日照\",\"cityId\":\"3177\"},{\"cityName\":\"宜昌\",\"cityId\":\"858\"},{\"cityName\":\"枣庄\",\"cityId\":\"961\"},{\"cityName\":\"绍兴\",\"cityId\":\"355\"},{\"cityName\":\"德州\",\"cityId\":\"728\"},{\"cityName\":\"德阳\",\"cityId\":\"2373\"},{\"cityName\":\"石狮\",\"cityId\":\"296\"},{\"cityName\":\"晋江\",\"cityId\":\"297\"},{\"cityName\":\"溧阳\",\"cityId\":\"469\"},{\"cityName\":\"景德镇\",\"cityId\":\"2360\"},{\"cityName\":\"公主岭\",\"cityId\":\"10171\"},{\"cityName\":\"大庆\",\"cityId\":\"375\"},{\"cityName\":\"吉林\",\"cityId\":\"700\"},{\"cityName\":\"齐齐哈尔\",\"cityId\":\"5853\"},{\"cityName\":\"苍南\",\"cityId\":\"7579\"},{\"cityName\":\"嘉兴\",\"cityId\":\"497\"},{\"cityName\":\"乐清\",\"cityId\":\"13895\"},{\"cityName\":\"临海\",\"cityId\":\"407\"},{\"cityName\":\"平阳\",\"cityId\":\"7578\"},{\"cityName\":\"瑞安\",\"cityId\":\"13894\"},{\"cityName\":\"台州\",\"cityId\":\"403\"},{\"cityName\":\"温岭市\",\"cityId\":\"408\"},{\"cityName\":\"义乌\",\"cityId\":\"12221\"},{\"cityName\":\"慈溪\",\"cityId\":\"5334\"},{\"cityName\":\"东阳\",\"cityId\":\"536\"},{\"cityName\":\"衡水\",\"cityId\":\"993\"},{\"cityName\":\"惠州\",\"cityId\":\"722\"},{\"cityName\":\"金华\",\"cityId\":\"531\"},{\"cityName\":\"襄阳\",\"cityId\":\"891\"},{\"cityName\":\"余姚\",\"cityId\":\"5333\"},{\"cityName\":\"舟山\",\"cityId\":\"8470\"},{\"cityName\":\"诸暨\",\"cityId\":\"357\"},{\"cityName\":\"保定\",\"cityId\":\"424\"},{\"cityName\":\"邯郸\",\"cityId\":\"572\"},{\"cityName\":\"衡阳\",\"cityId\":\"914\"},{\"cityName\":\"锦州\",\"cityId\":\"2354\"},{\"cityName\":\"株洲\",\"cityId\":\"1086\"},{\"cityName\":\"蚌埠\",\"cityId\":\"3470\"},{\"cityName\":\"阜阳\",\"cityId\":\"2325\"},{\"cityName\":\"淮安\",\"cityId\":\"968\"},{\"cityName\":\"廊坊\",\"cityId\":\"772\"},{\"cityName\":\"连云港\",\"cityId\":\"2049\"},{\"cityName\":\"宿迁\",\"cityId\":\"2350\"},{\"cityName\":\"泰安\",\"cityId\":\"686\"},{\"cityName\":\"泰兴\",\"cityId\":\"696\"},{\"cityName\":\"泰州\",\"cityId\":\"693\"},{\"cityName\":\"潍坊\",\"cityId\":\"362\"},{\"cityName\":\"芜湖\",\"cityId\":\"2045\"},{\"cityName\":\"燕郊\",\"cityId\":\"12730\"},{\"cityName\":\"张家口\",\"cityId\":\"3328\"},{\"cityName\":\"镇江\",\"cityId\":\"645\"},{\"cityName\":\"安阳\",\"cityId\":\"1096\"},{\"cityName\":\"鞍山\",\"cityId\":\"523\"},{\"cityName\":\"包头\",\"cityId\":\"801\"},{\"cityName\":\"沧州\",\"cityId\":\"652\"},{\"cityName\":\"常德\",\"cityId\":\"872\"},{\"cityName\":\"赤峰\",\"cityId\":\"6700\"},{\"cityName\":\"东营\",\"cityId\":\"623\"},{\"cityName\":\"鄂尔多斯\",\"cityId\":\"2037\"},{\"cityName\":\"肥城\",\"cityId\":\"690\"},{\"cityName\":\"赣州\",\"cityId\":\"2363\"},{\"cityName\":\"桂林\",\"cityId\":\"1039\"},{\"cityName\":\"江门\",\"cityId\":\"629\"},{\"cityName\":\"焦作\",\"cityId\":\"3266\"},{\"cityName\":\"金坛\",\"cityId\":\"468\"},{\"cityName\":\"九江\",\"cityId\":\"2247\"},{\"cityName\":\"开封\",\"cityId\":\"2342\"},{\"cityName\":\"垦利\",\"cityId\":\"11288\"},{\"cityName\":\"莱州\",\"cityId\":\"235\"},{\"cityName\":\"临沂\",\"cityId\":\"505\"},{\"cityName\":\"柳州\",\"cityId\":\"7133\"},{\"cityName\":\"泸州\",\"cityId\":\"2372\"},{\"cityName\":\"洛阳\",\"cityId\":\"556\"},{\"cityName\":\"绵阳\",\"cityId\":\"1057\"},{\"cityName\":\"南充\",\"cityId\":\"2378\"},{\"cityName\":\"南阳\",\"cityId\":\"592\"},{\"cityName\":\"蓬莱\",\"cityId\":\"237\"},{\"cityName\":\"平顶山\",\"cityId\":\"1005\"},{\"cityName\":\"莆田\",\"cityId\":\"2429\"},{\"cityName\":\"濮阳\",\"cityId\":\"2346\"},{\"cityName\":\"荣成\",\"cityId\":\"522\"},{\"cityName\":\"汕头\",\"cityId\":\"783\"},{\"cityName\":\"商丘\",\"cityId\":\"1029\"},{\"cityName\":\"上饶\",\"cityId\":\"10116\"},{\"cityName\":\"寿光\",\"cityId\":\"369\"},{\"cityName\":\"顺德\",\"cityId\":\"8694\"},{\"cityName\":\"湘潭\",\"cityId\":\"2047\"},{\"cityName\":\"新乡\",\"cityId\":\"1016\"},{\"cityName\":\"兴化\",\"cityId\":\"699\"},{\"cityName\":\"许昌\",\"cityId\":\"977\"},{\"cityName\":\"宜宾\",\"cityId\":\"2380\"},{\"cityName\":\"岳阳\",\"cityId\":\"821\"},{\"cityName\":\"湛江\",\"cityId\":\"791\"},{\"cityName\":\"诸城\",\"cityId\":\"9124\"},{\"cityName\":\"遵义\",\"cityId\":\"7624\"},{\"cityName\":\"其他\",\"cityId\":\"2258\"},{\"cityName\":\"昆山市\",\"cityId\":\"16\"},{\"cityName\":\"乌鲁木齐\",\"cityId\":\"984\"},{\"cityName\":\"温州\",\"cityId\":\"330\"},{\"cityName\":\"唐山\",\"cityId\":\"276\"},{\"cityName\":\"烟台\",\"cityId\":\"228\"},{\"cityName\":\"济宁\",\"cityId\":\"450\"},{\"cityName\":\"兰州\",\"cityId\":\"952\"},{\"cityName\":\"银川\",\"cityId\":\"2054\"},{\"cityName\":\"淄博\",\"cityId\":\"385\"},{\"cityName\":\"徐州\",\"cityId\":\"471\"},{\"cityName\":\"呼和浩特\",\"cityId\":\"811\"},{\"cityName\":\"珠海\",\"cityId\":\"910\"},{\"cityName\":\"西宁\",\"cityId\":\"2052\"},{\"cityName\":\"威海\",\"cityId\":\"518\"},{\"cityName\":\"拉萨\",\"cityId\":\"2055\"}]"

wconfig:
  client:
    secret: b3e93233b01579f8520f863be318414c
    group-name: sandbox_group           # 默认分组名称 default_group
    local-file-folder: /tmp/wconfig-files/   # 默认本地备份文件路径 /tmp/wconfig-files/
    agent-dev-mode: false # 连接测试环境公共 agent 的开发模式
