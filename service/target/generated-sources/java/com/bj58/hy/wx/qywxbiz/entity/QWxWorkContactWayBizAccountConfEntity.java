package com.bj58.hy.wx.qywxbiz.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QWxWorkContactWayBizAccountConfEntity is a Querydsl query type for WxWorkContactWayBizAccountConfEntity
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QWxWorkContactWayBizAccountConfEntity extends EntityPathBase<WxWorkContactWayBizAccountConfEntity> {

    private static final long serialVersionUID = 867948387L;

    public static final QWxWorkContactWayBizAccountConfEntity wxWorkContactWayBizAccountConfEntity = new QWxWorkContactWayBizAccountConfEntity("wxWorkContactWayBizAccountConfEntity");

    public final com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable _super = new com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable(this);

    public final NumberPath<Integer> bizLine = createNumber("bizLine", Integer.class);

    public final NumberPath<Integer> bizScene = createNumber("bizScene", Integer.class);

    public final StringPath cityId = createString("cityId");

    public final StringPath corpId = createString("corpId");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final NumberPath<Integer> rate = createNumber("rate", Integer.class);

    public final NumberPath<Integer> rateInterval = createNumber("rateInterval", Integer.class);

    public final NumberPath<Integer> rateIntervalUnit = createNumber("rateIntervalUnit", Integer.class);

    public final NumberPath<Integer> state = createNumber("state", Integer.class);

    public final NumberPath<Integer> aiState = createNumber("aiState", Integer.class);

    public final DateTimePath<java.util.Date> updateTime = createDateTime("updateTime", java.util.Date.class);

    public final StringPath userId = createString("userId");

    public QWxWorkContactWayBizAccountConfEntity(String variable) {
        super(WxWorkContactWayBizAccountConfEntity.class, forVariable(variable));
    }

    public QWxWorkContactWayBizAccountConfEntity(Path<? extends WxWorkContactWayBizAccountConfEntity> path) {
        super(path.getType(), path.getMetadata());
    }

    public QWxWorkContactWayBizAccountConfEntity(PathMetadata metadata) {
        super(WxWorkContactWayBizAccountConfEntity.class, metadata);
    }

}

