package com.bj58.hy.wx.qywxbiz.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QAutoMakeAuntBizTagDpEntity is a Querydsl query type for AutoMakeAuntBizTagDpEntity
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QAutoMakeAuntBizTagDpEntity extends EntityPathBase<AutoMakeAuntBizTagDpEntity> {

    private static final long serialVersionUID = 1820092590L;

    public static final QAutoMakeAuntBizTagDpEntity autoMakeAuntBizTagDpEntity = new QAutoMakeAuntBizTagDpEntity("autoMakeAuntBizTagDpEntity");

    public final com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable _super = new com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable(this);

    public final NumberPath<Integer> age = createNumber("age", Integer.class);

    public final StringPath aunt_service_level = createString("aunt_service_level");

    public final StringPath background_check_status = createString("background_check_status");

    public final StringPath bindingMerchantType = createString("bindingMerchantType");

    public final StringPath crowdsourcingFinishNum = createString("crowdsourcingFinishNum");

    public final StringPath crowdsourcingLastFinishIntervalDays = createString("crowdsourcingLastFinishIntervalDays");

    public final NumberPath<Long> externalUser58Id = createNumber("externalUser58Id", Long.class);

    public final StringPath finishNum = createString("finishNum");

    public final StringPath gender = createString("gender");

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final StringPath importDate = createString("importDate");

    public final StringPath joinInStatus = createString("joinInStatus");

    public final StringPath last_10_baojie_order_complaint_rate = createString("last_10_baojie_order_complaint_rate");

    public final StringPath last_10_baojie_order_service_rate = createString("last_10_baojie_order_service_rate");

    public final StringPath last_order_pre7day_finish_order_num = createString("last_order_pre7day_finish_order_num");

    public final StringPath last_order_pre7day_zhongbao_finish_order_num = createString("last_order_pre7day_zhongbao_finish_order_num");

    public final StringPath medical_status = createString("medical_status");

    public final NumberPath<Integer> new_zhongdiangong_aunt = createNumber("new_zhongdiangong_aunt", Integer.class);

    public final StringPath receivingStatus = createString("receivingStatus");

    public final StringPath serviceCate4Ids = createString("serviceCate4Ids");

    public final StringPath serviceCity1Name = createString("serviceCity1Name");

    public final NumberPath<Integer> zhongdiangong_aunt = createNumber("zhongdiangong_aunt", Integer.class);

    public final NumberPath<Integer> zhongdiangong_finish_order_num = createNumber("zhongdiangong_finish_order_num", Integer.class);

    public QAutoMakeAuntBizTagDpEntity(String variable) {
        super(AutoMakeAuntBizTagDpEntity.class, forVariable(variable));
    }

    public QAutoMakeAuntBizTagDpEntity(Path<? extends AutoMakeAuntBizTagDpEntity> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAutoMakeAuntBizTagDpEntity(PathMetadata metadata) {
        super(AutoMakeAuntBizTagDpEntity.class, metadata);
    }

}

