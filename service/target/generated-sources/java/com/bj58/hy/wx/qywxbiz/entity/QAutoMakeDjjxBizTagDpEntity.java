package com.bj58.hy.wx.qywxbiz.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QAutoMakeDjjxBizTagDpEntity is a Querydsl query type for AutoMakeDjjxBizTagDpEntity
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QAutoMakeDjjxBizTagDpEntity extends EntityPathBase<AutoMakeDjjxBizTagDpEntity> {

    private static final long serialVersionUID = 604747400L;

    public static final QAutoMakeDjjxBizTagDpEntity autoMakeDjjxBizTagDpEntity = new QAutoMakeDjjxBizTagDpEntity("autoMakeDjjxBizTagDpEntity");

    public final com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable _super = new com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable(this);

    public final NumberPath<Long> externalUser58Id = createNumber("externalUser58Id", Long.class);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final NumberPath<Integer> operationType = createNumber("operationType", Integer.class);

    public final StringPath tagGroupName = createString("tagGroupName");

    public final StringPath tagName = createString("tagName");

    public QAutoMakeDjjxBizTagDpEntity(String variable) {
        super(AutoMakeDjjxBizTagDpEntity.class, forVariable(variable));
    }

    public QAutoMakeDjjxBizTagDpEntity(Path<? extends AutoMakeDjjxBizTagDpEntity> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAutoMakeDjjxBizTagDpEntity(PathMetadata metadata) {
        super(AutoMakeDjjxBizTagDpEntity.class, metadata);
    }

}

