package com.bj58.hy.wx.qywxbiz.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QBizDictEntity is a Querydsl query type for BizDictEntity
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QBizDictEntity extends EntityPathBase<BizDictEntity> {

    private static final long serialVersionUID = 1970013003L;

    public static final QBizDictEntity bizDictEntity = new QBizDictEntity("bizDictEntity");

    public final com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable _super = new com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable(this);

    public final NumberPath<Integer> bizLine = createNumber("bizLine", Integer.class);

    public final StringPath bizName = createString("bizName");

    public final NumberPath<Integer> bizScene = createNumber("bizScene", Integer.class);

    public final StringPath corpId = createString("corpId");

    //inherited
    public final NumberPath<Long> id = _super.id;

    public QBizDictEntity(String variable) {
        super(BizDictEntity.class, forVariable(variable));
    }

    public QBizDictEntity(Path<? extends BizDictEntity> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBizDictEntity(PathMetadata metadata) {
        super(BizDictEntity.class, metadata);
    }

}

