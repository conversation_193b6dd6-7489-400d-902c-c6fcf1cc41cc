package com.bj58.hy.wx.qywxbiz.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;


/**
 * QAccountConfOperatorLogEntity is a Querydsl query type for AccountConfOperatorLogEntity
 */
@Generated("com.querydsl.codegen.EntitySerializer")
public class QAccountConfOperatorLogEntity extends EntityPathBase<AccountConfOperatorLogEntity> {

    private static final long serialVersionUID = 964301971L;

    public static final QAccountConfOperatorLogEntity accountConfOperatorLogEntity = new QAccountConfOperatorLogEntity("accountConfOperatorLogEntity");

    public final com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable _super = new com.bj58.hy.lib.spring.support.jpa.superclass.QIdentifiable(this);

    public final NumberPath<Long> accountConfId = createNumber("accountConfId", Long.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    //inherited
    public final NumberPath<Long> id = _super.id;

    public final StringPath log = createString("log");

    public final StringPath operator = createString("operator");

    public final DateTimePath<java.util.Date> updateTime = createDateTime("updateTime", java.util.Date.class);

    public QAccountConfOperatorLogEntity(String variable) {
        super(AccountConfOperatorLogEntity.class, forVariable(variable));
    }

    public QAccountConfOperatorLogEntity(Path<? extends AccountConfOperatorLogEntity> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAccountConfOperatorLogEntity(PathMetadata metadata) {
        super(AccountConfOperatorLogEntity.class, metadata);
    }

}

