package com.bj58.hy.wx.qywxbiz.contract.dto.csc_auth;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class GetCscAuthInfoReq {

    /**
     * 企业
     */
    @NotEmpty
    @SCFMember(orderId = 1)
    private String corpId;

    /**
     * 企业成员唯一标识
     */
    @NotEmpty
    @SCFMember(orderId = 2)
    private String userId;

    /**
     * 外部联系人唯一标识
     */
    @NotEmpty
    @SCFMember(orderId = 3)
    private String externalUserId;

}
