package com.bj58.hy.wx.qywxbiz.contract;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.contract.constants.GlobalConstants;
import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.GetRecommendedOwnerIdReq;
import com.bj58.spat.scf.server.contract.annotation.OperationContract;
import com.bj58.spat.scf.server.contract.annotation.ServiceContract;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@ServiceContract
public interface IGroupChatService {

    String SCF_URL = GlobalConstants.SCF_URL_PREFIX + "/GroupChatService";

    /**
     * 根据业务线，返回一个建议的群主
     */
    @OperationContract
    Result<String> getRecommendedOwnerId(@NotNull @Valid final GetRecommendedOwnerIdReq req) throws Exception;

}
