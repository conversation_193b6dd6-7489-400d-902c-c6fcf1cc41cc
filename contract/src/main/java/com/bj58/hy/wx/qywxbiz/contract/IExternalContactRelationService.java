package com.bj58.hy.wx.qywxbiz.contract;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.contract.constants.GlobalConstants;
import com.bj58.hy.wx.qywxbiz.contract.dto.external_contact.ExternalContactRelationResp;
import com.bj58.spat.scf.server.contract.annotation.OperationContract;
import com.bj58.spat.scf.server.contract.annotation.ServiceContract;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

@ServiceContract
public interface IExternalContactRelationService {

    String SCF_URL = GlobalConstants.SCF_URL_PREFIX + "/ExternalContactRelationService";

    /**
     * 获取多个客服与当前客户的关系
     */
    @OperationContract
    Result<ExternalContactRelationResp> getAllExternalRelationWithUsers(@NotEmpty final String corpId,
                                                                        @NotNull final Set<String> userIds,
                                                                        @NotEmpty final String externalUserId) throws Exception;

}
