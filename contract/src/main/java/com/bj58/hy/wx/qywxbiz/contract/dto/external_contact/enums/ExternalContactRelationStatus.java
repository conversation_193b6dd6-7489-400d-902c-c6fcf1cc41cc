package com.bj58.hy.wx.qywxbiz.contract.dto.external_contact.enums;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.Getter;

import java.util.Optional;

@Getter
@SCFSerializable
public enum ExternalContactRelationStatus {

    /**
     * 不存在关系
     */
    NO_RELATION(0),

    /**
     * 可用的
     */
    ENABLE(1),

    /**
     * 已删除
     */
    DELETED(-1),

    /**
     * 句子已删除，实际未删除
     */
    DELETE_BY_JUZI(-2),
    ;

    private final int code;

    ExternalContactRelationStatus(int code) {
        this.code = code;
    }

    public static ExternalContactRelationStatus of(Integer code) {
        if (ObjectUtils.isNull(code)) {
            return null;
        }

        for (ExternalContactRelationStatus source : ExternalContactRelationStatus.values()) {
            if (source.getCode() == code) {
                return source;
            }
        }

        return null;
    }

    public static ExternalContactRelationStatus strictOf(Integer code) {
        return Optional.ofNullable(of(code))
                .orElseThrow(() -> new UnsupportedOperationException(
                        String.format("unsupported enum, value = %s", code)));
    }
}
