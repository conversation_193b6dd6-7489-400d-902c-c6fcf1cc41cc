package com.bj58.hy.wx.qywxbiz.contract;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.contract.constants.GlobalConstants;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.*;
import com.bj58.spat.scf.server.contract.annotation.OperationContract;
import com.bj58.spat.scf.server.contract.annotation.ServiceContract;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/19 10:48
 */
@ServiceContract
public interface IContactWayService {

    String SCF_URL = GlobalConstants.SCF_URL_PREFIX + "/ContactWayService";

    @OperationContract
    Result<ContactWayResp> getContactWay(@NotNull @Valid final ContactWayReq req) throws Exception;

    /**
     * 获取 指定业务线、指定业务场景 所有的可用客服
     */
    @OperationContract
    Result<Set<String>> getAvailableUserIds(@NotEmpty final String corpId,
                                            @NotNull final Integer bizLine,
                                            @NotEmpty final List<Integer> bizScenes) throws Exception;

    /**
     * 查询客服列表
     */
    @OperationContract
    Result<UserListResp> listUsers(@NotNull final UserListReq req) throws Exception;


    /**
     * 添加客服
     */
    @OperationContract
    Result<String> addUsers(@NotNull @Valid final AddUsersReq req) throws Exception;

    /**
     * 更新客服信息
     */
    @OperationContract
    Result<String> updateUserInfo(@NotNull @Valid final UpdateUserInfoReq req) throws Exception;

    /**
     * 查询操作日志列表
     */
    @OperationContract
    Result<OperatorLogListResp> listOperatorLog(@NotNull @Valid final Long id,
                                                Integer pageNum,
                                                Integer pageSize) throws Exception;

    /**
     * 查询城市列表
     */
    @OperationContract
    Result<List<CityListResp>> listCity(String cityName) throws Exception;

    /**
     * 通过美事卡片操作状态
     * type为1时代表拒绝
     * type为2时代表下线
     */
    @OperationContract
    Result<String> updateUserStateFromCard(@NotEmpty final String operator,
                                           @NotNull final Integer type,
                                           @NotNull final Long userConfigId) throws Exception;

    /**
     * 企微账户状态报警
     */
    @OperationContract
    Result<String> warningUserState(@NotEmpty final String corpId,
                                    @NotNull final Integer bizLine,
                                    @NotNull final Integer bizScenes,
                                    @NotEmpty final String userId) throws Exception;

    /**
     * 根据id获取二维码信息
     */
    @OperationContract
    Result<ContactWayResp> getQrCodeById(@NotEmpty final String corpId,
                                    @NotNull final Long id) throws Exception;

    /**
     * 获取渠道枚举
     * @return
     */
    @OperationContract
    Result<List<BizTypeResp>> getBizType();

    /**
     * 更新客服账号AI状态
     */
    @OperationContract
    Result<String> updateUserAiState(String operator, Long id, Integer state);

    /**
     * 删除客服
     */
    @OperationContract
    Result<String> deleteUser(Long id);

    @OperationContract
    Result<ContactWayForHyResp> getContactWayForHy(@NotNull @Valid ContactWayForHyReq req);

}
