package com.bj58.hy.wx.qywxbiz.contract;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.contract.constants.GlobalConstants;
import com.bj58.hy.wx.qywxbiz.contract.dto.csc_auth.GetCscAuthInfoReq;
import com.bj58.hy.wx.qywxbiz.contract.dto.csc_auth.GetCscAuthInfoResp;
import com.bj58.spat.scf.server.contract.annotation.OperationContract;
import com.bj58.spat.scf.server.contract.annotation.ServiceContract;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * CSC后台，权限相关
 */
@ServiceContract
public interface ICscAuthService {

    String SCF_URL = GlobalConstants.SCF_URL_PREFIX + "/CscAuthService";

    /**
     * 获取 CSC 权限相关信息
     */
    @OperationContract
    Result<GetCscAuthInfoResp> getCscAuthInfo(@NotNull @Valid final GetCscAuthInfoReq req) throws Exception;
}
