package com.bj58.hy.wx.qywxbiz.contract.dto.group_chat;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * 邀请外部联系人加入群聊返回
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class InviteExternalUserToGroupResp {

    /**
     * 结果id
     */
    @NotEmpty
    @SCFMember(orderId = 1)
    private String resultId;

    /**
     * 群ID，如果是邀请的时候，进行返回
     */
    @NotEmpty
    @SCFMember(orderId = 2)
    private String chatId;

}