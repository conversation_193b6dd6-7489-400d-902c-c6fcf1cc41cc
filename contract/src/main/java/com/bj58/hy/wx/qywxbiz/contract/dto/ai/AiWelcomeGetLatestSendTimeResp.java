package com.bj58.hy.wx.qywxbiz.contract.dto.ai;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@SCFSerializable
@AllArgsConstructor
@NoArgsConstructor
public class AiWelcomeGetLatestSendTimeResp {

    /**
     * 各个业务线对应的AI欢迎语时间
     */
    @SCFMember(orderId = 1)
    private List<Item> aiWelcomeSendTimes;

    @Data
    @SCFSerializable
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Item {

        @SCFMember(orderId = 1)
        private Integer bizScene;

        @SCFMember(orderId = 2)
        private Long timestamp;

    }

}
