package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class CityListResp {

    /**
     * 城市id
     */
    @SCFMember(orderId = 1)
    private String cityId;

    /**
     * 城市名称
     */
    @SCFMember(orderId = 2)
    private String cityName;

}
