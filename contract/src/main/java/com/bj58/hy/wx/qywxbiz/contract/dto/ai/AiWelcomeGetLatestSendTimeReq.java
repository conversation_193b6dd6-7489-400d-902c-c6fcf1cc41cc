package com.bj58.hy.wx.qywxbiz.contract.dto.ai;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

@Data
@NoArgsConstructor
@SCFSerializable
public class AiWelcomeGetLatestSendTimeReq {

    /**
     * 企业微信唯一标识
     */
    @NotEmpty
    @SCFMember(orderId = 1)
    private String corpId;

    /**
     * 业务线
     */
    @javax.validation.constraints.NotNull
    @SCFMember(orderId = 2)
    private Integer bizLine;

    /**
     * 业务场景
     */
    @javax.validation.constraints.NotNull
    @SCFMember(orderId = 3)
    private Set<Integer> bizScene;

    /**
     * 客服id
     */
    @NotEmpty
    @SCFMember(orderId = 4)
    private String userId;

    /**
     * 用户id
     */
    @NotEmpty
    @SCFMember(orderId = 5)
    private String externalUserId;

}
