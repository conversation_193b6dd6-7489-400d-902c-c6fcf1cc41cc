package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@SCFSerializable(name = "com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayResp")
public class ContactWayForHyResp {


    /**
     * 已有好友关系的userId
     */
    @SCFMember(orderId = 1)
    private String userId;

    /**
     * 获客链接
     */
    @SCFMember(orderId = 2)
    private String linkUrl;

    /**
     * 是否有好友关系
     * true: 已有好友关系，false: 无好友关系使用轮询
     */
    @SCFMember(orderId = 3)
    private Boolean hasFriendRelation;

    /**
     * 外部联系人ID（当有好友关系时返回）
     */
    @SCFMember(orderId = 4)
    private String externalUserId;
}
