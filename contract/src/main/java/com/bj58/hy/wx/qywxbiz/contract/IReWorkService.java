package com.bj58.hy.wx.qywxbiz.contract;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.contract.constants.GlobalConstants;
import com.bj58.hy.wx.qywxbiz.contract.dto.rework.ReWorkPushReq;
import com.bj58.spat.scf.server.contract.annotation.OperationContract;
import com.bj58.spat.scf.server.contract.annotation.ServiceContract;

/**
 * <AUTHOR>
 * @date 2025/3/31 14:02
 */
@ServiceContract
public interface IReWorkService {

    String SCF_URL = GlobalConstants.SCF_URL_PREFIX + "/ReWorkService";

    /**
     * 不满意重做 多场景push触发
     * @param reWorkPushReq
     * @return
     */
    @OperationContract
    public Result<String> reWorkPush(ReWorkPushReq reWorkPushReq);
}
