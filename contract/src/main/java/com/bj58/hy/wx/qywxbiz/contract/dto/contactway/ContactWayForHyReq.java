package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@NoArgsConstructor
@SCFSerializable
public class ContactWayForHyReq {

    /**
     * 企业微信唯一标识
     */
    @NotEmpty
    @SCFMember(orderId = 1)
    private String corpId;

    /**
     * 业务线
     */
    @javax.validation.constraints.NotNull
    @SCFMember(orderId = 2)
    private Integer bizLine;

    /**
     * 业务场景
     */
    @javax.validation.constraints.NotNull
    @SCFMember(orderId = 3)
    private Integer bizScene;

    /**
     * 城市id
     */
    @SCFMember(orderId = 4)
    private Integer cityId;

    /**
     * 绑定的58用户ID，用于查询客服好友关系
     */
    @SCFMember(orderId = 5)
    private Long bindWuBa;

    /**
     * 绑定的业务key，用于查询客服好友关系
     */
    @SCFMember(orderId = 6)
    private String bindBiz;

}
