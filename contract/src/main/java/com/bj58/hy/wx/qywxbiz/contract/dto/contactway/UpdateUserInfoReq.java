package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class UpdateUserInfoReq {

    /**
     * 企业id
     */
    @NotEmpty
    @SCFMember(orderId = 1)
    private String corpId;

    /**
     * 用户id
     */
    @SCFMember(orderId = 2)
    private String userId;

    /**
     * 城市
     */
    @SCFMember(orderId = 3)
    private Integer cityId;

    /**
     * 业务线
     */
    @NotNull
    @SCFMember(orderId = 4)
    private Integer bizLine;

    /**
     * 业务场景
     */
    @NotEmpty
    @SCFMember(orderId = 5)
    private List<Integer> bizScene;

    /**
     * 操作人
     */
    @NotEmpty
    @SCFMember(orderId = 6)
    private String operator;

    /**
     * 状态
     */
    @SCFMember(orderId = 7)
    private Integer state;

    /**
     * id
     */
    @NotNull
    @SCFMember(orderId = 8)
    private Long id;

    /**
     * 城市id集合
     */
    @SCFMember(orderId = 9)
    private String cityIds;

}