package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/11/19 11:03
 */
@Data
@NoArgsConstructor
@SCFSerializable
public class ContactWayReq {

    /**
     * 企业微信唯一标识
     */
    @NotEmpty
    @SCFMember(orderId = 1)
    private String corpId;

    /**
     * 业务线
     */
    @javax.validation.constraints.NotNull
    @SCFMember(orderId = 2)
    private Integer bizLine;

    /**
     * 业务场景
     */
    @javax.validation.constraints.NotNull
    @SCFMember(orderId = 3)
    private Integer bizScene;

    /**
     * 渠道参数，最多30个字符若存在超长渠道参数，业务侧可提前调用渠道参数映射接口进行压缩
     */
    @NotEmpty
    @SCFMember(orderId = 4)
    private String state;

    /**
     * 城市id
     */
    @SCFMember(orderId = 5)
    private Integer cityId;
}
