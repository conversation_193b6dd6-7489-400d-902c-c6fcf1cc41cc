package com.bj58.hy.wx.qywxbiz.contract.dto.group_chat;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * 邀请外部联系人加入群聊请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class InviteExternalUserToGroupReq {

    /**
     * 企业微信唯一标识
     */
    @NotEmpty
    @SCFMember(orderId = 1)
    private String corpId;

    /**
     * 群主ID（句子账号）
     */
    @NotEmpty
    @SCFMember(orderId = 2)
    private String userId;

    /**
     * 外部联系人ID
     */
    @NotEmpty
    @SCFMember(orderId = 3)
    private String externalUserId;
    
    /**
     * 城市名称（用于群名称）
     */
    @SCFMember(orderId = 4)
    private String cityName;
} 