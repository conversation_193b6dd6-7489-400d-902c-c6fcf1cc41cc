package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025/1/11 14:43
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class BizTypeResp {

    @SCFMember(orderId = 1)
    private String typeName;
    @SCFMember(orderId = 2)
    private String type;
}
