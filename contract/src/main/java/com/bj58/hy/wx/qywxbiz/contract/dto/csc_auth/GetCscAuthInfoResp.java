package com.bj58.hy.wx.qywxbiz.contract.dto.csc_auth;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class GetCscAuthInfoResp {

    /**
     * 企业成员的OA名称
     */
    @SCFMember(orderId = 1)
    private String userOa;

    /**
     * 外部联系人对应的58id
     * 可能为空
     */
    @SCFMember(orderId = 2)
    private Long externalUser58Id;


}
