package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/28 14:37
 */
@Data
@NoArgsConstructor
@SCFSerializable(name = "com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayResp")
public class ContactWayResp {


    /**
     * 生成二维码的userId
     */
    @SCFMember(orderId = 1)
    private String userId;

    /**
     * 联系我二维码链接
     */
    @SCFMember(orderId = 2)
    private String qrCode;
}
