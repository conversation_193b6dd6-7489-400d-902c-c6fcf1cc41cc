package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class AddUsersReq {

    /**
     * 企业id
     */
    @NotEmpty
    @SCFMember(orderId = 1)
    private String corpId;

    /**
     * 用户id
     */
    @NotEmpty
    @SCFMember(orderId = 2)
    private List<String> userIds;

    /**
     * 城市
     */
    @NotNull
    @SCFMember(orderId = 3)
    private Integer cityId;

    /**
     * 业务线
     */
    @NotNull
    @SCFMember(orderId = 4)
    private Integer bizLine;

    /**
     * 业务场景
     */
    @NotNull
    @SCFMember(orderId = 5)
    private Integer bizScene;

    /**
     * 操作人
     */
    @NotEmpty
    @SCFMember(orderId = 6)
    private String operator;

}
