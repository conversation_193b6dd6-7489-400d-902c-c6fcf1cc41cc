package com.bj58.hy.wx.qywxbiz.contract.dto.external_contact;

import com.bj58.hy.wx.qywxbiz.contract.dto.external_contact.enums.ExternalContactRelationStatus;
import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class ExternalContactRelationResp {

    /**
     * 联系方式的配置id
     */
    @SCFMember(orderId = 1)
    private Set<Item> relations;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SCFSerializable
    public static class Item {

        /**
         * 企业成员id
         */
        @SCFMember(orderId = 1)
        private String userId;

        /**
         * 外部联系人状态
         * @see ExternalContactRelationStatus
         */
        @SCFMember(orderId = 2)
        private Integer relationStatus;

    }

}
