package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class OperatorLogListResp {

    /**
     * 总数
     */
    @SCFMember(orderId = 1)
    private Long totalCount;

    /**
     * 列表信息
     */
    @SCFMember(orderId = 2)
    private List<Item> list;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SCFSerializable
    public static class Item {

        /**
         * 操作人
         */
        @SCFMember(orderId = 1)
        private String operatorName;

        /**
         * 操作时间
         */
        @SCFMember(orderId = 2)
        private Date operatorTime;

        /**
         * 日志
         */
        @SCFMember(orderId = 3)
        private String operatorLog;

        /**
         * id  前端用于校验数据唯一 防止重复数据报错
         */
        @SCFMember(orderId = 4)
        private Long id;

    }

}
