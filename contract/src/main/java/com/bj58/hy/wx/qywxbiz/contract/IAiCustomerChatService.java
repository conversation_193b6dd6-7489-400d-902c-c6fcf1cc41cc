package com.bj58.hy.wx.qywxbiz.contract;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.contract.constants.GlobalConstants;
import com.bj58.hy.wx.qywxbiz.contract.dto.ai.AiWelcomeGetLatestSendTimeReq;
import com.bj58.hy.wx.qywxbiz.contract.dto.ai.AiWelcomeGetLatestSendTimeResp;
import com.bj58.spat.scf.server.contract.annotation.OperationContract;
import com.bj58.spat.scf.server.contract.annotation.ServiceContract;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

@ServiceContract
public interface IAiCustomerChatService {

    String SCF_URL = GlobalConstants.SCF_URL_PREFIX + "/AiCustomerChatService";

    /**
     * 查询业务数据 通用的
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    @OperationContract
    Result<Object> queryBizData(@NotNull @Valid final Map<String, Object> queryParam) throws Exception;

    /**
     * 获取最近一次发送AI欢迎语的时间
     */
    @OperationContract
    Result<AiWelcomeGetLatestSendTimeResp> aiWelcomeGetLatestSendTime(@NotNull @Valid final AiWelcomeGetLatestSendTimeReq req) throws Exception;

}
