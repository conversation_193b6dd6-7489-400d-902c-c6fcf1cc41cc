package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class UserListResp {

    /**
     * 总数
     */
    @SCFMember(orderId = 1)
    private Long totalCount;

    /**
     * 列表信息
     */
    @SCFMember(orderId = 2)
    private List<Item> list;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SCFSerializable
    public static class Item {

        /**
         * 城市id
         */
        @SCFMember(orderId = 1)
        private String cityId;

        /**
         * 城市名称
         */
        @SCFMember(orderId = 2)
        private String cityName;

        /**
         * id
         */
        @SCFMember(orderId = 3)
        private Long Id;

        /**
         * 企微账户新增时间
         */
        @SCFMember(orderId = 4)
        private Date createTime;

        /**
         * 状态，选项为：0-待上线、1-已上线；
         */
        @SCFMember(orderId = 5)
        private Integer state;

        /**
         * 状态名称
         */
        @SCFMember(orderId = 6)
        private String stateName;

        /**
         * 业务线类型
         */
        @SCFMember(orderId = 7)
        private Integer type;

        /**
         * 业务线类型名称
         */
        @SCFMember(orderId = 8)
        private String typeName;

        /**
         * 更新时间
         */
        @SCFMember(orderId = 9)
        private Date updateTime;

        /**
         * 用户id
         */
        @SCFMember(orderId = 10)
        private String userId;

        /**
         * 客户数（不排除已删除）
         */
        @SCFMember(orderId = 11)
        private Long addCustomerCount;

        /**
         * 当日新增客户数（不排除已删除）
         */
        @SCFMember(orderId = 12)
        private Long todayAddCustomerCount;

        /**
         * 客服名称
         */
        @SCFMember(orderId = 13)
        private String userName;

        /**
         * 账号二维码
         */
        @SCFMember(orderId = 14)
        private String qrCode;

        /**
         * 账号AI状态,0-待上线，1-已上线
         */
        @SCFMember(orderId = 15)
        private Integer aiState;

    }

}
