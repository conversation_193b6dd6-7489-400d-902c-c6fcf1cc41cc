package com.bj58.hy.wx.qywxbiz.contract.dto.group_chat;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class GetRecommendedOwnerIdReq {

    @NotEmpty
    @SCFMember(orderId = 1)
    private String corpId;

    @NotNull
    @SCFMember(orderId = 2)
    private Integer bizLine;

    @NotNull
    @SCFMember(orderId = 3)
    private Integer bizScene;

    /**
     * 当无可建议值时，根据业务线和场景规则，可能会返回业务侧默认的群主
     */
    @SCFMember(orderId = 4)
    private String defaultOwnerId;

}
