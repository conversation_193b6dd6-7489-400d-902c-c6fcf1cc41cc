package com.bj58.hy.wx.qywxbiz.contract;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.contract.constants.GlobalConstants;
import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.InviteExternalUserToGroupReq;
import com.bj58.hy.wx.qywxbiz.contract.dto.group_chat.InviteExternalUserToGroupResp;
import com.bj58.spat.scf.server.contract.annotation.OperationContract;
import com.bj58.spat.scf.server.contract.annotation.ServiceContract;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 群成员邀请服务接口，家服业务使用
 */
@ServiceContract
public interface IGroupMemberInviteService {

    String SCF_URL = GlobalConstants.SCF_URL_PREFIX + "/GroupMemberInviteService";

    /**
     * 将外部联系人拉入群聊
     * 根据群主ID，获取对应账号创建的群聊，并轮询查找当前可用的群，将外部联系人拉入群中。
     * 当群人数超过 180 人时，标记该群不可继续拉人。
     * 若无可用群，则自动创建新群，并邀请当前外部联系人加入。
     */
    @OperationContract
    Result<InviteExternalUserToGroupResp> inviteExternalUserToGroup(@NotNull @Valid final InviteExternalUserToGroupReq req) throws Exception;
} 