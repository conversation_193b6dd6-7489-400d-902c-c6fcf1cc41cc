package com.bj58.hy.wx.qywxbiz.contract.dto.rework;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2025/4/1 15:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class ReWorkPushReq {

    @NotEmpty
    @SCFMember(orderId = 1)
    private int scene;

    @NotEmpty
    @SCFMember(orderId = 2)
    private long userId;

    @SCFMember(orderId = 3)
    private String orderSkuName;

    @SCFMember(orderId = 4)
    private String orderServiceTime;


    @SCFMember(orderId = 5)
    private Long orderId;
}
