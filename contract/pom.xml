<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bj58.hy.wx</groupId>
        <artifactId>com.bj58.hy.wx.qywxbiz.parent</artifactId>
        <version>1.0.21</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>com.bj58.hy.wx.qywxbiz.contract</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.bj58.spat</groupId>
            <artifactId>com.bj58.spat.scf.serializer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bj58.spat</groupId>
            <artifactId>com.bj58.spat.scf.protocol</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-source</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>